"use client";

import React, { useState, useEffect, useMemo, useCallback } from "react";
import { Question, QuestionGroup } from "@/types/formBuilder";
import { useQuery, useMutation } from "@tanstack/react-query";
import { useParams } from "next/navigation";
import { decode } from "@/lib/encodeDecode";
import { fetchQuestions } from "@/lib/api/form-builder";
import { fetchQuestionGroups } from "@/lib/api/question-groups";
import { createAnswerSubmission, fetchProjectById } from "@/lib/api/projects";
import { Project } from "@/types";
import Spinner from "@/components/general/Spinner";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { ChevronDown, ChevronRight } from "lucide-react";
import { useDispatch } from "react-redux";
import { showNotification } from "@/redux/slices/notificationSlice";
import { TableInput } from "@/components/form-inputs/TableInput";
import debounce from "lodash/debounce";

export default function FormTestPage() {
  const dispatch = useDispatch();
  const { hashedId } = useParams();
  const hashedIdString = hashedId as string;
  const projectId = decode(hashedIdString);

  const [answers, setAnswers] = useState<Record<string, any>>({});
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [expandedGroups, setExpandedGroups] = useState<Record<number, boolean>>(
    {}
  );

  const {
    data: questionsData,
    isLoading,
    isError,
  } = useQuery<Question[]>({
    queryKey: ["questions", projectId],
    queryFn: () => fetchQuestions({ projectId: projectId! }),
    enabled: !!projectId,
  });

  const { data: questionGroups = [] } = useQuery<QuestionGroup[]>({
    queryKey: ["questionGroups", projectId],
    queryFn: () => fetchQuestionGroups({ projectId: projectId! }),
    enabled: !!projectId,
  });

  const { data: projectData } = useQuery<Project>({
    queryKey: ["project", projectId],
    queryFn: () => fetchProjectById({ projectId: projectId! }),
    enabled: !!projectId,
  });

  useEffect(() => {
    if (questionsData) {
      const initialAnswers: Record<string, any> = {};
      questionsData.forEach((question) => {
        initialAnswers[question.id] =
          question.inputType === "selectmany" ? [] : "";
      });
      setAnswers(initialAnswers);
    }
  }, [questionsData]);

  useEffect(() => {
    if (questionGroups.length > 0) {
      const initialExpandedState: Record<number, boolean> = {};
      questionGroups.forEach((group) => {
        initialExpandedState[group.id] = true;
      });
      setExpandedGroups(initialExpandedState);
    }
  }, [questionGroups.length]);

  const groupedQuestions = useMemo(() => {
    return questionGroups.reduce(
      (acc: Record<number, Question[]>, group: QuestionGroup) => {
        acc[group.id] =
          questionsData?.filter((q) => q.questionGroupId === group.id) || [];
        return acc;
      },
      {} as Record<number, Question[]>
    );
  }, [questionGroups, questionsData]);

  const ungroupedQuestions = useMemo(() => {
    return (
      questionsData?.filter(
        (q) => q.questionGroupId === null || q.questionGroupId === undefined
      ) || []
    );
  }, [questionsData]);

  const unifiedFormItems = useMemo(() => {
    const items: Array<{
      type: "group" | "question";
      data: QuestionGroup | Question;
      order: number;
      originalPosition?: number;
    }> = [];

    questionGroups.forEach((group: QuestionGroup) => {
      const groupQuestions =
        questionsData?.filter((q) => q.questionGroupId === group.id) || [];
      const minQuestionPosition =
        groupQuestions.length > 0
          ? Math.min(...groupQuestions.map((q) => q.position))
          : group.order;

      items.push({
        type: "group",
        data: group,
        order: minQuestionPosition,
        originalPosition: minQuestionPosition,
      });
    });

    ungroupedQuestions.forEach((question: Question) => {
      items.push({
        type: "question",
        data: question,
        order: question.position,
        originalPosition: question.position,
      });
    });

    return items.sort((a, b) => {
      if (a.order === b.order) {
        return (
          (a.originalPosition || a.order) - (b.originalPosition || b.order)
        );
      }
      return a.order - b.order;
    });
  }, [questionGroups, ungroupedQuestions, questionsData]);

  const toggleGroupExpansion = useCallback((groupId: number) => {
    setExpandedGroups((prev) => ({
      ...prev,
      [groupId]: !prev[groupId],
    }));
  }, []);

  const submitAnswersMutation = useMutation({
    mutationFn: async (answers: Record<string, any>) => {
      const formattedAnswers =
        questionsData?.map((question) => {
          const answerValue = answers[question.id];
          const isSelectMany = question.inputType === "selectmany";

          let questionOptionId: number | number[] | undefined;

          if (
            isSelectMany &&
            Array.isArray(answerValue) &&
            question.questionOptions
          ) {
            questionOptionId = answerValue
              .map((val: string) => {
                const option = question.questionOptions.find(
                  (opt) => opt.label === val
                );
                return option?.id;
              })
              .filter((id: number | undefined) => id !== undefined) as number[];
          } else if (
            question.inputType === "selectone" &&
            answerValue &&
            question.questionOptions
          ) {
            const option = question.questionOptions.find(
              (opt) => opt.label === answerValue
            );
            questionOptionId = option?.id;
          }

          let formattedValue: string | number | boolean | undefined;
          if (isSelectMany) {
            formattedValue =
              Array.isArray(answerValue) && answerValue.length > 0
                ? answerValue.join(", ")
                : undefined;
          } else if (question.inputType === "number") {
            formattedValue = answerValue ? Number(answerValue) : undefined;
          } else if (
            question.inputType === "date" ||
            question.inputType === "dateandtime"
          ) {
            formattedValue = answerValue || undefined;
          } else if (question.inputType === "table") {
            if (Array.isArray(answerValue) && answerValue.length > 0) {
              try {
                const validatedCellValues = answerValue.map((cell) => ({
                  columnId: Number(cell.columnId),
                  rowsId: Number(cell.rowsId),
                  value: String(cell.value || ""),
                }));
                formattedValue = JSON.stringify(validatedCellValues);
              } catch (err) {
                console.error("Error formatting table data:", err);
                formattedValue = undefined;
              }
            } else {
              formattedValue = undefined;
            }
          } else {
            formattedValue = answerValue ? String(answerValue) : undefined;
          }

          return {
            projectId: Number(projectId),
            questionId: question.id,
            answerType: String(question.inputType),
            value: formattedValue,
            questionOptionId,
            isOtherOption: false,
          };
        }) || [];

      return await createAnswerSubmission(formattedAnswers);
    },
    onSuccess: (data) => {
      dispatch(
        showNotification({
          message: "Form submitted successfully",
          type: "success",
        })
      );
      setAnswers({});
      window.dispatchEvent(new Event("form-submitted"));
      localStorage.setItem("form_submitted", Date.now().toString());
    },
    onError: (error: any) => {
      dispatch(
        showNotification({
          message: "Failed to submit form. Please try again.",
          type: "error",
        })
      );
      console.error("Submission Error:", error);
    },
    onSettled: () => {
      setIsSubmitting(false);
    },
  });

  const handleTableInputChange = useCallback(
    debounce(
      (questionId: number, cellValues: any) => {
        setAnswers((prev) => ({
          ...prev,
          [questionId]: cellValues,
        }));
        setErrors((prev) => ({
          ...prev,
          [questionId]: "",
        }));
      },
      500,
      { leading: false, trailing: true }
    ),
    []
  );

  const handleInputChange = useCallback(
    (questionId: number, value: any) => {
      if (
        questionsData?.find((q) => q.id === questionId)?.inputType === "table"
      ) {
        handleTableInputChange(questionId, value);
      } else {
        setAnswers((prev) => ({
          ...prev,
          [questionId]: value,
        }));
        setErrors((prev) => ({
          ...prev,
          [questionId]: "",
        }));
      }
    },
    [questionsData, handleTableInputChange]
  );

  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    let isValid = true;

    questionsData?.forEach((question) => {
      if (question.isRequired) {
        const value = answers[question.id];
        if (
          (typeof value === "string" && !value.trim()) ||
          (Array.isArray(value) && value.length === 0) ||
          value === undefined ||
          value === null
        ) {
          newErrors[question.id] = `${question.label} is required`;
          isValid = false;
        }
      }
    });

    setErrors(newErrors);
    return isValid;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!validateForm()) return;
    setIsSubmitting(true);
    submitAnswersMutation.mutate(answers);
  };

  const renderQuestionInput = (question: Question) => {
    const value =
      answers[question.id] ?? (question.inputType === "selectmany" ? [] : "");

    switch (question.inputType) {
      case "text":
        if (question.hint?.includes("multiline")) {
          return (
            <Textarea
              value={value}
              onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) =>
                handleInputChange(question.id, e.target.value)
              }
              placeholder={question.placeholder || "Your answer"}
              required={question.isRequired}
            />
          );
        }
        return (
          <input
            className="input-field w-full"
            value={value}
            onChange={(e) => handleInputChange(question.id, e.target.value)}
            placeholder={question.placeholder || "Your answer"}
            required={question.isRequired}
          />
        );

      case "number":
        return (
          <input
            className="input-field w-full"
            type="number"
            value={value}
            onChange={(e) => handleInputChange(question.id, e.target.value)}
            placeholder={question.placeholder || "Your answer"}
            required={question.isRequired}
          />
        );

      case "decimal":
        return (
          <input
            className="input-field w-full"
            type="decimal"
            value={value}
            onChange={(e) => handleInputChange(question.id, e.target.value)}
            placeholder={question.placeholder || "Your answer"}
            required={question.isRequired}
          />
        );

      case "selectone":
        return (
          <RadioGroup
            value={value}
            onValueChange={(val: string) => handleInputChange(question.id, val)}
            required={question.isRequired}
          >
            <div className="space-y-2">
              {question.questionOptions?.map((option, index) => (
                <div key={index} className="flex items-center space-x-2">
                  <RadioGroupItem
                    value={option.label}
                    id={`option-${option.id}`}
                  />
                  <Label
                    htmlFor={`option-${option.id}`}
                    className="cursor-pointer"
                  >
                    {option.label}
                  </Label>
                  {option.sublabel && (
                    <p className="text-sm text-neutral-700 ml-4">
                      {`(${option.sublabel})`}
                    </p>
                  )}
                </div>
              ))}
            </div>
          </RadioGroup>
        );

      case "selectmany":
        return (
          <div className="space-y-2">
            {question.questionOptions?.map((option) => (
              <div key={option.id} className="flex items-center space-x-2">
                <Checkbox
                  id={`option-${option.id}`}
                  checked={(value || []).includes(option.label)}
                  onCheckedChange={(checked) => {
                    const currentValues = value || [];
                    const newValues = checked
                      ? [...currentValues, option.label]
                      : currentValues.filter((v: string) => v !== option.label);
                    handleInputChange(question.id, newValues);
                  }}
                />
                <Label
                  htmlFor={`option-${option.id}`}
                  className="cursor-pointer"
                >
                  {option.label}
                </Label>
              </div>
            ))}
          </div>
        );

      case "date":
        return (
          <div className="relative">
            <input
              className="input-field w-full"
              type="date"
              value={value}
              onChange={(e) => handleInputChange(question.id, e.target.value)}
              placeholder={question.placeholder || "Select date"}
              required={question.isRequired}
            />
          </div>
        );

      case "dateandtime":
        return (
          <div className="relative">
            <input
              className="input-field w-full"
              type="time"
              value={value}
              onChange={(e) => handleInputChange(question.id, e.target.value)}
              placeholder={question.placeholder || "Select time"}
              required={question.isRequired}
            />
          </div>
        );

      case "table":
        return (
          <TableInput
            questionId={question.id}
            value={value}
            onChange={(cellValues) =>
              handleInputChange(question.id, cellValues)
            }
            required={question.isRequired}
            tableLabel={question.label}
          />
        );

      default:
        return null;
    }
  };

  const renderQuestion = (question: Question) => (
    <div
      key={question.id}
      className="border border-gray-200 dark:bg-gray-800 rounded-md p-4"
    >
      <div className="mb-2">
        <Label className="text-base font-medium">
          {question.label}
          {question.isRequired && <span className="text-red-500 ml-1">*</span>}
        </Label>
        {question.hint && (
          <p className="text-sm text-muted-foreground mt-1">{question.hint}</p>
        )}
        {errors[question.id] && (
          <p className="text-sm text-red-500 mt-1">{errors[question.id]}</p>
        )}
      </div>
      <div className="mt-2">{renderQuestionInput(question)}</div>
    </div>
  );

  if (isLoading) return <Spinner />;
  if (isError || !questionsData) {
    return (
      <p className="text-sm text-red-500">
        Error loading form. Please try again.
      </p>
    );
  }

  return (
    <div className="min-h-screen w-full bg-neutral-100 dark:bg-gray-900 flex flex-col items-center p-4 md:p-6">
      <div className="w-full max-w-screen-lg bg-neutral-100 dark:bg-gray-800 rounded-md shadow-sm border border-gray-200 dark:border-gray-700">
        <h2 className="text-2xl font-semibold p-6 border-b border-gray-200 dark:border-gray-700">
          {projectData?.name || "Test Form"}
        </h2>
        <form onSubmit={handleSubmit} className="p-6">
          <div className="space-y-6">
            {!questionsData || questionsData.length === 0 ? (
              <div className="text-center py-12">
                <p className="text-muted-foreground">
                  This form has no questions yet.
                </p>
              </div>
            ) : (
              unifiedFormItems.map((item) => {
                if (item.type === "group") {
                  const group = item.data as QuestionGroup;
                  const groupQuestions = groupedQuestions[group.id] || [];
                  const isExpanded = expandedGroups[group.id];

                  return (
                    <div
                      key={`group-${group.id}`}
                      className="border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800"
                    >
                      <div
                        className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700"
                        onClick={() => toggleGroupExpansion(group.id)}
                      >
                        <div className="flex items-center space-x-2">
                          {isExpanded ? (
                            <ChevronDown className="h-5 w-5 text-gray-500" />
                          ) : (
                            <ChevronRight className="h-5 w-5 text-gray-500" />
                          )}
                          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                            {group.title}
                          </h3>
                          <span className="text-sm text-gray-500 dark:text-gray-400">
                            ({groupQuestions.length} question
                            {groupQuestions.length !== 1 ? "s" : ""})
                          </span>
                        </div>
                      </div>
                      {isExpanded && (
                        <div className="p-4 space-y-4">
                          {groupQuestions.length > 0 ? (
                            groupQuestions.map((question) =>
                              renderQuestion(question)
                            )
                          ) : (
                            <div className="text-center py-4 text-gray-500 dark:text-gray-400">
                              No questions in this group.
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  );
                } else {
                  const question = item.data as Question;
                  return renderQuestion(question);
                }
              })
            )}
            {questionsData && questionsData.length > 0 && (
              <div className="mt-6 flex justify-end">
                <button
                  className="btn-primary"
                  type="submit"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? "Submitting..." : "Submit Form"}
                </button>
              </div>
            )}
          </div>
        </form>
      </div>
    </div>
  );
}

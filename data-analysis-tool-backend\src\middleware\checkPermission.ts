import { NextFunction, Request, Response } from "express";
import { hasPermission } from "../utils/permissionChecker";
import { prisma } from "../utils/prisma";

interface UserRequest extends Request {
  user?: {
    id: number;
  };
}

export const checkPermission = (actionKeys: string | string[]) => {
  return async (req: UserRequest, res: Response, next: NextFunction) => {
    try {
      if (!req.user)
        return res.status(400).json({ message: "User ID not found" });

      const userId = Number(req.user.id);
      const projectIdStr =
        req.params.projectId || (req.query.projectId as string);
      const projectId = parseInt(projectIdStr);

      if (isNaN(projectId))
        return res.status(400).json({ message: "Invalid project ID" });

      const project = await prisma.project.findUnique({
        where: { id: projectId },
        select: { id: true, userId: true },
      });

      if (!project)
        return res.status(404).json({ message: "Project not found" });

      if (project.userId === userId) return next();

      const projectUser = await prisma.projectUser.findUnique({
        where: { userId_projectId: { userId, projectId } },
      });

      if (!projectUser)
        return res.status(403).json({ message: "No access to project" });

      const keys = Array.isArray(actionKeys) ? actionKeys : [actionKeys];
      const allowed = keys.some((key) =>
        hasPermission(projectUser.permission, key)
      );

      if (!allowed)
        return res.status(403).json({ message: "Permission denied" });

      next();
    } catch (error) {
      console.error("checkPermission error:", error);
      res.status(500).json({ message: "Internal server error" });
    }
  };
};

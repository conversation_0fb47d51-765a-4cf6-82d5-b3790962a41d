"use client";

import { Session } from "@/types";
import { ColumnDef } from "@tanstack/react-table";

export const columns: ColumnDef<Session>[] = [
  {
    accessorKey: "deviceInfo",
    header: "Device",
  },
  {
    accessorKey: "browserInfo",
    header: "Browser",
  },
  {
    accessorKey: "updatedAt",
    header: "Last Activity",
  },

  {
    accessorKey: "ipAddress",
    header: "IP Address",
  },
  {
    accessorKey: "isActive",
    header: "Status",
    cell: ({ getValue }) => {
      const value = getValue() as boolean;
      return value ? "Active" : "Inactive";
    },
  },
];

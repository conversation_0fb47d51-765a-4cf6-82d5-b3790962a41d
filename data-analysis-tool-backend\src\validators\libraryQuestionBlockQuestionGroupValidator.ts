// validators/questionGroupValidator.ts
import { z } from "zod";

// Define the QuestionGroup validation schema
export const libraryQuestionBlockQuestionGroupSchema = z.object({
  title: z.string().min(1, "Title is required"), // Ensure the title is a non-empty string
  order: z.number().int().min(1, "Order must be a positive integer"), // Ensure order is a positive integer
  parentGroupId: z.number().int().optional(), // Optional parent group ID, if any
  // Project ID validation
  selectedQuestionIds: z
    .array(
      z
        .number()
        .int()
        .positive("Selected question IDs must be positive integers")
    )
    .nonempty("At least one question must be selected"),
  createdAt: z.date().optional(), // Optional createdAt field
  updatedAt: z.date().optional(), // Optional updatedAt field
  subGroups: z
    .array(
      // Validate subGroups (if present)
      z.object({
        id: z.number().int(), // Assuming each subGroup has an ID
        title: z.string().min(1, "SubGroup title is required"), // Title for subGroups
        order: z
          .number()
          .int()
          .min(1, "Order for subGroup must be a positive integer"), // Order for subGroups
      })
    )
    .optional(), // SubGroups are optional
});

// For updating a QuestionGroup (without requiring title)
export const updateLibraryQuestionBlockQuestionGroupSchema = z.object({
  id: z.number().int().min(1, "ID is required"),
  title: z.string().min(1, "Title is required"),
  order: z.number().int().min(1, "Order must be a positive integer"),
  parentGroupId: z.number().int().optional(),
  selectedQuestionIds: z
    .array(
      z
        .number()
        .int()
        .positive("Selected question IDs must be positive integers")
    )
    .optional(), // optional because maybe you just update title/order
});

export const updateLibraryQuestionBlockSubGroupOrderAndAddQuestionSchema =
  z.object({
    subGroups: z
      .array(
        z.object({
          id: z.number().int().min(1, "SubGroup ID is required"),
          order: z.number().int().min(1, "Order must be a positive integer"),
        })
      )
      .nonempty("subGroups cannot be empty"),

    newQuestions: z
      .array(
        z.object({
          subGroupId: z.number().int().min(1, "SubGroup ID is required"),
          title: z.string().min(1, "Question title is required"),
          order: z.number().int().min(1, "Order must be a positive integer"),
          projectId: z.number().int().min(1, "Project ID is required"),
        })
      )
      .optional(),
  });

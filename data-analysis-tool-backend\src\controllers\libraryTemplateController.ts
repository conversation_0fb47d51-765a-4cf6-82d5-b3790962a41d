import { Request, Response } from "express";
import { ApiResponse } from "../utils/ApiResponse";
import {
  deleteMultipleLibraryTemplateSchema,
  LibraryTemplateSchema,
} from "../validators/libraryTemplateValidators";
import libraryTemplateRepository from "../repositories/libraryTemplateRepository";
import { Status } from "@prisma/client";

interface userRequest extends Request {
  user?: {
    id: number;
  };
}

export const createLibraryTemplate = async (
  req: userRequest,
  res: Response
): Promise<void> => {
  try {
    const userId = req.user!.id;
    const result = LibraryTemplateSchema.safeParse(req.body);

    if (!result.success) {
      res.status(400).json({
        success: false,
        message: result.error.flatten().fieldErrors,
      });
      return;
    }

    const { name, description, sector, country } = result.data;

    const existingLibraryTemplate = await libraryTemplateRepository.findByName(
      name,
      userId
    );
    if (existingLibraryTemplate) {
      res
        .status(400)
        .json({ success: false, message: "library template already exists" });
      return;
    }

    const libraryTemplate = await libraryTemplateRepository.create({
      name,
      description,
      sector,
      userId,
      country,
    });

    res.status(201).json({
      message: "Library template created successfully",
      libraryTemplate,
    });
    return;
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: "error creating library template",
      error: error.message,
    });
    return;
  }
};

export const getAllLibraryTemplates = async (
  req: userRequest,
  res: Response
): Promise<void> => {
  try {
    const id = Number(req.user!.id);
    if (!id) {
      res.status(404).json({
        success: false,
        message: "user id not found",
      });
      return;
    }

    const templates = await libraryTemplateRepository.findAll(id);

    res.status(200).json({
      message: "Successfully fetched all library templates",
      templates,
    });

    return;
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: "error getting library templates",
      error: error.message,
    });
    return;
  }
};

export const updateLibraryTemplate = async (
  req: userRequest,
  res: Response
): Promise<void> => {
  const id = Number(req.params.id);
  try {
    const userId = req.user!.id;
    const libraryTemplateUser =
      await libraryTemplateRepository.findLibraryTemplateByIdAndUser(
        id,
        userId
      );

    if (!libraryTemplateUser) {
      res.status(404).json({
        success: false,
        message: "no library template found",
      });

      return;
    }

    const result = LibraryTemplateSchema.safeParse(req.body);
    if (!result.success) {
      res.status(400).json({
        success: false,
        message: "Validation failed",
        error: result.error.flatten(),
      });
      return;
    }

    const updatedData = result.data;

    const updatedLibraryTemplate = await libraryTemplateRepository.updateById(
      id,
      updatedData
    );
    res
      .status(200)
      .json(
        new ApiResponse(
          200,
          { updatedLibraryTemplate },
          "Library template updated successfully"
        )
      );
    return;
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: "error updating library template",
      error: error.message,
    });
    return;
  }
};

export const deleteLibraryTemplate = async (
  req: userRequest,
  res: Response
): Promise<void> => {
  const id = Number(req.params.id);
  try {
    const userId = req.user!.id;
    const libraryTemplate =
      await libraryTemplateRepository.findLibraryTemplateByIdAndUser(
        id,
        userId
      );
    if (!libraryTemplate) {
      res.status(404).json({
        success: false,
        message: "no library template found with given id",
      });
      return;
    }

    await libraryTemplateRepository.deleteLibraryTemplate(id);

    res
      .status(200)
      .json(new ApiResponse(200, {}, "library template deleted successfully"));
    return;
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: "error deleting library template",
      error: error.message,
    });

    return;
  }
};

export const getLibraryTemplateById = async (
  req: userRequest,
  res: Response
): Promise<void> => {
  const id = Number(req.params.id);
  try {
    const userId = req.user!.id;
    const template =
      await libraryTemplateRepository.findLibraryTemplateByIdAndUser(
        id,
        userId
      );
    if (!template) {
      res.status(404).json({
        success: false,
        message: "library template not found",
      });
      return;
    }

    res.status(200).json({
      message: "Successfully fetched library template",
      template,
    });
    return;
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: "error getting library template by id",
      error: error.message,
    });
    return;
  }
};

export const fetchQuestionForLibraryTemplate = async (
  req: Request,
  res: Response
) => {
  try {
    const id = Number(req.params.id);
    if (!id) {
      return res.status(400).json({
        message: "id not found",
      });
    }

    const library =
      await libraryTemplateRepository.findLibraryWithQuestionGroupAndQuestion(
        id
      );

    return res.status(200).json({
      success: true,
      message: "library question fetched success",
      data: { library },
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: "error getting library template questions",
      error: error.message,
    });
    return;
  }
};

export const DeleteMultipleLibraryTemplate = async (
  req: Request,
  res: Response
) => {
  try {
    const result = deleteMultipleLibraryTemplateSchema.safeParse(req.body);

    if (!result.success) {
      return res.status(400).json({
        success: false,
        message: "Validation failed",
        error: result.error.flatten(),
      });
    }

    const updatedData = result.data;

    const deleted =
      await libraryTemplateRepository.deleteMultipleLibraryTemplate(
        updatedData.templateIds
      );

    return res.status(200).json({
      success: false,
      message: "library template deleted success",
    });
  } catch (error: unknown) {
    res.status(500).json({
      success: false,
      message: "error deleting libray template",
      error: error instanceof Error ? error.message : "unexpected error",
    });
    return;
  }
};

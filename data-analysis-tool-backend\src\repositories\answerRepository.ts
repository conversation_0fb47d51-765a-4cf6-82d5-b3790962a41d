import { Answer, Prisma } from "@prisma/client";
import { prisma } from "../utils/prisma";
import { CreateAnswerInput } from "../validators/answerValidator";

class AnswerRepository {
  async createAnswer(data: CreateAnswerInput) {
    return await prisma.answer.create({
      data: {
        formSubmissionId: data.submissionId,
        questionId: data.questionId,
        questionOptionId: data.questionOptionId,
        isOtherOption: data.isOtherOption,
        value: data.value,
        answerType: data.answerType,
        imageUrl: data.imageUrl,
      },
    });
  }

  async getAnswersBySubmission(formSubmissionId: number) {
    return await prisma.answer.findMany({
      where: { formSubmissionId },
      include: {
        question: true,
      },
    });
  }

  async getAnswerById(id: number) {
    return await prisma.answer.findUnique({
      where: { id },
      include: {
        question: true,
      },
    });
  }

  // answerRepository.ts
  async deleteAnswersBySubmissionIdAndQuestionId(
    submissionId: number,
    questionId: number,
    answerType: string
  ) {
    if (answerType === "selectmany") {
      return await prisma.answer.deleteMany({
        where: {
          formSubmissionId: submissionId,
          questionId: questionId,
        },
      });
    } else {
      const answer = await prisma.answer.findFirst({
        where: {
          formSubmissionId: submissionId,
          questionId: questionId,
        },
      });

      if (!answer) return null;
      return await prisma.answer.delete({
        where: {
          id: answer.id,
        },
      });
    }
  }

  async findBySubmissionIdQuestion(
    formSubmissionId: number,
    questionId?: number
  ) {
    return await prisma.answer.findFirst({
      where: {
        formSubmissionId: formSubmissionId,
        questionId: questionId,
      },
    });
  }

  async updateSelectManyAnswers(
    submissionId: number,
    questionId: number,
    values: string[],
    questionOptionIds: number[]
  ) {
    // Delete previous answers
    await prisma.answer.deleteMany({
      where: {
        formSubmissionId: submissionId,
        questionId,
        answerType: "selectmany",
      },
    });

    // Insert new answers
    const createdAnswers = [];

    for (let i = 0; i < values.length; i++) {
      const val = values[i];
      const optionId = questionOptionIds[i] ?? null;

      const answer = await prisma.answer.create({
        data: {
          formSubmissionId: submissionId,
          questionId,
          questionOptionId: optionId,
          value: String(val),
          answerType: "selectmany",
        },
      });

      createdAnswers.push(answer);
    }

    return createdAnswers;
  }

  async findDuplicateAnswer(
    submissionId: number,
    questionId: number,
    answerType: string
  ) {
    if (answerType !== "selectmany") {
      return await prisma.answer.findFirst({
        where: {
          formSubmissionId: submissionId,
          questionId: questionId,
          answerType: { not: "selectmany" },
        },
      });
    }
  }

  async updateSingleAnswer(data: CreateAnswerInput) {
    const existing = await prisma.answer.findFirst({
      where: {
        formSubmissionId: data.submissionId,
        questionId: data.questionId,
        answerType: { not: "selectmany" },
      },
    });

    if (existing) {
      return await prisma.answer.update({
        where: { id: existing.id },
        data: {
          value: String(data.value),
          questionOptionId: data.questionOptionId,
          isOtherOption: data.isOtherOption,
          imageUrl: data.imageUrl,
          answerType: data.answerType,
        },
      });
    } else {
      return await prisma.answer.create({
        data: {
          formSubmissionId: data.submissionId,
          questionId: data.questionId,
          questionOptionId: data.questionOptionId,
          isOtherOption: data.isOtherOption,
          value: String(data.value),
          answerType: data.answerType,
          imageUrl: data.imageUrl,
        },
      });
    }
  }

  async findAllAnswers(projectId: number) {
    return await prisma.answer.findMany({
      where: {
        formSubmission: {
          projectId: projectId,
        },
      },
      include: {
        question: true,
        questionOption: true,
        formSubmission: {
          include: {
            user: true,
          },
        },
      },
    });
  }

  async AddMultipleAnswer(
    answers: {
      formSubmissionId?: number;
      questionId?: number;
      value: any;
      answerType?: string;
      imageUrl?: string;
      questionOptionId?: number;
      isOtherOption?: boolean;
    }[]
  ) {
    return await prisma.$transaction(async (tx) => {
      const createdAnswers = [];

      for (const answer of answers) {
        try {
          // Handle table data type specifically
          if (answer.answerType === 'table') {
            // Ensure the value is a string
            const tableValue = typeof answer.value === 'string' 
              ? answer.value 
              : String(answer.value || '');
              
            // Create the answer with table data
            const created = await tx.answer.create({
              data: {
                formSubmissionId: answer.formSubmissionId,
                questionId: answer.questionId,
                value: tableValue,
                answerType: answer.answerType,
                imageUrl: answer.imageUrl,
                // Don't include questionOptionId for table type
                isOtherOption: answer.isOtherOption ?? false,
              },
            });
            createdAnswers.push(created);
          } else {
            // Handle other answer types
            const created = await tx.answer.create({
              data: {
                formSubmissionId: answer.formSubmissionId,
                questionId: answer.questionId,
                value: answer.value,
                answerType: answer.answerType,
                imageUrl: answer.imageUrl,
                questionOptionId: answer.questionOptionId,
                isOtherOption: answer.isOtherOption ?? false,
              },
            });
            createdAnswers.push(created);
          }
        } catch (error) {
          console.error('Error creating answer:', error);
          console.error('Problematic answer data:', JSON.stringify(answer));
          throw error; // Re-throw to trigger transaction rollback
        }
      }

      return createdAnswers;
    });
  }
  async UpdateMultipleAnswers(
    answers: {
      id?: number;
      value?: any;
      answerType: string;
      imageUrl?: string | null;
      questionOptionId?: number | number[] | null;
      isOtherOption?: boolean;
      formSubmissionId: number;
      projectId: number;
    }[]
  ) {
    return await prisma.$transaction(async (tx) => {
      const updatedAnswers = [];

      for (const answer of answers) {
        const existingAnswer = await tx.answer.findUnique({
          where: { id: answer.id },
        });

        if (!existingAnswer) {
          throw new Error(`Answer with id ${answer.id} not found`);
        }

        // SELECTMANY
        if (answer.answerType === "selectmany") {
          if (!Array.isArray(answer.questionOptionId)) {
            throw new Error(
              "Expected array of questionOptionIds for selectmany"
            );
          }

          // Find all existing answers for this question (based on answer group, adjust this if needed)
          const existingGroupAnswers = await tx.answer.findMany({
            where: {
              // you can use questionId or submissionId if available
              questionId: existingAnswer.questionId,
              answerType: "selectmany",
              formSubmissionId: existingAnswer.formSubmissionId,
            },
          });

          const existingOptionIds = existingGroupAnswers
            .map((a) => a.questionOptionId)
            .sort();
          const newOptionIds = [...answer.questionOptionId].sort();

          const isSame =
            existingOptionIds.length === newOptionIds.length &&
            existingOptionIds.every((id, idx) => id === newOptionIds[idx]);

          if (!isSame) {
            // Delete old group
            await tx.answer.deleteMany({
              where: {
                questionId: existingAnswer.questionId,
                answerType: "selectmany",
                formSubmissionId: existingAnswer.formSubmissionId,
              },
            });

            console.log("existansdlfkjsldfs", existingAnswer.formSubmissionId);
            const values = Array.isArray(answer.value)
              ? answer.value
              : typeof answer.value === "string"
              ? answer.value.split(",").map((v) => v.trim())
              : [];

            for (let i = 0; i < answer.questionOptionId.length; i++) {
              const created = await tx.answer.create({
                data: {
                  questionId: existingAnswer.questionId,
                  value: values[i] ?? "",
                  answerType: "selectmany",
                  imageUrl: answer.imageUrl,
                  questionOptionId: answer.questionOptionId[i],
                  isOtherOption: answer.isOtherOption ?? false,
                  formSubmissionId: answer.formSubmissionId,
                },
              });
              updatedAnswers.push(created);
            }
          } else {
            // If same, keep old answers
            updatedAnswers.push(...existingGroupAnswers);
          }
        }

        // SELECTONE
        else if (answer.answerType === "selectone") {
          if (existingAnswer.questionOptionId !== answer.questionOptionId) {
            // Delete old
            await tx.answer.delete({
              where: {
                id: existingAnswer.id,
                formSubmissionId: existingAnswer.formSubmissionId,
              },
            });

            const created = await tx.answer.create({
              data: {
                questionId: existingAnswer.questionId,
                value: String(answer.value ?? ""),
                answerType: "selectone",
                imageUrl: answer.imageUrl,
                questionOptionId: answer.questionOptionId as number,
                isOtherOption: answer.isOtherOption ?? false,
                formSubmissionId: answer.formSubmissionId,
              },
            });

            updatedAnswers.push(created);
          } else {
            // Just update other fields (value/imageUrl/etc.)
            const updated = await tx.answer.update({
              where: { id: existingAnswer.id },
              data: {
                value: String(answer.value ?? ""),
                imageUrl: answer.imageUrl,
                isOtherOption: answer.isOtherOption,
              },
            });
            updatedAnswers.push(updated);
          }
        }

        // TABLE TYPE
        else if (answer.answerType === "table") {
          // Ensure the value is a string
          const tableValue = typeof answer.value === 'string' 
            ? answer.value 
            : String(answer.value || '');
            
          try {
            // Validate that it's valid JSON before saving
            if (tableValue) {
              JSON.parse(tableValue);
            }
            
            // Update the table data
            const updated = await tx.answer.update({
              where: { id: answer.id },
              data: {
                value: tableValue,
                imageUrl: answer.imageUrl,
                isOtherOption: answer.isOtherOption,
                // Don't include questionOptionId for table type
              },
            });
            updatedAnswers.push(updated);
          } catch (err) {
            console.error("Error updating table data:", err);
            // Update with empty value if JSON parsing fails
            const updated = await tx.answer.update({
              where: { id: answer.id },
              data: {
                value: "",
                imageUrl: answer.imageUrl,
                isOtherOption: answer.isOtherOption,
              },
            });
            updatedAnswers.push(updated);
          }
        }
        // OTHER TYPES
        else {
          const updated = await tx.answer.update({
            where: { id: answer.id },
            data: {
              value: String(answer.value ?? ""),
              answerType: answer.answerType,
              imageUrl: answer.imageUrl,
              questionOptionId: Array.isArray(answer.questionOptionId)
                ? undefined
                : answer.questionOptionId,
              isOtherOption: answer.isOtherOption,
            },
          });

          updatedAnswers.push(updated);
        }
      }

      return updatedAnswers;
    });
  }
}

export default new AnswerRepository();

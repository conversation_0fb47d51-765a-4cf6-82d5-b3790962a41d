import { prisma } from "../utils/prisma";
import {
  LibraryTemplateQuestionGroup,
  Prisma,
  QuestionGroup,
} from "@prisma/client";

//  libraryTemplateQuestionGroupRepository

class libraryTemplateQuestionGroupRepository {
  async create(data: {
    title: string;
    order: number;
    libraryTemplateId: number;
    parentGroupId?: number;
    selectedQuestionIds?: number[];
  }): Promise<LibraryTemplateQuestionGroup> {
    const newGroup = await prisma.libraryTemplateQuestionGroup.create({
      data: {
        title: data.title,
        order: data.order,
        libraryTemplateId: data.libraryTemplateId,
        parentGroupId: data.parentGroupId,
      },
    });

    // Step 2: If there are selected question IDs, update those questions to associate them with the new group
    if (data.selectedQuestionIds && data.selectedQuestionIds.length > 0) {
      const LibraryQuestion = await prisma.libraryQuestion.updateMany({
        where: {
          id: {
            in: data.selectedQuestionIds, // Array of selected question IDs
          },
        },
        data: {
          libraryTemplateQuestionGroupId: newGroup.id, // Set the new questionGroupId to associate with the new group
        },
      });

      console.log("updated questions", LibraryQuestion);
    }

    return newGroup;
  }

  async delete(id: number): Promise<LibraryTemplateQuestionGroup> {
    return await prisma.libraryTemplateQuestionGroup.delete({
      where: { id },
    });
  }

  async deleteManyQuestionByGroup(id: number): Promise<Prisma.BatchPayload> {
    return await prisma.libraryQuestion.deleteMany({
      where: {
        libraryTemplateQuestionGroupId: id,
      },
    });
  }

  async findById(id: number): Promise<LibraryTemplateQuestionGroup | null> {
    return await prisma.libraryTemplateQuestionGroup.findUnique({
      where: { id },
      include: { libraryQuestions: true },
    });
  }

  async findAllByLibraryTemplate(
    libraryTemplateId: number
  ): Promise<LibraryTemplateQuestionGroup[]> {
    return await prisma.libraryTemplateQuestionGroup.findMany({
      where: { libraryTemplateId },
      select: {
        id: true,
        title: true,
        order: true,
        parentGroupId: true,
        libraryTemplateId: true,
        createdAt: true,
        updatedAt: true,
        libraryQuestions: true,
        subGroups: true,
      },
      orderBy: {
        order: "asc",
      },
    });
  }

  async update(
    id: number,
    updates: any
  ): Promise<LibraryTemplateQuestionGroup> {
    return await prisma.libraryTemplateQuestionGroup.update({
      where: { id },
      data: updates,
    });
  }

  async updateGroupInsideParentGroup(
    childGroupId: number,
    ParentGroupId: number
  ): Promise<LibraryTemplateQuestionGroup | null> {
    return await prisma.libraryTemplateQuestionGroup.update({
      where: {
        id: childGroupId,
      },
      data: {
        parentGroupId: ParentGroupId,
      },
    });
  }
  async RemoveGroupFromParentGroup(
    groupId: number
  ): Promise<LibraryTemplateQuestionGroup | null> {
    return prisma.libraryTemplateQuestionGroup.update({
      where: {
        id: groupId,
      },
      data: {
        parentGroupId: null,
      },
    });
  }
}

export default new libraryTemplateQuestionGroupRepository();

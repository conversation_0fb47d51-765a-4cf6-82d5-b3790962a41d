"use client";

import { PanelsTopLeft, Settings, FilePen, Database } from "lucide-react";
import { useParams } from "next/navigation";
import { Navbar } from "@/components/general/SecondaryNavbar";
import { ProjectPermissionFlags } from "@/types";

const ProjectNavbar = ({
  permissions,
}: {
  permissions: ProjectPermissionFlags;
}) => {
  const { hashedId } = useParams();
  const hashedIdString = hashedId as string;

  const isOwnerOrManager = permissions.manageProject;

  const canAccessFormBuilder =
    isOwnerOrManager || permissions.viewForm || permissions.editForm;
  const canAccessData =
    isOwnerOrManager ||
    permissions.viewSubmissions ||
    permissions.editSubmissions ||
    permissions.addSubmissions ||
    permissions.deleteSubmissions;
  const canAccessSettings = isOwnerOrManager;

  const projectNavbarItems = [
    {
      label: "Overview",
      icon: <PanelsTopLeft size={16} />,
      route: `/project/${hashedIdString}/overview`,
      disabled: false,
    },
    {
      label: "Form Builder",
      icon: <FilePen size={16} />,
      route: `/project/${hashedIdString}/form-builder`,
      disabled: !canAccessFormBuilder,
    },
    {
      label: "Data",
      icon: <Database size={16} />,
      route: `/project/${hashedIdString}/data`,
      disabled: !canAccessData,
    },
    {
      label: "Settings",
      icon: <Settings size={16} />,
      route: `/project/${hashedIdString}/settings`,
      disabled: !canAccessSettings,
    },
  ];

  return <Navbar items={projectNavbarItems} />;
};

export { ProjectNavbar };

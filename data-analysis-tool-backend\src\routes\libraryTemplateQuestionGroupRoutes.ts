import express from "express";

import {
  createLibraryTemplateQuestionGroup,
  updateLibraryTemplateQuestionGroup,
  deleteLibraryTemplateQuestionGroup,
  findAllLibraryTemplateGroupByLibrarytemplate,
  removeLibraryTemplateQuestionIdFromGroup,
  updateLibraryTemplateQuestionFromOneGroupToAnother,
  updateOneLibraryTemplateGroupInsideAnotherGroup,
  removeLibraryTemplateGroupFromParentGroup,
  deleteLibraryTemplateQuestionAndGroup,
} from "../controllers/libraryTemplateQuestionGroupController";
const router = express.Router();

router.get(
  "/",
  findAllLibraryTemplateGroupByLibrarytemplate as unknown as express.RequestHandler
);
router.post(
  "/",
  createLibraryTemplateQuestionGroup as unknown as express.RequestHandler
);
router.patch(
  "/",
  updateLibraryTemplateQuestionGroup as unknown as express.RequestHandler
);
router.delete(
  "/",
  deleteLibraryTemplateQuestionGroup as unknown as express.RequestHandler
);
router.delete(
  "/group/question",
  deleteLibraryTemplateQuestionAndGroup as unknown as express.RequestHandler
);
router.patch(
  "/question/remove",
  removeLibraryTemplateQuestionIdFromGroup as unknown as express.RequestHandler
);
router.patch(
  "/question/move",
  updateLibraryTemplateQuestionFromOneGroupToAnother as unknown as express.RequestHandler
);
router.patch(
  "/group/add",
  updateOneLibraryTemplateGroupInsideAnotherGroup as unknown as express.RequestHandler
);
router.patch(
  "/group/remove",
  removeLibraryTemplateGroupFromParentGroup as unknown as express.RequestHandler
);

export default router;

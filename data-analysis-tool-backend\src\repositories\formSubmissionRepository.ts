import { FormSubmission, Prisma } from "@prisma/client";
import { prisma } from "../utils/prisma";

class FormSubmissionRepository {
  // Create a new form submission

  async create(
    formData: {
      projectId: number;
      status?: string;
      location?: string;
      metadata?: Record<string, any>;
    },
    userId: number,
    deviceInfo?: string,
    location?: string
  ): Promise<FormSubmission> {
    const { projectId, status, metadata } = formData;

    return await prisma.formSubmission.create({
      data: {
        projectId,
        userId,
        status,
        deviceInfo,
        location,
        metadata,
        startedAt: new Date(Date.now()),
      },
    });
  }
  // Get a specific form submission with its answers
  async findById(id: number) {
    return await prisma.formSubmission.findUnique({
      where: { id },
      include: {
        answers: true,
        project: {
          include: {
            questions: {
              include: {
                questionOptions: true,
                questionConditions: true,
              },
            },
          },
        },
      },
    });
  }

  // Get all submissions for a project
  async findByProjectId(projectId: number) {
    return await prisma.formSubmission.findMany({
      where: { projectId },
      include: {
        user: true,
        answers: {
          include: {
            question: true,
            questionOption: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });
  }

  // Update a form submission
  async update(
    id: number,
    data: {
      status?: string;
      completedAt?: Date;
      durationSeconds?: number;
      metadata?: Record<string, any>;
      submittedAt?: Date;
    }
  ): Promise<FormSubmission> {
    const { status, completedAt, durationSeconds, metadata, submittedAt } =
      data;
    return await prisma.formSubmission.update({
      where: { id },
      data: {
        status,
        completedAt,
        durationSeconds,
        metadata,
        submittedAt,
      },
    });
  }

  // Delete a form submission
  async delete(id: number): Promise<void> {
    await prisma.formSubmission.delete({
      where: { id },
    });
  }

  // Check if user has permission to access a project
  async isProjectAccessible(
    userId: number,
    projectId: number
  ): Promise<boolean> {
    const project = await prisma.project.findFirst({
      where: {
        id: projectId,
        OR: [
          { userId: userId },
          {
            projectUser: {
              some: {
                userId: userId,
              },
            },
          },
        ],
      },
    });

    return !!project;
  }

  async changeLoginRequired(id: number): Promise<FormSubmission> {
    return await prisma.formSubmission.update({
      where: { id },
      data: {
        loginRequired: false,
      },
    });
  }

  async deleteMultipleSubmission(ids: number[]): Promise<Prisma.BatchPayload> {
    return await prisma.formSubmission.deleteMany({
      where: {
        id: {
          in: ids,
        },
      },
    });
  }
}

export default new FormSubmissionRepository();

import { Metadata } from "next";
import "./globals.css";
import { <PERSON><PERSON><PERSON> } from "next/font/google";
import { ReduxProvider } from "@/providers/ReduxProvider";
import { Notification } from "@/components/general/Notification";
import { ReactQueryProvider } from "@/providers/ReactQueryProvider";

const poppins = Poppins({
  weight: ["300", "400", "500", "600"],
  subsets: ["latin"],
  display: "swap",
});

export const metadata: Metadata = {
  title: "Data analysis tool",
  description: "A tool for data collection and analysis.",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={`${poppins.className} antialiased`}>
        <ReduxProvider>
          <ReactQueryProvider>
            <Notification />
            <main className="bg-neutral-200">{children}</main>
          </ReactQueryProvider>
        </ReduxProvider>
      </body>
    </html>
  );
}

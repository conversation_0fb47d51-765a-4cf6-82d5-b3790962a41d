"use client";

import React, { useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import { fetchQuestionBlockQuestions } from "@/lib/api/form-builder";
import { Question } from "@/types/formBuilder";
import { Search, X } from "lucide-react";
import Spinner from "../general/Spinner";

interface LibraryQuestionsSidebarProps {
  isOpen: boolean;
  onClose: () => void;
  onAddQuestions: (questions: Question[]) => void;
}

const LibraryQuestionsSidebar: React.FC<LibraryQuestionsSidebarProps> = ({
  isOpen,
  onClose,
  onAddQuestions,
}) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedQuestions, setSelectedQuestions] = useState<Question[]>([]);
  const [expandDetails, setExpandDetails] = useState(true);

  // Fetch question block questions
  const {
    data: libraryQuestions,
    isLoading,
    isError,
  } = useQuery<Question[]>({
    queryKey: ["libraryQuestions"],
    queryFn: () => fetchQuestionBlockQuestions(),
    enabled: isOpen, // Only fetch when sidebar is open
  });

  // Filter questions based on search term
  const filteredQuestions = libraryQuestions
    ? libraryQuestions.filter((question) =>
        question.label.toLowerCase().includes(searchTerm.toLowerCase())
      )
    : [];

  // Handle question selection
  const toggleQuestionSelection = (question: Question) => {
    if (selectedQuestions.some((q) => q.id === question.id)) {
      setSelectedQuestions(selectedQuestions.filter((q) => q.id !== question.id));
    } else {
      setSelectedQuestions([...selectedQuestions, question]);
    }
  };

  // Add selected questions to the form
  const handleAddQuestions = () => {
    onAddQuestions(selectedQuestions);
    setSelectedQuestions([]);
    onClose();
  };

  // Reset selections when sidebar closes
  useEffect(() => {
    if (!isOpen) {
      setSelectedQuestions([]);
      setSearchTerm("");
    }
  }, [isOpen]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex justify-end">
      {/* Backdrop */}
      <div
        className="absolute inset-0 bg-neutral-900/50"
        onClick={onClose}
      ></div>

      {/* Sidebar */}
      <div className="relative w-full max-w-md bg-neutral-50 h-full overflow-auto shadow-xl">
        <div className="p-4 border-b border-neutral-200">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-bold">Search Library</h2>
            <button
              onClick={onClose}
              className="self-end cursor-pointer text-neutral-700 hover:text-neutral-900 active:scale-90 transition-all duration-300"
            >
              <X size={20} />
            </button>
          </div>

          {/* Search input */}
          <div className="relative mb-4">
            <input
              type="text"
              placeholder="Search..."
              className="input-field w-full p-2 pl-10"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
            <Search className="absolute left-3 top-2.5 " size={18} />
          </div>

          {/* Tags filter (placeholder) */}
          {/* <div className="mb-4">
            <button className="w-full p-2 text-left border border-gray-300 rounded-md flex justify-between items-center">
              <span className="text-gray-500">Search Tags</span>
              <span>▼</span>
            </button>
          </div> */}

          {/* Collection filter (placeholder) */}
          {/* <div className="mb-4">
            <button className="w-full p-2 text-left border border-gray-300 rounded-md flex justify-between items-center">
              <span className="text-gray-500">Select Collection Name</span>
              <span>▼</span>
            </button>
          </div> */}

          {/* Results count and expand toggle */}
          <div className="flex justify-between items-center mb-4">
            <span>
              {filteredQuestions.length} asset{filteredQuestions.length !== 1 ? "s" : ""} found
            </span>
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={expandDetails}
                onChange={() => setExpandDetails(!expandDetails)}
                className="mr-2"
              />
              expand details
            </label>
          </div>
        </div>

        {/* Question list */}
        <div className="p-4">
          {isLoading ? (
            <div className="flex justify-center p-8">
              <Spinner />
            </div>
          ) : isError ? (
            <div className="text-red-500 p-4 text-center">
              Error loading library questions
            </div>
          ) : filteredQuestions.length === 0 ? (
            <div className="text-neutral-700 p-4 text-center">
              No questions found
            </div>
          ) : (
            <div className="space-y-2">
              {filteredQuestions.map((question) => (
                <div
                  key={question.id}
                  className="border border-neutral-500 rounded-md p-3"
                >
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      checked={selectedQuestions.some((q) => q.id === question.id)}
                      onChange={() => toggleQuestionSelection(question)}
                      className="mr-3 h-5 w-5 cursor-pointer"
                    />
                    <div className="flex-1">
                      <div className="font-medium">{question.label}</div>
                      {expandDetails && (
                        <div className="text-sm text-neutral-700 mt-1">
                          Type: {String(question.inputType)}
                          {question.hint && <div>Hint: {question.hint}</div>}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Action buttons */}
        <div className="border-t border-gray-200 p-4 sticky bottom-0 bg-neutral-50">
          <div className="flex justify-between">
            <button
              onClick={onClose}
              className="btn-outline"
            >
              Cancel
            </button>
            <button
              onClick={handleAddQuestions}
              disabled={selectedQuestions.length === 0}
              className={`px-4 py-2 rounded-md ${
                selectedQuestions.length > 0
                  ? "btn-primary"
                  : "bg-gray-200 text-gray-500 pointer-events-none"
              }`}
            >
              Add Selected ({selectedQuestions.length})
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LibraryQuestionsSidebar;

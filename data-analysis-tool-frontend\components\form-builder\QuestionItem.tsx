"use client";

import React, { useState } from "react";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { Question } from "@/types/formBuilder";

import { GripVertical, Trash, Copy, Pencil, CopyPlus } from "lucide-react";
import { TemplateQuestion } from "@/types";

interface QuestionItemProps {
  question: Question | TemplateQuestion;
  onEdit: () => void;
  onDelete: () => void;
  onDuplicate: () => void;
  isSelected?: boolean;
  onToggleSelect?: () => void;
  selectionMode?: boolean;
}

const QuestionItem = ({
  question,
  onEdit,
  onDelete,
  onDuplicate,
  isSelected = false,
  onToggleSelect,
  selectionMode = false,
}: QuestionItemProps) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: question.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={"border border-neutral-400 rounded-md bg-card shadow-sm"}
    >
      <div className="flex items-center p-4">
        {selectionMode && (
          <div className="mr-2">
            <input
              type="checkbox"
              checked={isSelected}
              onChange={() => onToggleSelect && onToggleSelect()}
              className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
            />
          </div>
        )}

        <div
          {...attributes}
          {...listeners}
          className="cursor-grab mr-3 hover:text-primary"
        >
          <GripVertical className="h-5 w-5" />
        </div>

        <div className="flex-1">
          <h3 className="text-xl font-semibold">
            {question.label || (
              <span className="text-muted-foreground italic">
                Empty question
              </span>
            )}
          </h3>
          {question.hint && (
            <p className="text-sm sub-text mt-1">{question.hint}</p>
          )}
        </div>

        <div className="flex items-center space-x-1">
          <button
            onClick={(e) => {
              e.stopPropagation();
              onDuplicate();
            }}
            title="Duplicate"
            className="cursor-pointer p-2 rounded-full hover:bg-primary-100 text-neutral-700 hover:text-primary-500 transition-colors"
          >
            <CopyPlus size={16} />
          </button>
          <button
            onClick={(e) => {
              e.stopPropagation();
              onDelete();
            }}
            title="Delete"
            className="cursor-pointer p-2 rounded-full hover:bg-destructive/10 text-neutral-700 hover:text-destructive transition-colors"
          >
            <Trash size={16} />
          </button>
          <button
            onClick={(e) => {
              e.stopPropagation();
              onEdit();
            }}
            title="Edit"
            className="cursor-pointer p-2 rounded-full hover:bg-primary-100 text-neutral-700 hover:text-primary-500 transition-colors"
          >
            <Pencil size={16} />
          </button>
        </div>
      </div>
    </div>
  );
};

export { QuestionItem };

import { z } from "zod";
import { InputType, Operator } from "@prisma/client";

// Example enum for InputType and Operator

// LibraryQuestionBlockQuestionOption Schema
export const LibraryQuestionBlockQuestionOptionSchema = z.object({
  id: z.number().int().optional(), // optional if creating
  label: z.string(),
  code: z.string(),
  nextLibraryQuestionId: z.number().int().nullable().optional(),
  libraryQuestionBlockQuestionId: z.number().int(),
});

// LibraryQuestionBlockQuestionCondition Schema
export const LibraryQuestionBlockQuestionConditionSchema = z.object({
  id: z.number().int().optional(),
  operator: z.nativeEnum(Operator, {
    errorMap: () => ({ message: "Invalid operator selected" }),
  }),
  value: z.string(),
  libraryQuestionBlockQuestionId: z.number().int(),
});

// LibraryQuestionBlockQuestionGroup Schema
export const LibraryQuestionBlockQuestionGroupSchema = z.object({
  id: z.number().int().optional(),
  title: z.string(),
  order: z.number().int().optional(),
  parentGroupId: z.number().int().nullable().optional(),
});

// LibraryQuestionBlockQuestion Schema
export const LibraryQuestionBlockQuestionSchema = z
  .object({
    id: z.number().int().optional(),
    label: z.string(),
    inputType: z.nativeEnum(InputType),
    hint: z.string().optional().nullable(),
    placeholder: z.string().optional().nullable(),
    isRequired: z.boolean().default(false),
    position: z.number().int(),
    userId: z.number().int(),
    // libraryQuestionBlockQuestionGroupId: z.number().int().nullable().optional(),
    questionOptions: z
      .array(
        z.object({
          id: z.number().optional(),
          label: z.string(),
          code: z.string(),
          nextQuestionId: z.number().optional(),
        })
      )
      .optional(),
    conditions: z
      .array(LibraryQuestionBlockQuestionConditionSchema)
      .optional()
      .nullable(),
  })
  .superRefine((data, ctx) => {
    if (
      (data.inputType === InputType.selectone ||
        data.inputType === InputType.selectmany) &&
      (!data.questionOptions || data.questionOptions.length === 0)
    ) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        path: ["questionOptions"],
        message: "Options are required for select input types.",
      });
    }
  });

export type libraryQuestionBlockQuestionInput = z.infer<
  typeof LibraryQuestionBlockQuestionSchema
>;
export type LibraryQuestionBlockQuestionOption = z.infer<
  typeof LibraryQuestionBlockQuestionOptionSchema
>;
export type LibraryQuestionBlockQuestionCondition = z.infer<
  typeof LibraryQuestionBlockQuestionConditionSchema
>;

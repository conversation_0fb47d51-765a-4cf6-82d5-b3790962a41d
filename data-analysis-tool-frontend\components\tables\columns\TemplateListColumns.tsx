import { Checkbox } from "@/components/ui";
import { encode } from "@/lib/encodeDecode";
import { Template } from "@/types";
import { ColumnDef } from "@tanstack/react-table";
import Link from "next/link";

const TemplateListColumns: ColumnDef<Template>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        className="w-6 h-6 data-[state=checked]:bg-primary-500 data-[state=checked]:text-neutral-500 rounded border border-neutral-100 data-[state=checked]:border-neutral-100 cursor-pointer"
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        className="w-6 h-6 bg-neutral-100 rounded border border-neutral-400 data-[state=checked]:bg-neutral-100 data-[state=checked]:text-primary-500 data-[state=checked]:border-primary-500 cursor-pointer"
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableHiding: false,
    enableSorting: false,
  },
  {
    accessorKey: "name",
    header: "Template Name",
    cell: ({ row }) => {
      const templateId = row.original.id;
      const encryptedTemplateId = encode(templateId);
      return (
        <Link
          href={`library/template/${encryptedTemplateId}/form-builder`}
          className="cursor-pointer text-primary-500 hover:text-primary-600 hover:underline transition-all duration-300"
        >
          {row.getValue("name")}
        </Link>
      );
    },
  },
  {
    accessorKey: "description",
    header: "Description",
  },
  {
    id: "owner",
    accessorFn: (row) => row.user?.name ?? "unknown", // So filtering works
    header: "Owner",
    cell: ({ getValue }) => getValue(), // You could use `getValue` directly
  },
  {
    accessorKey: "sector",
    header: "Sector",
  },
  {
    accessorKey: "country",
    header: "Country",
  },
  {
    accessorKey: "updatedAt",
    header: "Last Modified",
  },
];

export { TemplateListColumns };

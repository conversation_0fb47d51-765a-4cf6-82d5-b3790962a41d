import { z } from "zod";

// Validator for the ExportedFile model
export const ExportedFileValidator = z.object({
  projectId: z.number().optional(), // projectId is optional
  userId: z.number().optional(), // userId is optional
  fileName: z.string().min(1, "File name is required"), // Ensure file name is not empty
  fileType: z.string().min(1, "File type is required"), // Ensure file type is not empty
  contentType: z.string().min(1, "Content type is required"), // Ensure content type is not empty
  fileBuffer: z
    .instanceof(Buffer)
    .refine((buffer) => buffer.length > 0, "File buffer cannot be empty"), // Ensure fileBuffer is a non-empty buffer
  createdAt: z
    .date()
    .optional()
    .default(() => new Date()), // createdAt is optional and defaults to current date if not provided
});

export type CreateExportFile = z.infer<typeof ExportedFileValidator>;

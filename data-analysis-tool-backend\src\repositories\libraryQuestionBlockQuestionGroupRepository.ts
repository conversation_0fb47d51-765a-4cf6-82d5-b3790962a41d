import { promises } from "dns";
import { prisma } from "../utils/prisma";
import {
  LibraryQuestionBlockQuestionGroup,
  Prisma,
  QuestionGroup,
} from "@prisma/client";
import { promise } from "zod";
import { group } from "console";

class LibraryQuestionBlockQuestionGroupRepository {
  async create(data: {
    title: string;
    order: number;
    parentGroupId?: number;
    selectedQuestionIds?: number[];
  }): Promise<LibraryQuestionBlockQuestionGroup> {
    const newGroup = await prisma.libraryQuestionBlockQuestionGroup.create({
      data: {
        title: data.title,
        order: data.order,
        parentGroupId: data.parentGroupId,
      },
    });

    // Step 2: If there are selected question IDs, update those questions to associate them with the new group
    if (data.selectedQuestionIds && data.selectedQuestionIds.length > 0) {
      const question = await prisma.libraryQuestionBlockQuestion.updateMany({
        where: {
          id: {
            in: data.selectedQuestionIds, // Array of selected question IDs
          },
        },
        data: {
          libraryQuestionBlockQuestionGroupId: newGroup.id, // Set the new questionGroupId to associate with the new group
        },
      });

      console.log("updated questions", question);
    }

    return newGroup;
  }

  async delete(id: number): Promise<LibraryQuestionBlockQuestionGroup> {
    return await prisma.libraryQuestionBlockQuestionGroup.delete({
      where: { id },
    });
  }

  async deleteManyQuestionByGroup(id: number): Promise<Prisma.BatchPayload> {
    return await prisma.libraryQuestionBlockQuestion.deleteMany({
      where: {
        libraryQuestionBlockQuestionGroupId: id,
      },
    });
  }

  async findById(
    id: number
  ): Promise<LibraryQuestionBlockQuestionGroup | null> {
    return await prisma.libraryQuestionBlockQuestionGroup.findUnique({
      where: { id },
      include: { questionBlockQuestion: true },
    });
  }

  async update(
    id: number,
    updates: any
  ): Promise<LibraryQuestionBlockQuestionGroup> {
    return await prisma.libraryQuestionBlockQuestionGroup.update({
      where: { id },
      data: updates,
    });
  }

  async updateGroupInsideParentGroup(
    childGroupId: number,
    ParentGroupId: number
  ): Promise<LibraryQuestionBlockQuestionGroup | null> {
    return await prisma.libraryQuestionBlockQuestionGroup.update({
      where: {
        id: childGroupId,
      },
      data: {
        parentGroupId: ParentGroupId,
      },
    });
  }
  async RemoveGroupFromParentGroup(
    groupId: number
  ): Promise<LibraryQuestionBlockQuestionGroup | null> {
    return prisma.libraryQuestionBlockQuestionGroup.update({
      where: {
        id: groupId,
      },
      data: {
        parentGroupId: null,
      },
    });
  }
}

export default new LibraryQuestionBlockQuestionGroupRepository();

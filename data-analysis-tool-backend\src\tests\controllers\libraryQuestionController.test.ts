import { Request, Response } from "express";
import {
  createLibraryQuestion,
  getAllLibraryQuestions,
  getLibraryQuestionById,
  updateLibraryQuestion,
  duplicateTemplateQuestion,
  deleteLibraryQuestion,
} from "../../controllers/libraryQuestionController";
import libraryQuestionRepository from "../../repositories/libraryQuestionRepository";
import libraryTemplateRepository from "../../repositories/libraryTemplateRepository";
import { InputType, Operator } from "@prisma/client";

// Mock the repositories
jest.mock("../../repositories/libraryQuestionRepository");
jest.mock("../../repositories/libraryTemplateRepository");

describe("Library Question Controller", () => {
  let mockRequest: Partial<Request & { user?: { id: number } }>;
  let mockResponse: Partial<Response>;
  let responseObject: any = {};

  beforeEach(() => {
    // Reset mocks before each test
    jest.clearAllMocks();
    jest.resetAllMocks();

    // Setup mock response
    mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockImplementation((result) => {
        responseObject = result;
        return mockResponse;
      }),
    };

    // Reset response object
    responseObject = {};

    // Setup default authenticated user
    mockRequest = {
      user: {
        id: 1,
      },
      params: {},
      body: {},
    };
  });

  describe("createLibraryQuestion", () => {
    beforeEach(() => {
      mockRequest.params = { libraryTemplateId: "1" };
      mockRequest.body = {
        label: "Test Question",
        inputType: "text",
        isRequired: true,
        position: 1,
      };
    });

    it("should create a library question successfully", async () => {
      const mockQuestion = {
        id: 1,
        libraryTemplateId: 1,
        label: "Test Question",
        inputType: "text" as InputType,
        isRequired: true,
        position: 1,
        questionOptions: [],
        questionConditions: [],
      };

      (
        libraryQuestionRepository.isLibraryTemplateOwner as jest.Mock
      ).mockResolvedValue(true);
      (libraryQuestionRepository.create as jest.Mock).mockResolvedValue(
        mockQuestion
      );

      await createLibraryQuestion(mockRequest as any, mockResponse as Response);

      expect(
        libraryQuestionRepository.isLibraryTemplateOwner
      ).toHaveBeenCalledWith(1, 1);
      expect(libraryQuestionRepository.create).toHaveBeenCalledWith(
        expect.objectContaining({
          label: "Test Question",
          inputType: "text",
          isRequired: true,
          position: 1,
          libraryTemplateId: 1,
        })
      );
      expect(mockResponse.status).toHaveBeenCalledWith(201);
      expect(responseObject).toHaveProperty("data.question", mockQuestion);
      expect(responseObject).toHaveProperty(
        "message",
        "Library question created successfully"
      );
    });

    it("should return 403 when user is not the template owner", async () => {
      (
        libraryQuestionRepository.isLibraryTemplateOwner as jest.Mock
      ).mockResolvedValue(false);

      await createLibraryQuestion(mockRequest as any, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(403);
      expect(responseObject).toHaveProperty("success", false);
      expect(responseObject).toHaveProperty(
        "message",
        "User is not associated with this library template"
      );
      expect(libraryQuestionRepository.create).not.toHaveBeenCalled();
    });

    it("should return 400 for invalid input", async () => {
      // First we need to mock isLibraryTemplateOwner to return true
      // so that the validation check is reached
      (
        libraryQuestionRepository.isLibraryTemplateOwner as jest.Mock
      ).mockResolvedValue(true);

      // Then provide invalid input
      mockRequest.body = {}; // Missing required fields

      await createLibraryQuestion(mockRequest as any, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(responseObject).toHaveProperty("success", false);
      expect(responseObject).toHaveProperty("errors");
      expect(libraryQuestionRepository.create).not.toHaveBeenCalled();
    });

    it("should handle server errors", async () => {
      (
        libraryQuestionRepository.isLibraryTemplateOwner as jest.Mock
      ).mockRejectedValue(new Error("Database error"));

      await createLibraryQuestion(mockRequest as any, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(responseObject).toHaveProperty("success", false);
      expect(responseObject).toHaveProperty(
        "message",
        "Error creating library question"
      );
    });
  });

  describe("getAllLibraryQuestions", () => {
    beforeEach(() => {
      mockRequest.params = { libraryTemplateId: "1" };
    });

    it("should get all library questions successfully", async () => {
      const mockQuestions = [
        {
          id: 1,
          libraryTemplateId: 1,
          label: "Question 1",
          inputType: "text" as InputType,
          isRequired: true,
          position: 1,
          questionOptions: [],
          questionConditions: [],
        },
        {
          id: 2,
          libraryTemplateId: 1,
          label: "Question 2",
          inputType: "selectone" as InputType,
          isRequired: false,
          position: 2,
          questionOptions: [],
          questionConditions: [],
        },
      ];

      (
        libraryQuestionRepository.isLibraryTemplateOwner as jest.Mock
      ).mockResolvedValue(true);
      (
        libraryQuestionRepository.findByLibraryTemplateId as jest.Mock
      ).mockResolvedValue(mockQuestions);

      await getAllLibraryQuestions(
        mockRequest as any,
        mockResponse as Response
      );

      expect(
        libraryQuestionRepository.isLibraryTemplateOwner
      ).toHaveBeenCalledWith(1, 1);
      expect(
        libraryQuestionRepository.findByLibraryTemplateId
      ).toHaveBeenCalledWith(1);
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(responseObject).toHaveProperty("success", true);
      expect(responseObject).toHaveProperty(
        "message",
        "Successfully fetched template questions"
      );
      expect(responseObject).toHaveProperty("questions", mockQuestions);
    });

    it("should return 403 when user is not the template owner", async () => {
      (
        libraryQuestionRepository.isLibraryTemplateOwner as jest.Mock
      ).mockResolvedValue(false);

      await getAllLibraryQuestions(
        mockRequest as any,
        mockResponse as Response
      );

      expect(mockResponse.status).toHaveBeenCalledWith(403);
      expect(responseObject).toHaveProperty("success", false);
      expect(responseObject).toHaveProperty(
        "message",
        "User is not associated with this library template"
      );
      expect(
        libraryQuestionRepository.findByLibraryTemplateId
      ).not.toHaveBeenCalled();
    });

    it("should handle server errors", async () => {
      (
        libraryQuestionRepository.isLibraryTemplateOwner as jest.Mock
      ).mockRejectedValue(new Error("Database error"));

      await getAllLibraryQuestions(
        mockRequest as any,
        mockResponse as Response
      );

      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(responseObject).toHaveProperty("success", false);
      expect(responseObject).toHaveProperty(
        "message",
        "Error retrieving library questions"
      );
    });
  });

  describe("getLibraryQuestionById", () => {
    beforeEach(() => {
      mockRequest.params = { id: "1" };
    });

    it("should get a library question by ID successfully", async () => {
      const mockQuestion = {
        id: 1,
        libraryTemplateId: 1,
        label: "Test Question",
        inputType: "text" as InputType,
        isRequired: true,
        position: 1,
        questionOptions: [],
        questionConditions: [],
      };

      (libraryQuestionRepository.findById as jest.Mock).mockResolvedValue(
        mockQuestion
      );
      (
        libraryQuestionRepository.isLibraryTemplateOwner as jest.Mock
      ).mockResolvedValue(true);

      await getLibraryQuestionById(
        mockRequest as any,
        mockResponse as Response
      );

      expect(libraryQuestionRepository.findById).toHaveBeenCalledWith(1);
      expect(
        libraryQuestionRepository.isLibraryTemplateOwner
      ).toHaveBeenCalledWith(1, 1);
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(responseObject).toHaveProperty("data.question", mockQuestion);
      expect(responseObject).toHaveProperty(
        "message",
        "Library question retrieved successfully"
      );
    });

    it("should return 404 when question not found", async () => {
      (libraryQuestionRepository.findById as jest.Mock).mockResolvedValue(null);

      await getLibraryQuestionById(
        mockRequest as any,
        mockResponse as Response
      );

      expect(mockResponse.status).toHaveBeenCalledWith(404);
      expect(responseObject).toHaveProperty("success", false);
      expect(responseObject).toHaveProperty(
        "message",
        "Library question not found"
      );
    });

    it("should return 403 when user is not the template owner", async () => {
      const mockQuestion = {
        id: 1,
        libraryTemplateId: 1,
        label: "Test Question",
        inputType: "text" as InputType,
        isRequired: true,
        position: 1,
      };

      (libraryQuestionRepository.findById as jest.Mock).mockResolvedValue(
        mockQuestion
      );
      (
        libraryQuestionRepository.isLibraryTemplateOwner as jest.Mock
      ).mockResolvedValue(false);

      await getLibraryQuestionById(
        mockRequest as any,
        mockResponse as Response
      );

      expect(mockResponse.status).toHaveBeenCalledWith(403);
      expect(responseObject).toHaveProperty("success", false);
      expect(responseObject).toHaveProperty(
        "message",
        "User is not associated with this library template"
      );
    });

    it("should handle server errors", async () => {
      (libraryQuestionRepository.findById as jest.Mock).mockRejectedValue(
        new Error("Database error")
      );

      await getLibraryQuestionById(
        mockRequest as any,
        mockResponse as Response
      );

      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(responseObject).toHaveProperty("success", false);
      expect(responseObject).toHaveProperty(
        "message",
        "Error retrieving library question"
      );
    });
  });

  describe("updateLibraryQuestion", () => {
    beforeEach(() => {
      mockRequest.params = { id: "1" };
      mockRequest.body = {
        label: "Updated Question",
        isRequired: false,
      };
    });

    it("should update a library question successfully", async () => {
      const existingQuestion = {
        id: 1,
        libraryTemplateId: 1,
        label: "Test Question",
        inputType: "text" as InputType,
        isRequired: true,
        position: 1,
      };

      const updatedQuestion = {
        ...existingQuestion,
        label: "Updated Question",
        isRequired: false,
        questionOptions: [],
        questionConditions: [],
      };

      (libraryQuestionRepository.findById as jest.Mock).mockResolvedValue(
        existingQuestion
      );
      (
        libraryQuestionRepository.isLibraryTemplateOwner as jest.Mock
      ).mockResolvedValue(true);
      (libraryQuestionRepository.update as jest.Mock).mockResolvedValue(
        updatedQuestion
      );

      await updateLibraryQuestion(mockRequest as any, mockResponse as Response);

      expect(libraryQuestionRepository.findById).toHaveBeenCalledWith(1);
      expect(
        libraryQuestionRepository.isLibraryTemplateOwner
      ).toHaveBeenCalledWith(1, 1);
      expect(libraryQuestionRepository.update).toHaveBeenCalledWith(
        1,
        expect.objectContaining({
          label: "Updated Question",
          isRequired: false,
        })
      );
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(responseObject).toHaveProperty("data.question", updatedQuestion);
      expect(responseObject).toHaveProperty(
        "message",
        "Library question updated successfully"
      );
    });

    it("should return 404 when question not found", async () => {
      (libraryQuestionRepository.findById as jest.Mock).mockResolvedValue(null);

      await updateLibraryQuestion(mockRequest as any, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(404);
      expect(responseObject).toHaveProperty("success", false);
      expect(responseObject).toHaveProperty(
        "message",
        "Library question not found"
      );
      expect(libraryQuestionRepository.update).not.toHaveBeenCalled();
    });

    it("should return 403 when user is not the template owner", async () => {
      const existingQuestion = {
        id: 1,
        libraryTemplateId: 1,
        label: "Test Question",
        inputType: "text" as InputType,
        isRequired: true,
        position: 1,
      };

      (libraryQuestionRepository.findById as jest.Mock).mockResolvedValue(
        existingQuestion
      );
      (
        libraryQuestionRepository.isLibraryTemplateOwner as jest.Mock
      ).mockResolvedValue(false);

      await updateLibraryQuestion(mockRequest as any, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(403);
      expect(responseObject).toHaveProperty("success", false);
      expect(responseObject).toHaveProperty(
        "message",
        "User is not associated with this library template"
      );
      expect(libraryQuestionRepository.update).not.toHaveBeenCalled();
    });

    it("should handle server errors", async () => {
      (libraryQuestionRepository.findById as jest.Mock).mockRejectedValue(
        new Error("Database error")
      );

      await updateLibraryQuestion(mockRequest as any, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(responseObject).toHaveProperty("success", false);
      expect(responseObject).toHaveProperty(
        "message",
        "Error updating library question"
      );
    });
  });

  describe("duplicateTemplateQuestion", () => {
    beforeEach(() => {
      mockRequest.params = { id: "1" };
    });

    it("should duplicate a library question successfully", async () => {
      const originalQuestion = {
        id: 1,
        libraryTemplateId: 1,
        label: "Original Question",
        inputType: "text" as InputType,
        isRequired: true,
        position: 1,
      };

      const duplicatedQuestion = {
        id: 2,
        libraryTemplateId: 1,
        label: "Original Question",
        inputType: "text" as InputType,
        isRequired: true,
        position: 2,
        questionOptions: [],
        questionConditions: [],
      };

      (libraryQuestionRepository.findById as jest.Mock).mockResolvedValue(
        originalQuestion
      );
      (libraryTemplateRepository.isOwner as jest.Mock).mockResolvedValue(true);
      (
        libraryQuestionRepository.duplicateQuestion as jest.Mock
      ).mockResolvedValue(duplicatedQuestion);

      await duplicateTemplateQuestion(
        mockRequest as any,
        mockResponse as Response
      );

      expect(libraryQuestionRepository.findById).toHaveBeenCalledWith(1);
      expect(libraryTemplateRepository.isOwner).toHaveBeenCalledWith(1, 1);
      expect(libraryQuestionRepository.duplicateQuestion).toHaveBeenCalledWith(
        1,
        1
      );
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(responseObject).toHaveProperty("success", true);
      expect(responseObject).toHaveProperty(
        "message",
        "Successfully duplicated the question"
      );
      expect(responseObject).toHaveProperty(
        "duplicatedQuestion",
        duplicatedQuestion
      );
    });

    it("should return 404 when question not found", async () => {
      (libraryQuestionRepository.findById as jest.Mock).mockResolvedValue(null);

      await duplicateTemplateQuestion(
        mockRequest as any,
        mockResponse as Response
      );

      expect(mockResponse.status).toHaveBeenCalledWith(404);
      expect(responseObject).toHaveProperty("success", false);
      expect(responseObject).toHaveProperty(
        "message",
        "Question not found with the provided question id"
      );
      expect(
        libraryQuestionRepository.duplicateQuestion
      ).not.toHaveBeenCalled();
    });

    it("should return 403 when user is not the template owner", async () => {
      const originalQuestion = {
        id: 1,
        libraryTemplateId: 1,
        label: "Original Question",
        inputType: "text" as InputType,
        isRequired: true,
        position: 1,
      };

      (libraryQuestionRepository.findById as jest.Mock).mockResolvedValue(
        originalQuestion
      );
      (libraryTemplateRepository.isOwner as jest.Mock).mockResolvedValue(false);

      await duplicateTemplateQuestion(
        mockRequest as any,
        mockResponse as Response
      );

      expect(mockResponse.status).toHaveBeenCalledWith(403);
      expect(responseObject).toHaveProperty("success", false);
      expect(responseObject).toHaveProperty("message", "Unauthorized user");
      expect(
        libraryQuestionRepository.duplicateQuestion
      ).not.toHaveBeenCalled();
    });

    it("should handle server errors", async () => {
      (libraryQuestionRepository.findById as jest.Mock).mockRejectedValue(
        new Error("Database error")
      );

      await duplicateTemplateQuestion(
        mockRequest as any,
        mockResponse as Response
      );

      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(responseObject).toHaveProperty(
        "message",
        expect.stringContaining("Failed to duplicate question")
      );
    });
  });

  describe("deleteLibraryQuestion", () => {
    beforeEach(() => {
      mockRequest.params = { id: "1" };
    });

    it("should delete a library question successfully", async () => {
      const existingQuestion = {
        id: 1,
        libraryTemplateId: 1,
        label: "Test Question",
        inputType: "text" as InputType,
        isRequired: true,
        position: 1,
      };

      (libraryQuestionRepository.findById as jest.Mock).mockResolvedValue(
        existingQuestion
      );
      (
        libraryQuestionRepository.isLibraryTemplateOwner as jest.Mock
      ).mockResolvedValue(true);
      (libraryQuestionRepository.delete as jest.Mock).mockResolvedValue(
        undefined
      );

      await deleteLibraryQuestion(mockRequest as any, mockResponse as Response);

      expect(libraryQuestionRepository.findById).toHaveBeenCalledWith(1);
      expect(
        libraryQuestionRepository.isLibraryTemplateOwner
      ).toHaveBeenCalledWith(1, 1);
      expect(libraryQuestionRepository.delete).toHaveBeenCalledWith(1);
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(responseObject).toHaveProperty(
        "message",
        "Library question deleted successfully"
      );
    });

    it("should return 404 when question not found", async () => {
      (libraryQuestionRepository.findById as jest.Mock).mockResolvedValue(null);

      await deleteLibraryQuestion(mockRequest as any, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(404);
      expect(responseObject).toHaveProperty("success", false);
      expect(responseObject).toHaveProperty(
        "message",
        "Library question not found"
      );
      expect(libraryQuestionRepository.delete).not.toHaveBeenCalled();
    });

    it("should return 403 when user is not the template owner", async () => {
      const existingQuestion = {
        id: 1,
        libraryTemplateId: 1,
        label: "Test Question",
        inputType: "text" as InputType,
        isRequired: true,
        position: 1,
      };

      (libraryQuestionRepository.findById as jest.Mock).mockResolvedValue(
        existingQuestion
      );
      (
        libraryQuestionRepository.isLibraryTemplateOwner as jest.Mock
      ).mockResolvedValue(false);

      await deleteLibraryQuestion(mockRequest as any, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(403);
      expect(responseObject).toHaveProperty("success", false);
      expect(responseObject).toHaveProperty(
        "message",
        "User is not associated with this library template"
      );
      expect(libraryQuestionRepository.delete).not.toHaveBeenCalled();
    });

    it("should handle server errors", async () => {
      (libraryQuestionRepository.findById as jest.Mock).mockRejectedValue(
        new Error("Database error")
      );

      await deleteLibraryQuestion(mockRequest as any, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(responseObject).toHaveProperty("success", false);
      expect(responseObject).toHaveProperty(
        "message",
        "Error deleting library question"
      );
    });
  });
});

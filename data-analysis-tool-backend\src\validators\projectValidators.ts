import { z } from "zod";
import { Sector, Status } from "@prisma/client";

export const ProjectSchema = z.object({
  name: z.string().min(1, "Name is required"),
  description: z.string().min(1, "Name is required"),
  sector: z.nativeEnum(Sector),
  country: z.string().optional(),
});

export const ChangeStatusSchema = z.object({
  status: z.nativeEnum(Status),
});

export const updateProjectStatusesSchema = z.object({
  projectIds: z.array(z.number().int().positive()).nonempty({
    message: "At least one project ID is required.",
  }),
  status: z.nativeEnum(Status),
});

export const deleteMultipleProjectSchema = z.object({
  projectIds: z.array(z.number().int().positive()).nonempty({
    message: "At least one project ID is required.",
  }),
});

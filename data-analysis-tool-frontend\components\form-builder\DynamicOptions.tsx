import { Plus, Trash } from "lucide-react";
import { useEffect } from "react";
import { useFieldArray, useFormContext } from "react-hook-form";
import { QuestionSelector } from "./QuestionSelector";
import { ContextType } from "@/types";

interface DynamicOptionsProps {
  contextType?: ContextType;
  contextId?: number;
  currentQuestionId?: number;
  inputType?: string;
}

const DynamicOptions = ({
  contextType,
  contextId,
  currentQuestionId,
  inputType,
}: DynamicOptionsProps) => {
  const {
    control,
    register,
    formState: { errors },
    setValue,
    watch,
  } = useFormContext();
  const { fields, append, remove } = useFieldArray({
    control,
    name: "questionOptions",
  });

  useEffect(() => {
    if (fields.length === 0) {
      append({ label: "", sublabel: "", code: "", nextQuestionId: null });
    }
  }, [fields, append]);

  // Check if current input type supports conditional questions
  const supportsConditionalQuestions =
    inputType === "selectone" || inputType === "selectmany";

  return (
    <div className="flex flex-col gap-2">
      <label className="label-text">Options</label>
      <div className="flex flex-col gap-2">
        {fields.map((field, index) => (
          <div
            key={index}
            className="border  border-gray-400 rounded-lg p-3 space-y-2"
          >
            <div className="flex items-center gap-2">
              <input
                {...register(`questionOptions.${index}.label`)}
                placeholder={`Option ${index + 1}`}
                className="input-field flex-1 min-w-[150px]"
              />

              <input
                {...register(`questionOptions.${index}.sublabel`)}
                placeholder={`Sub Options`}
                className="input-field flex-1 min-w-[150px]"
              />
              <input
                {...register(`questionOptions.${index}.code`)}
                placeholder="Code"
                className="w-28 input-field"
              />
              <button
                type="button"
                onClick={() => remove(index)}
                className="p-2 rounded-full hover:bg-red-500/10 text-neutral-700 hover:text-red-500 transition-colors cursor-pointer duration-300"
              >
                <Trash size={16} />
              </button>
            </div>

            {/* Conditional Question Selector */}
            {supportsConditionalQuestions && contextType && contextId && (
              <div className="ml-2">
                <label className="text-xs text-gray-600 mb-1 block">
                  Next Question (when this option is selected):
                </label>
                <QuestionSelector
                  contextType={contextType}
                  contextId={contextId}
                  currentQuestionId={currentQuestionId}
                  value={watch(`questionOptions.${index}.nextQuestionId`)}
                  onChange={(questionId) => {
                    setValue(
                      `questionOptions.${index}.nextQuestionId`,
                      questionId
                    );
                  }}
                  placeholder="No follow-up question"
                />
              </div>
            )}
          </div>
        ))}

        {errors.questionOptions && (
          <p className="text-sm text-red-500">{`${errors.questionOptions.root?.message}`}</p>
        )}
        <button
          type="button"
          onClick={() => append({ label: "", sublabel: "", code: "", nextQuestionId: null })}
          className="btn-outline mt-2 flex items-center justify-center gap-2"
        >
          <Plus size={16} />
          Add Option
        </button>
      </div>
    </div>
  );
};

export { DynamicOptions };

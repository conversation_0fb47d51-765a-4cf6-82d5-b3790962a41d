"use client";

import { useState, useEffect } from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { DateRangePicker } from "@/components/ui/date-range-picker";
import { usePathname } from "next/navigation";
import axiosInstance from "@/lib/axios";
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  <PERSON><PERSON>,
  Pie,
  Cell,
  <PERSON>hart as RechartsBar<PERSON>hart,
  Bar,
} from "recharts";
import { DateRange } from "react-day-picker";
import { Tooltip as UiTooltip } from "@/components/ui/tooltip";

// Add COLORS constant
const COLORS = [
  "#0088FE",
  "#00C49F",
  "#FFBB28",
  "#FF8042",
  "#8884D8",
  "#82CA9D",
  "#FFC658",
  "#FF6B6B",
];

// Chart type options
const CHART_TYPES = [
  { value: "verticalBar", label: "Vertical Bar", icon: BarChartIcon },
  { value: "pie", label: "Pie", icon: PieChartIcon },
  { value: "donut", label: "Donut", icon: PieChartIcon },
];

// Report data interfaces
interface ReportItem {
  question: string;
  type: string;
  answered: number;
  total: number;
  table:
    | Array<{
        value: string;
        frequency: number;
        percentage: number;
      }>
    | {
        structure: {
          columns: Array<{
            id: number;
            name: string;
            parentId: number | null;
            children: Array<{
              id: number;
              name: string;
            }>;
          }>;
          rows: Array<{
            id: number;
            name: string;
          }>;
        };
        data: Array<{
          value: any;
          rowId: number | null;
          columnId: number | null;
          cellValue: string | null;
        }>;
        cellValues?: Record<string, string> | Map<string, string>;
        metadata?: {
          hasRealData: boolean;
          rowCount: number;
          columnCount: number;
          totalSubmissions: number;
          submissionsWithData: number;
          lastUpdated: string;
        };
      };
  chartData: {
    labels: string[];
    values: number[];
  };
  stats?: {
    min: number;
    max: number;
    avg: number;
  };
  trendData?: Array<{
    date: string;
    value: number;
  }>;
}

interface ReportSummary {
  totalSubmissions: number;
  totalQuestions: number;
  averageResponseRate: number;
  submissionDates: string[];
}

interface ReportMetadata {
  projectName: string;
  generatedAt: string;
  filters: {
    type: string;
    startDate?: string;
    endDate?: string;
  };
}

interface ReportData {
  summary: ReportSummary;
  data: ReportItem[];
  metadata: ReportMetadata;
}

// Chart
const PieChartComponent = ({
  data,
  donut = false,
}: {
  data: { labels: string[]; values: number[] };
  donut?: boolean;
}) => {
  const chartData = data.labels.map((label, index) => ({
    name: label,
    value: data.values[index],
  }));
  return (
    <div className="h-[400px] w-full">
      <ResponsiveContainer>
        <PieChart>
          <Pie
            data={chartData}
            dataKey="value"
            nameKey="name"
            cx="50%"
            cy="50%"
            outerRadius={150}
            innerRadius={donut ? 80 : 0}
            fill="#8884d8"
            label={({ name, percent }) =>
              `${name} (${(percent * 100).toFixed(0)}%)`
            }
          >
            {chartData.map((_, index) => (
              <Cell
                key={`cell-${index}`}
                fill={COLORS[index % COLORS.length]}
              />
            ))}
          </Pie>
          <Tooltip formatter={(value) => [`${value} responses`, "Count"]} />
          <Legend />
        </PieChart>
      </ResponsiveContainer>
    </div>
  );
};

const BarChartComponent = ({
  data,
  layout = "verticalBar",
  questionType,
}: {
  data: { labels: string[]; values: number[] };
  layout?: "verticalBar" | "horizontalBar";
  questionType?: string;
}) => {
  const chartData = data.labels.map((label, index) => ({
    name: label,
    value: data.values[index],
  }));

  // For table data, we want to use a horizontal bar chart to better display row names
  const effectiveLayout = questionType === "table" ? "horizontalBar" : layout;

  // For table data, we need to limit the number of rows to display
  const displayData =
    questionType === "table" && chartData.length > 10
      ? chartData.slice(0, 10)
      : chartData;

  return (
    <div className="h-[400px] w-full">
      <ResponsiveContainer>
        <RechartsBarChart
          data={displayData}
          layout={
            effectiveLayout === "horizontalBar" ? "vertical" : "horizontal"
          }
          margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
        >
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis
            dataKey="name"
            type={effectiveLayout === "horizontalBar" ? "number" : "category"}
            tick={{ fontSize: 12 }}
          />
          <YAxis
            type={effectiveLayout === "horizontalBar" ? "category" : "number"}
            tick={{ fontSize: 12 }}
            width={questionType === "table" ? 150 : 60}
          />
          <Tooltip formatter={(value) => [`${value} responses`, "Count"]} />
          <Legend />
          <Bar dataKey="value" fill="#8884d8">
            {displayData.map((_, index) => (
              <Cell
                key={`cell-${index}`}
                fill={COLORS[index % COLORS.length]}
              />
            ))}
          </Bar>
        </RechartsBarChart>
      </ResponsiveContainer>
    </div>
  );
};

const TrendChart = ({
  data,
}: {
  data?: Array<{ date: string; value: number }>;
}) => {
  if (!data || data.length === 0) {
    return (
      <div className="h-[400px] flex items-center justify-center">
        <p className="text-muted-foreground">No trend data available</p>
      </div>
    );
  }

  return (
    <div className="h-[400px]">
      <ResponsiveContainer width="100%" height="100%">
        <LineChart data={data}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="date" />
          <YAxis />
          <Tooltip />
          <Legend />
          <Line type="monotone" dataKey="value" stroke="#8884d8" />
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
};

// Report Section Component
const ReportSection = ({ report }: { report: ReportItem }) => {
  const { question, type, answered, total, table, chartData, stats } = report;
  const responseRate = total > 0 ? Math.round((answered / total) * 100) : 0;
  // Default chart type based on question type
  const defaultChartType = type === "table" ? "verticalBar" : "pie";
  const [chartType, setChartType] = useState<string>(defaultChartType);

  // Check if the question type supports charts (selectone, selectmany, but NOT table)
  const supportsCharts = type === "selectone" || type === "selectmany";

  // Helper to render the selected chart
  const renderChart = () => {
    switch (chartType) {
      case "verticalBar":
        return (
          <BarChartComponent
            data={chartData}
            layout="verticalBar"
            questionType={type}
          />
        );
      case "pie":
        return <PieChartComponent data={chartData} />;
      case "donut":
        return <PieChartComponent data={chartData} donut />;
      default:
        return <PieChartComponent data={chartData} />;
    }
  };

  // Render the table view
  const renderTable = () => {
    // Check if this is a matrix table format
    if (type === "table" && !Array.isArray(table) && table.structure) {
      // Log the entire table object to see what we're working with
      console.log("Table data:", table);

      const { structure, data, cellValues, metadata } = table as {
        structure: {
          columns: Array<{
            id: number;
            name: string;
            parentId: number | null;
            children: Array<{
              id: number;
              name: string;
            }>;
          }>;
          rows: Array<{
            id: number;
            name: string;
          }>;
        };
        data: Array<{
          value: any;
          rowId: number | null;
          columnId: number | null;
          cellValue: string | null;
        }>;
        cellValues: Record<string, string> | Map<string, string>;
        metadata?: {
          hasRealData: boolean;
          rowCount: number;
          columnCount: number;
          totalSubmissions: number;
          submissionsWithData: number;
          lastUpdated: string;
        };
      };

      // Get parent columns (those without a parentId)
      const parentColumns = structure.columns.filter(
        (col) => col.parentId === null
      );

      // Create a map for quick lookup of cell values
      // First convert the object from the backend to a proper Map
      let cellValueMap = new Map<string, string>();

      // If we have cellValues from the backend, use those
      if (cellValues) {
        console.log("Cell values from backend:", cellValues);

        // Convert from object to Map if needed
        if (typeof cellValues === "object" && !Array.isArray(cellValues)) {
          Object.entries(cellValues).forEach(([key, value]) => {
            console.log(`Setting cell value for ${key}: ${value}`);
            cellValueMap.set(key, value as string);
          });
        } else if (cellValues instanceof Map) {
          // Use the Map directly
          cellValueMap = cellValues;
        }
      } else {
        console.log("No cellValues provided from backend");
      }

      // Also add any values from the data array
      if (data && data.length > 0) {
        console.log("Data array:", data);
        data.forEach((item) => {
          if (item.rowId && item.columnId) {
            const key = `${item.rowId}_${item.columnId}`;
            if (item.cellValue) {
              console.log(
                `Setting cell value from data for ${key}: ${item.cellValue}`
              );
              cellValueMap.set(key, item.cellValue);
            }
          }
        });
      } else {
        console.log("No data array or empty data array");
      }

      // For debugging - log the cell values
      console.log("Final cell value map:", Object.fromEntries(cellValueMap));

      // If we have no cell values, let's create some dummy data for testing
      if (cellValueMap.size === 0) {
        console.log("No cell values found, creating dummy data for testing");
        structure.rows.forEach((row) => {
          structure.columns.forEach((col) => {
            if (col.parentId === null) {
              const key = `${row.id}_${col.id}`;
              cellValueMap.set(key, `Test value for ${row.name} - ${col.name}`);
            }
          });
        });
      }

      return (
        <div className="overflow-auto">
          <div className="mb-2 p-2 bg-blue-50 rounded-md">
            <p className="text-sm text-blue-700">
              <span className="font-medium">Matrix Question:</span> This table
              shows the submitted responses for each row and column.
            </p>
            {metadata && (
              <div className="mt-1 text-xs text-blue-600 flex items-center justify-between">
                <span>
                  {metadata.hasRealData
                    ? `Showing data from ${metadata.submissionsWithData} of ${metadata.totalSubmissions} submissions`
                    : `No submissions found for this table`}
                </span>
                <span>
                  Last updated:{" "}
                  {new Date(metadata.lastUpdated).toLocaleString()}
                </span>
              </div>
            )}
          </div>
          <table className="w-full my-4 text-sm border-collapse shadow-sm">
            <thead>
              <tr className="border-b bg-gray-100">
                <th className="pb-3 pt-3 text-left border p-2 bg-gray-50 font-semibold text-gray-700">
                  {question}
                </th>
                {parentColumns.map((col) => (
                  <th
                    key={col.id}
                    className="pb-3 pt-3 text-center border p-2 bg-gray-50 font-semibold text-gray-700"
                  >
                    {col.name}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody>
              {structure.rows.map((row, rowIndex) => (
                <tr
                  key={row.id}
                  className={`border-b last:border-0 ${
                    rowIndex % 2 === 0 ? "bg-white" : "bg-gray-50"
                  } hover:bg-blue-50 transition-colors duration-150`}
                >
                  <td className="py-3 border p-3 font-medium text-gray-700">
                    {row.name}
                  </td>
                  {parentColumns.map((col) => {
                    // If this column has children, we need to handle it differently
                    if (col.children && col.children.length > 0) {
                      return (
                        <td
                          key={col.id}
                          className="py-3 border p-3 text-center"
                        >
                          {col.children.map((child) => {
                            // Standardized key format
                            const key = `${row.id}_${child.id}`;

                            // Get value from cellValues object directly
                            let value =
                              cellValues instanceof Map
                                ? cellValues.get(key)
                                : typeof cellValues === "object" &&
                                  !Array.isArray(cellValues)
                                ? (cellValues as Record<string, string>)[key]
                                : cellValueMap.get(key);

                            return (
                              <div
                                key={child.id}
                                className="mb-2 p-1 rounded hover:bg-blue-100"
                              >
                                <span className="font-medium text-xs text-gray-600 block mb-1">
                                  {child.name}
                                </span>
                                <span className="text-gray-800">
                                  {value !== undefined && value !== "" ? (
                                    // Check if this is aggregated data (contains commas or count indicators)
                                    value.includes(",") ||
                                    value.includes("(") ? (
                                      <span className="text-xs">
                                        {value.split(", ").map((part, idx) => (
                                          <span
                                            key={idx}
                                            className={`inline-block mr-1 mb-1 px-1 py-0.5 rounded ${
                                              part.includes("(")
                                                ? "bg-blue-100 text-blue-800"
                                                : "bg-gray-100 text-gray-700"
                                            }`}
                                          >
                                            {part}
                                          </span>
                                        ))}
                                      </span>
                                    ) : (
                                      value
                                    )
                                  ) : (
                                    "-"
                                  )}
                                </span>
                              </div>
                            );
                          })}
                        </td>
                      );
                    } else {
                      // Simple column without children
                      const key = `${row.id}_${col.id}`;

                      // Get value from cellValues object directly
                      let value =
                        cellValues instanceof Map
                          ? cellValues.get(key)
                          : typeof cellValues === "object" &&
                            !Array.isArray(cellValues)
                          ? (cellValues as Record<string, string>)[key]
                          : cellValueMap.get(key);

                      return (
                        <td
                          key={col.id}
                          className={`py-3 border p-3 text-center ${
                            value && value !== "-"
                              ? "text-gray-800 font-medium"
                              : "text-gray-400"
                          }`}
                        >
                          {value !== undefined && value !== "" ? (
                            // Check if this is aggregated data (contains commas or count indicators)
                            value.includes(",") || value.includes("(") ? (
                              <span className="text-xs">
                                {value.split(", ").map((part, idx) => (
                                  <span
                                    key={idx}
                                    className={`inline-block mr-1 mb-1 px-1 py-0.5 rounded ${
                                      part.includes("(")
                                        ? "bg-blue-100 text-blue-800"
                                        : "bg-gray-100 text-gray-700"
                                    }`}
                                  >
                                    {part}
                                  </span>
                                ))}
                              </span>
                            ) : (
                              value
                            )
                          ) : (
                            "-"
                          )}
                        </td>
                      );
                    }
                  })}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      );
    }

    // Default table format for non-matrix questions
    return (
      <div className="overflow-auto">
        <table className="w-full my-4 text-sm">
          <thead>
            <tr className="border-b">
              <th className="pb-2 text-left">Value</th>
              <th className="pb-2 text-right">Count</th>
              <th className="pb-2 text-right">Percentage</th>
            </tr>
          </thead>
          <tbody>
            {Array.isArray(table) &&
              table.map((row, index) => (
                <tr key={index} className="border-b last:border-0">
                  <td className="py-2">{row.value || "No Answer"}</td>
                  <td className="py-2 text-right">{row.frequency}</td>
                  <td className="py-2 text-right">{row.percentage}%</td>
                </tr>
              ))}
          </tbody>
        </table>
      </div>
    );
  };

  return (
    <Card className="mb-6">
      <CardHeader>
        <div className="flex flex-col space-y-2 md:space-y-0 md:flex-row md:justify-between md:items-center">
          <CardTitle className="text-base">{question}</CardTitle>
          <div className="flex items-center space-x-2">
            <Badge
              variant={
                responseRate > 75
                  ? "default"
                  : responseRate > 50
                  ? "secondary"
                  : "outline"
              }
            >
              {answered} of {total} responses ({responseRate}%)
            </Badge>
            <Badge variant="outline">{type}</Badge>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {supportsCharts ? (
          // For selectone and selectmany, show both table and chart options
          <Tabs defaultValue="table">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="table">Table</TabsTrigger>
              <TabsTrigger value="chart">Chart</TabsTrigger>
            </TabsList>

            <TabsContent value="table">{renderTable()}</TabsContent>

            <TabsContent value="chart">
              <div className="flex items-center space-x-2 mb-4">
                {CHART_TYPES.map((ct) => {
                  const Icon = ct.icon;
                  const iconStyle =
                    ct.value === "verticalBar"
                      ? { transform: "rotate(90deg)" }
                      : {};
                  return (
                    <UiTooltip key={ct.value} content={ct.label} side="top">
                      <button
                        onClick={() => setChartType(ct.value)}
                        className={`p-2 rounded-full border transition-colors duration-150 ${
                          chartType === ct.value
                            ? "bg-primary text-neutral-100 border-primary"
                            : "bg-muted text-muted-foreground border-transparent"
                        }`}
                        style={iconStyle}
                      >
                        <Icon size={20} />
                      </button>
                    </UiTooltip>
                  );
                })}
              </div>

              {renderChart()}
            </TabsContent>
          </Tabs>
        ) : (
          // For other input types, only show the table view
          <div>
            <h3 className="text-sm font-medium mb-4">Response Data</h3>
            {renderTable()}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

// Main Page Component
export default function ReportsPage() {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [reportData, setReportData] = useState<ReportData | null>(null);
  const [dateRange, setDateRange] = useState<DateRange | undefined>();
  const pathname = usePathname();

  const fetchReportData = async (startDate?: Date, endDate?: Date) => {
    try {
      setIsLoading(true);
      setError(null);

      // Get the hashed project ID from the URL path
      const pathParts = pathname.split("/");
      const hashedId = pathParts[pathParts.indexOf("project") + 1];

      if (!hashedId) {
        throw new Error("Project ID not found in URL");
      }

      const queryParams = new URLSearchParams();
      if (startDate) queryParams.append("startDate", startDate.toISOString());
      if (endDate) queryParams.append("endDate", endDate.toISOString());

      console.log("Fetching report data for project:", hashedId);
      const response = await axiosInstance.get(
        `/projects/${hashedId}/report?${queryParams.toString()}`
      );

      console.log("Report data response:", response.data);

      if (response.data?.data) {
        setReportData(response.data.data);
      } else {
        throw new Error("Invalid response format from server");
      }
    } catch (err: any) {
      console.error("Error fetching report data:", err);
      if (err.code === "ERR_NETWORK") {
        setError(
          "Unable to connect to the server. Please make sure the backend server is running."
        );
      } else if (err.response?.status === 401) {
        setError(
          "You are not authorized to view this report. Please log in again."
        );
      } else if (err.response?.status === 403) {
        setError("You don't have permission to view this report.");
      } else if (err.response?.status === 404) {
        setError(
          "Report not found. The project may have been deleted or you don't have access."
        );
      } else {
        setError(
          err.response?.data?.message ||
            err.message ||
            "Failed to load report data. Please try again later."
        );
      }
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchReportData();
  }, [pathname]);

  const handleDateRangeChange = (range: DateRange | undefined) => {
    setDateRange(range);
    if (range?.from && range?.to) {
      fetchReportData(range.from, range.to);
    }
  };

  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center h-[calc(100vh-200px)]">
        <Loader2 className="w-10 h-10 animate-spin text-primary" />
        <p className="mt-4 text-lg">Loading report data...</p>
      </div>
    );
  }

  if (error || !reportData) {
    return (
      <div className="p-4 md:p-8">
        <div className="mb-6">
          <h1 className="text-2xl font-bold">Data Report</h1>
          <div className="p-8 mt-4 text-center border rounded-md border-gray-200 bg-gray-50">
            <p className="text-lg text-red-500">
              {error || "No data available"}
            </p>
            <p className="mt-2 text-gray-600">
              {!error && "Submit some form responses to generate a report."}
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-4 md:p-8">
      <div className="mb-6">
        <div className="flex flex-col space-y-4 md:space-y-0 md:flex-row md:justify-between md:items-center">
          <div>
            <h1 className="text-2xl font-bold">Data Report</h1>
            <p className="mt-2 text-sm text-gray-500">
              {reportData.metadata.projectName}
            </p>
          </div>
          <DateRangePicker value={dateRange} onChange={handleDateRangeChange} />
        </div>

        <div className="grid grid-cols-1 gap-4 mt-6 md:grid-cols-3">
          <Card>
            <CardHeader>
              <CardTitle className="text-sm font-medium">
                Total Submissions
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-2xl font-bold">
                {reportData.summary.totalSubmissions}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-sm font-medium">
                Total Questions
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-2xl font-bold">
                {reportData.summary.totalQuestions}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-sm font-medium">
                Response Rate
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-2xl font-bold">
                {reportData.summary.averageResponseRate}%
              </p>
            </CardContent>
          </Card>
        </div>
      </div>

      <div>
        {reportData.data.map((report, index) => (
          <ReportSection key={index} report={report} />
        ))}
      </div>
    </div>
  );
}

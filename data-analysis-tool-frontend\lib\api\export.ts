import axios from "../axios";

const exportData = async (projectIds: number[], type: String) => {
  try {
    const { data } = await axios.post(`/export/${projectIds}?type=${type}`, {});
    return data;
  } catch (error) {
    console.error("Error exporting data:", error);
    throw error;
  }
};

const getexportData = async (projectIds: number[]) => {
  try {
    const { data } = await axios.get(`/export?projectId=${projectIds}`);
    return data;
  } catch (error) {
    console.error("Error fetching export data:", error);
    throw error;
  }
};


 const downloadExportData = async (fileId: string, fileName: string) => {
  try {
    const response = await axios.get(`/export/download/${fileId}`, {
      responseType: "blob", // Handle binary response
    });

    // Extract filename from Content-Disposition header
    const contentDisposition = response.headers["content-disposition"];

    if (contentDisposition) {
      const match = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);
      if (match && match[1]) {
        fileName = match[1].replace(/['"]/g, "");
      }
    }

     // Create a Blob from the response data (binary file content)
  const blob = new Blob([response.data]);

  // Generate a temporary URL for the Blob
  const url = window.URL.createObjectURL(blob);

  // Create an anchor element to trigger the download
  const a = document.createElement("a");
  a.href = url;
  a.download = fileName; // Set the desired file name
  document.body.appendChild(a);
  a.click(); // Trigger the download
  a.remove(); // Clean up the DOM

  // Release the Blob URL to free up memory
  window.URL.revokeObjectURL(url);
} catch (error) {
  console.error("Error downloading data:", error);
  throw error;
}
};


export { exportData, getexportData, downloadExportData };

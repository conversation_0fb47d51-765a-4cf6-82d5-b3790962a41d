import {
  setAuthenticatedUser,
  setAuthError,
  setAuthLoading,
  setUnauthenticated,
} from "@/redux/slices/authSlice";
import { RootState } from "@/redux/store";
import { UserSession } from "@/types/authTypes";
import { AxiosError, isAxiosError } from "axios";
import axios from "@/lib/axios";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";

const useAuth = (options?: { skipFetchUser?: boolean }) => {
  const dispatch = useDispatch();
  const router = useRouter();
  const { status, user, error } = useSelector((state: RootState) => state.auth);

  const fetchUserData = async () => {
    try {
      dispatch(setAuthLoading());
      const response = await axios.get(`/users/me`);
      const userData: UserSession = response.data;
      dispatch(setAuthenticatedUser(userData));
    } catch (error) {
      // Handle errors, especially 401 Unauthorized
      dispatch(setUnauthenticated());

      if (isAxiosError(error)) {
        console.log(
          "Auth error:",
          error.response?.status,
          error.response?.data
        );

        // If error is 401 Unauthorized (including expired token)
        if (error.response?.status === 401) {
          router.push("/");
        } else {
          // For other errors
          dispatch(
            setAuthError(error.response?.data?.message || error.message)
          );
        }
      } else {
        dispatch(
          setAuthError(
            error instanceof Error
              ? error.message
              : "An unknown error occurred."
          )
        );
      }
    }
  };

  useEffect(() => {
    if (!options?.skipFetchUser) {
      fetchUserData();
    }
  }, [options?.skipFetchUser]);

  // Add event listener for storage changes to handle logout across tabs
  useEffect(() => {
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === "logout" && e.newValue === "true") {
        dispatch(setUnauthenticated());
        router.push("/");
      }
    };

    window.addEventListener("storage", handleStorageChange);

    return () => {
      window.removeEventListener("storage", handleStorageChange);
    };
  }, [dispatch, router]);

  const refreshAuthState = () => {
    fetchUserData();
  };

  const signin = async (
    data: { email: string; password: string },
    onSuccess?: () => void,
    onError?: (errorType?: string) => void
  ) => {
    try {
      await axios.post(`/users/login`, data);
      await fetchUserData();
      onSuccess?.();
    } catch (error) {
      if (error instanceof AxiosError) {
        const errorType = error.response?.data?.errorType;
        onError?.(errorType);
      } else {
        onError?.();
      }
    }
  };

  const logout = async () => {
    try {
      await axios.post(`/users/logout`);
      // Notify other tabs about logout
      localStorage.setItem("logout", "true");
      // Remove the flag immediately to ensure future logout events still trigger
      setTimeout(() => localStorage.removeItem("logout"), 100);
    } finally {
      dispatch(setUnauthenticated());
      router.push("/");
    }
  };

  return {
    status,
    user,
    error,
    isAuthenticated: status === "authenticated",
    isLoading: status === "loading",
    refreshAuthState,
    signin,
    logout,
  };
};

export { useAuth };

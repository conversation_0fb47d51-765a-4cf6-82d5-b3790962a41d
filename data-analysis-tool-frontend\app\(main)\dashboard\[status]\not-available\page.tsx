"use client";

import React from "react";
import { useParams } from "next/navigation";
import { BsFolderPlus } from "react-icons/bs";
import { useDispatch } from "react-redux";
import { showCreateProjectModal } from "@/redux/slices/createProjectSlice";

const NotAvailablePage = () => {
  const { status } = useParams();
  const dispatch = useDispatch();
  
  const getStatusCapitalized = () => {
    if (!status || typeof status !== 'string') return 'Projects';
    return status.charAt(0).toUpperCase() + status.slice(1);
  };

  const handleCreateProject = () => {
    dispatch(showCreateProjectModal());
  };

  return (
    <div className="flex flex-col items-center justify-center py-16 px-4 min-h-[70vh]">
      <div className="bg-neutral-100 rounded-lg shadow-sm p-8 max-w-md w-full text-center">
        <div className="flex justify-center mb-6">
          <div className="bg-neutral-200 p-5 rounded-full">
            <BsFolderPlus size={50} className="text-primary-500" />
          </div>
        </div>
        <h2 className="text-2xl font-semibold text-neutral-800 mb-2">
          No {getStatusCapitalized()} Projects
        </h2>
        <p className="text-neutral-600 mb-8">
          {status === "draft" && "You don't have any draft projects yet. Create a new project to get started."}
          {status === "deployed" && "You don't have any deployed projects yet. Deploy a project to see it here."}
          {status === "archived" && "You don't have any archived projects yet. Archived projects will appear here."}
          {!status && "No projects available in this category."}
        </p>
        
        {status === "draft" && (
          <button 
            onClick={handleCreateProject}
            className="btn-primary w-full"
          >
            Create New Project
          </button>
        )}
        
        {status === "deployed" && (
          <div className="text-sm text-neutral-500">
            Create a project and deploy it to see it in this section.
          </div>
        )}
        
        {status === "archived" && (
          <div className="text-sm text-neutral-500">
            Archive projects you no longer need but want to keep for reference.
          </div>
        )}
      </div>
    </div>
  );
};

export default NotAvailablePage; 
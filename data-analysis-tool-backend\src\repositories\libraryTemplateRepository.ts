import { PrismaClient, LibraryTemplate, Sector, Status, Prisma } from "@prisma/client";
import { prisma } from "../utils/prisma";

class LibraryTemplateRepository {
  async findByName(
    name: string,
    userId: number
  ): Promise<LibraryTemplate | null> {
    return await prisma.libraryTemplate.findUnique({
      where: {
        name_userId: {
          name,
          userId,
        },
      },
    });
  }

  async findById(id: number) {
    return await prisma.libraryTemplate.findUnique({
      where: { id },
    });
  }

  async findLibraryTemplateByIdAndUser(
    id: number,
    userId: number
  ): Promise<Partial<LibraryTemplate> | null> {
    return await prisma.libraryTemplate.findFirst({
      where: {
        id,
        userId,
      },
      include: {
        user: true,
        libraryQuestions: true,
      },
    });
  }

  async findAll(id: number): Promise<LibraryTemplate[]> {
    return await prisma.libraryTemplate.findMany({
      where: {
        userId: id,
      },
      include: {
        user: true,
      },
    });
  }

  async create(libraryTemplateData: {
    name: string;
    description: string;
    sector: Sector;
    userId: number;
    country?: string;
  }): Promise<LibraryTemplate> {
    const { name, description, sector, userId, country } = libraryTemplateData;

    return await prisma.libraryTemplate.create({
      data: {
        name,
        description,
        sector,
        userId,
        country,
      },
    });
  }

  async updateById(
    id: number,
    updateData: {
      name?: string;
      description?: string;
      sector?: Sector;
      country?: string;
    }
  ): Promise<Partial<LibraryTemplate> | null> {
    const data: {
      name?: string;
      description?: string;
      sector?: Sector;
      country?: string;
    } = {};

    if (updateData.name !== undefined) data.name = updateData.name;
    if (updateData.description !== undefined)
      data.description = updateData.description;
    if (updateData.sector !== undefined) data.sector = updateData.sector;
    if (updateData.country !== undefined) data.country = updateData.country;

    return await prisma.libraryTemplate.update({
      where: { id },
      data,
    });
  }

  async deleteLibraryTemplate(id: number): Promise<LibraryTemplate> {
    return await prisma.libraryTemplate.delete({
      where: { id },
    });
  }

  async isOwner(userId: number, templateId: number): Promise<boolean> {
    const template = await prisma.libraryTemplate.findFirst({
      where: {
        id: templateId,
        userId: userId,
      },
    });

    return template !== null;
  }

  async findLibraryWithQuestionGroupAndQuestion(
    id: number
  ): Promise<LibraryTemplate | null> {
    return await prisma.libraryTemplate.findUnique({
      where: { id },
      include: {
        LibraryTemplateQuestionGroup: {
          include: {
            libraryQuestions: {
              include: {
                questionOptions: true,
                questionConditions: true,
              },
              orderBy: { position: "asc" },
            },
            subGroups: {
              include: {
                libraryQuestions: {
                  include: {
                    questionConditions: true,
                    questionOptions: true,
                  },
                  orderBy: { position: "asc" },
                },
              },
            },
          },
          orderBy: { order: "asc" },
        },
        libraryQuestions: {
          where: { libraryTemplateQuestionGroupId: null },
          include: {
            questionOptions: true,
            questionConditions: true,
          },
          orderBy: { position: "asc" },
        },
      },
    });
  }

   async deleteMultipleLibraryTemplate(ids: number[]): Promise<Prisma.BatchPayload> {
      return await prisma.libraryTemplate.deleteMany({
        where: { id: { in: ids } },
      });
    }
}

export default new LibraryTemplateRepository();

"use client";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { Briefcase, Globe, ShieldCheck, Eye, EyeOff } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import React, { useEffect, useState } from "react";
import { FieldValues, useForm } from "react-hook-form";
import { z } from "zod";
import { AxiosError } from "axios";
import { useDispatch } from "react-redux";
import { showNotification } from "@/redux/slices/notificationSlice";
import { Select } from "@/components/general/Select";
import countries from "@/constants/countryNames.json";
import { SectorLabelMap } from "@/constants/sectors";
import { OrganizationTypeLabelMap } from "@/constants/organizationType";
import { labelToKey } from "@/lib/labelToKey";
import axios from "@/lib/axios";

const signUpSchema = z
  .object({
    name: z.string().min(1, "Full name is required"),
    email: z
      .string()
      .min(1, "Email is required")
      .email("Please enter a valid email address"),
    password: z
      .string()
      .min(1, "Password is required")
      .min(8, "Password must be at least 8 characters")
      .max(32, "Password must be less than 32 characters")
      .regex(/[A-Z]/, "Password must contain at least one uppercase letter")
      .regex(/[a-z]/, "Password must contain at least one lowercase letter")
      .regex(/[0-9]/, "Password must contain at least one number")
      .regex(
        /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/,
        "Password must contain at least one special character"
      ),
    confirmPassword: z.string().min(1, "Please confirm your password"),
    country: z.string().min(1, "Please select a country"),
    sector: z.string().min(1, "Please select a sector"),
    organizationType: z.string().min(1, "Please select an organization type"),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords do not match",
    path: ["confirmPassword"],
  });

type SignUpFormValues = z.infer<typeof signUpSchema>;

const page = () => {
  const {
    register,
    formState: { errors, isSubmitting, isSubmitted },
    setValue,
    handleSubmit,
    setError,
    watch,
  } = useForm<SignUpFormValues>({ resolver: zodResolver(signUpSchema) });

  // Watch password fields to determine when to show the eye button
  const passwordValue = watch("password");
  const confirmPasswordValue = watch("confirmPassword");

  useEffect(() => {
    register("country", { required: "Please select a country" });
    register("sector", { required: "Please select a sector" });
    register("organizationType", {
      required: "Please select an organization type",
    });
  }, [register]);

  const [selectedCountry, setSelectedCountry] = useState<string>("");
  const [selectedSector, setSelectedSector] = useState<string>("");
  const [selectedOrganizationType, setSelectedOrganizationType] =
    useState<string>("");

  // Password visibility states
  const [showPassword, setShowPassword] = useState<boolean>(false);
  const [showConfirmPassword, setShowConfirmPassword] =
    useState<boolean>(false);

  useEffect(() => {
    setValue("country", selectedCountry, { shouldValidate: isSubmitted });
    setValue("sector", selectedSector, { shouldValidate: isSubmitted });
    setValue("organizationType", selectedOrganizationType, {
      shouldValidate: isSubmitted,
    });
  }, [selectedCountry, selectedSector, selectedOrganizationType, setValue]);

  const router = useRouter();
  const dispatch = useDispatch();

  const onSubmit = async (data: FieldValues) => {
    try {
      await axios.post(`/users/signup`, data);
      router.push("/");
      dispatch(
        showNotification({
          message:
            "Sign-up successful! We've sent a verification email to your inbox. Please verify your email before logging in.",
          type: "success",
        })
      );
    } catch (error) {
      if (error instanceof AxiosError) {
        setError(error.response?.data.errorField, {
          message: error.response?.data.message,
        });
      } else {
        dispatch(
          showNotification({
            message:
              "An unexpected error occured while trying to sign up. Please try again",
            type: "error",
          })
        );
      }
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="flex flex-col gap-8 section w-11/12 mobile:w-4/5 tablet:w-2xl my-8 tablet:my-16">
        <div className="flex flex-col items-center gap-2">
          <ShieldCheck size={36} />
          <h1 className="text-2xl tablet:text-3xl font-semibold text-center">
            Create your account
          </h1>
          <p className="text-neutral-700 text-center">
            Get started with data analysis tool
          </p>
        </div>
        <form className="flex flex-col gap-4" onSubmit={handleSubmit(onSubmit)}>
          <div className="group label-input-group">
            <label htmlFor="name" className="label-text">
              Full Name
            </label>
            <input
              {...register("name")}
              id="name"
              type="text"
              placeholder="Enter your full name"
              className="input-field"
            />
            {errors.name && (
              <p className="text-red-500 text-sm">{`${errors.name.message}`}</p>
            )}
          </div>
          <div className="group label-input-group">
            <label htmlFor="email" className="label-text">
              Email
            </label>
            <input
              {...register("email")}
              id="email"
              type="email"
              placeholder="Enter your email address"
              className="input-field"
            />
            {errors.email && (
              <p className="text-red-500 text-sm">{`${errors.email.message}`}</p>
            )}
          </div>
          <div className="group label-input-group">
            <label htmlFor="password" className="label-text">
              Password
            </label>
            <div className="relative">
              <input
                {...register("password")}
                id="password"
                type={showPassword ? "text" : "password"}
                placeholder="Enter your password"
                className="input-field w-full pr-10"
              />
              {passwordValue && passwordValue.length > 0 && (
                <button
                  type="button"
                  tabIndex={-1}
                  className="absolute right-2 top-1/2 -translate-y-1/2 text-gray-500"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                  <span className="sr-only">
                    {showPassword ? "Hide" : "Show"} password
                  </span>
                </button>
              )}
            </div>
            {errors.password && (
              <p className="text-red-500 text-sm">{`${errors.password.message}`}</p>
            )}
          </div>
          <div className="group label-input-group">
            <label htmlFor="confirm-password" className="label-text">
              Confirm Password
            </label>
            <div className="relative">
              <input
                {...register("confirmPassword")}
                id="confirm-password"
                type={showConfirmPassword ? "text" : "password"}
                placeholder="Confirm your password"
                className="input-field w-full pr-10"
              />
              {confirmPasswordValue && confirmPasswordValue.length > 0 && (
                <button
                  type="button"
                  tabIndex={-1}
                  className="absolute right-2 top-1/2 -translate-y-1/2 text-gray-500"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                >
                  {showConfirmPassword ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                  <span className="sr-only">
                    {showConfirmPassword ? "Hide" : "Show"} password
                  </span>
                </button>
              )}
            </div>
            {errors.confirmPassword && (
              <p className="text-red-500 text-sm">{`${errors.confirmPassword.message}`}</p>
            )}
          </div>

          <div className="grid grid-cols-1 tablet:grid-cols-2 gap-4">
            <div className="label-input-group group">
              <label htmlFor="country" className="label-text">
                <Globe size={16} /> Country
              </label>
              <Select
                id={`country`}
                options={countries}
                value={selectedCountry}
                onChange={setSelectedCountry}
              />
              {errors.country && (
                <p className="text-red-500 text-sm">{`${errors.country.message}`}</p>
              )}
            </div>
            {/* Sector */}
            <div className="label-input-group group">
              <label htmlFor="sector" className="label-text">
                <Briefcase size={16} /> Sector
              </label>
              <Select
                id={`sector`}
                options={Object.values(SectorLabelMap)} // Display labels
                value={
                  selectedSector && SectorLabelMap[selectedSector]
                    ? SectorLabelMap[selectedSector]
                    : "Select an option"
                }
                onChange={(label) => {
                  const selectedKey = labelToKey(label, SectorLabelMap);
                  setSelectedSector(selectedKey ?? ""); // Set the enum key for storage
                }}
              />
              {errors.sector && (
                <p className="text-red-500 text-sm">{`${errors.sector.message}`}</p>
              )}
            </div>
            {/* organization type */}
            <div className="label-input-group group">
              <label htmlFor="organizationType" className="label-text">
                <Briefcase size={16} /> Organization Type
              </label>
              <Select
                id={`organizationType`}
                options={Object.values(OrganizationTypeLabelMap)} // Display organization type labels
                value={
                  selectedOrganizationType &&
                  OrganizationTypeLabelMap[selectedOrganizationType]
                    ? OrganizationTypeLabelMap[selectedOrganizationType]
                    : "Select an option"
                }
                onChange={(label) => {
                  const selectedKey = labelToKey(
                    label,
                    OrganizationTypeLabelMap
                  );
                  setSelectedOrganizationType(selectedKey ?? ""); // Set the enum key for organization type
                }}
              />
              {errors.organizationType && (
                <p className="text-red-500 text-sm">{`${errors.organizationType.message}`}</p>
              )}
            </div>
          </div>
          {/* country */}

          <button type="submit" className="btn-primary" disabled={isSubmitting}>
            {isSubmitting ? (
              <span className="flex items-center gap-2">
                Signing up{" "}
                <div className="size-4 rounded-full border-x-2 animate-spin"></div>
              </span>
            ) : (
              "Sign up"
            )}
          </button>
        </form>
        <div className="text-neutral-700 flex items-center gap-2">
          <span>Already have an account?</span>
          <Link
            href="/"
            className="font-medium hover:text-neutral-900 duration-300"
          >
            Sign in
          </Link>
        </div>
      </div>
    </div>
  );
};

export default page;

import { Question } from "@/types/formBuilder";

/**
 * Utility functions for handling conditional questions logic
 */

/**
 * Get the next question ID based on the selected option
 */
export const getNextQuestionId = (
  question: Question,
  selectedValue: string | string[]
): number | null => {
  if (!question.questionOptions || question.questionOptions.length === 0) {
    return null;
  }

  // For selectone, selectedValue is a string
  if (question.inputType === "selectone" && typeof selectedValue === "string") {
    const selectedOption = question.questionOptions.find(
      (option) => option.label === selectedValue
    );
    return selectedOption?.nextQuestionId || null;
  }

  // For selectmany, selectedValue is an array - return the first next question found
  if (question.inputType === "selectmany" && Array.isArray(selectedValue)) {
    for (const value of selectedValue) {
      const selectedOption = question.questionOptions.find(
        (option) => option.label === value
      );
      if (selectedOption?.nextQuestionId) {
        return selectedOption.nextQuestionId;
      }
    }
  }

  return null;
};

/**
 * Get all possible next question IDs for a question (for dependency tracking)
 */
export const getAllNextQuestionIds = (question: Question): number[] => {
  if (!question.questionOptions || question.questionOptions.length === 0) {
    return [];
  }

  return question.questionOptions
    .map((option) => option.nextQuestionId)
    .filter((id): id is number => id !== null && id !== undefined);
};

/**
 * Determine which questions should be visible based on current answers
 */
export const getVisibleQuestions = (
  allQuestions: Question[],
  answers: Record<string, any>
): Question[] => {
  const visibleQuestionIds = new Set<number>();
  const conditionalQuestionIds = new Set<number>();

  // First, collect all questions that are conditional (have a parent question)
  allQuestions.forEach((question) => {
    const nextQuestionIds = getAllNextQuestionIds(question);
    nextQuestionIds.forEach((id) => conditionalQuestionIds.add(id));
  });

  // Start with all non-conditional questions (questions that are not triggered by other questions)
  allQuestions.forEach((question) => {
    if (!conditionalQuestionIds.has(question.id)) {
      visibleQuestionIds.add(question.id);
    }
  });

  // Process answers to determine which conditional questions should be visible
  Object.entries(answers).forEach(([questionIdStr, answer]) => {
    const questionId = parseInt(questionIdStr);
    const question = allQuestions.find((q) => q.id === questionId);

    if (question && answer) {
      const nextQuestionId = getNextQuestionId(question, answer);
      if (nextQuestionId) {
        visibleQuestionIds.add(nextQuestionId);
      }
    }
  });

  // Return questions in their original order, filtered by visibility
  return allQuestions.filter((question) => visibleQuestionIds.has(question.id));
};

/**
 * Check if a question should be visible based on current answers
 */
export const isQuestionVisible = (
  question: Question,
  allQuestions: Question[],
  answers: Record<string, any>
): boolean => {
  const visibleQuestions = getVisibleQuestions(allQuestions, answers);
  return visibleQuestions.some((q) => q.id === question.id);
};

/**
 * Get questions that depend on a specific question
 */
export const getDependentQuestions = (
  parentQuestionId: number,
  allQuestions: Question[]
): Question[] => {
  const parentQuestion = allQuestions.find((q) => q.id === parentQuestionId);
  if (!parentQuestion) return [];

  const nextQuestionIds = getAllNextQuestionIds(parentQuestion);
  return allQuestions.filter((q) => nextQuestionIds.includes(q.id));
};

/**
 * Check if a question is a follow-up question (has a parent question)
 */
export const isFollowUpQuestion = (
  questionId: number,
  allQuestions: Question[]
): boolean => {
  return allQuestions.some((question) => {
    const nextQuestionIds = getAllNextQuestionIds(question);
    return nextQuestionIds.includes(questionId);
  });
};

/**
 * Get the parent question for a follow-up question
 */
export const getParentQuestion = (
  questionId: number,
  allQuestions: Question[]
): Question | null => {
  return (
    allQuestions.find((question) => {
      const nextQuestionIds = getAllNextQuestionIds(question);
      return nextQuestionIds.includes(questionId);
    }) || null
  );
};

/**
 * Get questions in nested structure for rendering
 * Returns questions grouped by parent-child relationships, maintaining order
 */
export const getNestedQuestions = (
  allQuestions: Question[],
  answers: Record<string, any>
): Array<{
  question: Question;
  isVisible: boolean;
  isFollowUp: boolean;
  parentQuestion?: Question;
  followUps: Array<{
    question: Question;
    isVisible: boolean;
  }>;
}> => {
  const visibleQuestions = getVisibleQuestions(allQuestions, answers);
  const visibleQuestionIds = new Set(visibleQuestions.map((q) => q.id));

  // Get all parent questions (questions that are not follow-ups themselves)
  const parentQuestions = allQuestions.filter(
    (question) => !isFollowUpQuestion(question.id, allQuestions)
  );

  // Sort parent questions by position to maintain order
  const sortedParentQuestions = parentQuestions.sort(
    (a, b) => a.position - b.position
  );

  return sortedParentQuestions
    .map((parentQuestion) => {
      const followUpQuestions = getDependentQuestions(
        parentQuestion.id,
        allQuestions
      );
      const sortedFollowUps = followUpQuestions.sort(
        (a, b) => a.position - b.position
      );

      return {
        question: parentQuestion,
        isVisible: visibleQuestionIds.has(parentQuestion.id),
        isFollowUp: false,
        followUps: sortedFollowUps.map((followUp) => ({
          question: followUp,
          isVisible: visibleQuestionIds.has(followUp.id),
        })),
      };
    })
    .filter(
      (group) => group.isVisible || group.followUps.some((f) => f.isVisible)
    );
};

/**
 * Clean up answers for questions that are no longer visible
 */
export const cleanupHiddenAnswers = (
  answers: Record<string, any>,
  visibleQuestions: Question[]
): Record<string, any> => {
  const visibleQuestionIds = new Set(visibleQuestions.map((q) => q.id));
  const cleanedAnswers: Record<string, any> = {};

  Object.entries(answers).forEach(([questionId, answer]) => {
    if (visibleQuestionIds.has(parseInt(questionId))) {
      cleanedAnswers[questionId] = answer;
    }
  });

  return cleanedAnswers;
};

/**
 * Validate that all visible required questions have answers
 */
export const validateVisibleQuestions = (
  visibleQuestions: Question[],
  answers: Record<string, any>
): Record<string, string> => {
  const errors: Record<string, string> = {};

  visibleQuestions.forEach((question) => {
    if (question.isRequired) {
      const value = answers[question.id];
      if (
        (typeof value === "string" && !value.trim()) ||
        (Array.isArray(value) && value.length === 0) ||
        value === undefined ||
        value === null
      ) {
        errors[question.id] = `${question.label} is required`;
      }
    }
  });

  return errors;
};

"use client";

import { BsRocketTakeoffFill } from "react-icons/bs";
import { MdEditDocument } from "react-icons/md";
import { FaArchive } from "react-icons/fa";
import { LuLibrary } from "react-icons/lu";
import { MdFileCopy } from "react-icons/md";
import axios from "axios";
import { useQuery } from "@tanstack/react-query";
import { Project } from "@/types";
import { fetchProjects } from "@/lib/api/projects";
import { useAuth } from "@/hooks/useAuth";

type SubItem = {
  id: number;
  label: string;
  href: string;
};

type NavItem = {
  id: number;
  icon: React.ElementType;
  label: string;
  count: number;
  href: string;
  category: "project" | "library";
  subItems?: SubItem[];
};

const useNavItems = () => {
  const { user } = useAuth();

  const {
    data: projectsData,
    isLoading: projectsLoading,
    isError: projectsError,
  } = useQuery<Project[]>({
    queryKey: ["projects", user?.id],
    queryFn: fetchProjects,
    enabled: !!user?.id,
  });

  // Initialize separate arrays for each status
  let deployedProjects: Project[] = [];
  let draftStatusProjects: Project[] = [];
  let archivedProjects: Project[] = [];

  if (!projectsLoading && projectsData) {
    deployedProjects = projectsData.filter((projectData) => projectData.status === "deployed");
    draftStatusProjects = projectsData.filter((projectData) => projectData.status === "draft");
    archivedProjects = projectsData.filter((projectData) => projectData.status === "archived");
  }

  const navItems: NavItem[] = [
    {
      id: 1,
      icon: BsRocketTakeoffFill,
      label: "Deployed",
      count: deployedProjects.length || 0,
      href: "/dashboard/deployed",
      category: "project",
    },
    {
      id: 2,
      icon: MdEditDocument,
      label: "Draft",
      count: draftStatusProjects.length || 0,
      href: "/dashboard/draft",
      category: "project",
    },
    {
      id: 3,
      icon: FaArchive,
      label: "Archived",
      count: archivedProjects.length || 0,
      href: "/dashboard/archived",
      category: "project",
    },
    {
      id: 4,
      icon: LuLibrary,
      label: "My Library",
      count: 0,
      href: "/library",
      category: "library",
    },
    {
      id: 5,
      icon: MdFileCopy,
      label: "Collections",
      count: 0,
      href: "/library/#",
      category: "library",
    },
  ];

  return { navItems, deployedProjects, draftStatusProjects, archivedProjects };
};

export default useNavItems;

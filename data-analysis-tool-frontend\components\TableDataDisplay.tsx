"use client";

import React, { useState, useEffect } from "react";
import axios from "@/lib/axios";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

interface TableDataDisplayProps {
  tableData: any;
  title: string;
}

const TableDataDisplay: React.FC<TableDataDisplayProps> = ({
  tableData,
  title,
}) => {
  const [loading, setLoading] = useState(false);
  const [processedData, setProcessedData] = useState<any>({
    columns: [],
    rows: [],
    data: new Map<string, Map<string, string>>(),
  });
  const [tableStructure, setTableStructure] = useState<any>(null);

  useEffect(() => {
    processTableData();
  }, [tableData]);

  // Function to fetch table structure (columns and rows) from the backend
  const fetchTableStructure = async (questionId: number) => {
    try {
      // First try the tables endpoint
      try {
        const { data } = await axios.get(`/tables/${questionId}`);
        console.log("Table structure response:", data);
        if (data && data.data) {
          return data.data.question || data.data;
        }
      } catch (err) {
        console.log("Error fetching from /tables/ endpoint:", err);
      }

      // If that fails, try the table-questions endpoint
      try {
        const { data } = await axios.get(`/table-questions/${questionId}`);
        console.log("Table questions response:", data);
        if (data && data.data) {
          return data.data;
        }
      } catch (err) {
        console.log("Error fetching from /table-questions/ endpoint:", err);
      }

      return null;
    } catch (error) {
      console.error("Error fetching table structure:", error);
      return null;
    }
  };

  const processTableData = async () => {
    if (!tableData || !Array.isArray(tableData)) {
      console.log("No valid table data provided");
      return;
    }

    setLoading(true);
    try {
      // Extract the question ID from the first item if available
      let questionId = null;
      if (tableData.length > 0) {
        const firstItem = tableData[0];
        // Try to parse the value to extract the question ID
        try {
          const parsedValue = JSON.parse(firstItem.value);
          if (parsedValue && parsedValue.columnId) {
            questionId = parsedValue.question?.id;
          }
        } catch (e) {
          // If parsing fails, try to extract from the raw value
          const match = firstItem.value.match(/columnId['":\s]+(\d+)/);
          if (match && match[1]) {
            questionId = parseInt(match[1], 10);
          }
        }
      }

      console.log("Extracted question ID:", questionId);

      // Process the table data
      const items = tableData.flatMap((item) => {
        try {
          // Try to parse the value as JSON
          const parsedItems = JSON.parse(item.value);
          if (Array.isArray(parsedItems)) {
            return parsedItems;
          }
          return [parsedItems];
        } catch (e) {
          // If parsing fails, return the raw value
          return [{ value: item.value }];
        }
      });

      console.log("Processed items:", items);

      // Create maps for column and row names
      const columnMap = new Map<number, string>();
      const rowMap = new Map<number, string>();

      // If we have a question ID, try to fetch the table structure
      if (questionId) {
        const structure = await fetchTableStructure(questionId);
        if (structure && structure.tableColumns && structure.tableRows) {
          // Store the table structure for later use
          setTableStructure(structure);

          structure.tableColumns.forEach((col: any) => {
            columnMap.set(col.id, col.columnName);
          });

          structure.tableRows.forEach((row: any) => {
            rowMap.set(row.id, row.rowsName);
          });
        }
      }

      // Process the items to extract column and row information
      const processedItems = items.map((item) => {
        let columnName = "";
        let rowName = "";

        // Get column name
        if (item.columnName) {
          columnName = item.columnName;
        } else if (item.column && item.column.columnName) {
          columnName = item.column.columnName;
        } else if (item.columnId && columnMap.has(item.columnId)) {
          columnName = columnMap.get(item.columnId) || String(item.columnId);
        } else if (item.columnId) {
          columnName = String(item.columnId);
        }

        // Get row name
        if (item.rowsName) {
          rowName = item.rowsName;
        } else if (item.row && item.row.rowsName) {
          rowName = item.row.rowsName;
        } else if (item.rowsId && rowMap.has(item.rowsId)) {
          rowName = rowMap.get(item.rowsId) || String(item.rowsId);
        } else if (item.rowsId) {
          rowName = String(item.rowsId);
        }

        return {
          column: columnName,
          row: rowName,
          value: item.value !== undefined ? item.value : "",
        };
      });

      // Create a structured table
      const rowsData = new Map<string, Map<string, string>>();
      const columnsSet = new Set<string>();

      // Collect all unique rows and columns
      processedItems.forEach((item) => {
        if (item.column) columnsSet.add(String(item.column));
        if (item.row) {
          if (!rowsData.has(String(item.row))) {
            rowsData.set(String(item.row), new Map<string, string>());
          }
          if (item.column) {
            rowsData
              .get(String(item.row))
              ?.set(String(item.column), String(item.value));
          }
        }
      });

      // Convert to arrays for rendering
      const uniqueColumns = Array.from(columnsSet);
      const uniqueRows = Array.from(rowsData.keys());

      setProcessedData({
        columns: uniqueColumns,
        rows: uniqueRows,
        data: rowsData,
      });
    } catch (error) {
      console.error("Error processing table data:", error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"></div>
        <span className="ml-2 text-neutral-600">Loading table data...</span>
      </div>
    );
  }

  // Process table columns to create a hierarchical structure if tableStructure is provided
  const processTableColumns = () => {
    if (!tableStructure?.tableColumns) {
      return {
        columns: [],
        columnMap: new Map<number, any>(),
      };
    }

    // Create a map of column IDs to their corresponding Column objects
    const columnMap = new Map<number, any>();

    // First pass: create all columns
    tableStructure.tableColumns.forEach((col: any) => {
      const column = {
        id: col.id,
        columnName: col.columnName,
        level: 0, // We'll calculate the correct level in the next pass
        parentId: col.parentColumnId,
      };

      // Add to map for later reference
      columnMap.set(col.id, column);
    });

    // Calculate the correct level for each column based on its hierarchy
    const calculateColumnLevel = (
      columnId: number,
      visited = new Set<number>()
    ): number => {
      // Prevent infinite loops in case of circular references
      if (visited.has(columnId)) {
        console.warn(`Circular reference detected for column ID ${columnId}`);
        return 0;
      }

      visited.add(columnId);

      const column = columnMap.get(columnId);
      if (!column) return 0;

      // If this is a top-level column (no parent), its level is 0
      if (!column.parentId) return 0;

      // Otherwise, its level is 1 + the level of its parent
      return 1 + calculateColumnLevel(column.parentId as number, visited);
    };

    // Update the level for each column
    tableStructure.tableColumns.forEach((col: any) => {
      const column = columnMap.get(col.id);
      if (column) {
        column.level = calculateColumnLevel(col.id);
      }
    });

    // Get top-level columns
    const parentColumns = Array.from(columnMap.values()).filter(
      (col) => !col.parentId
    );

    return {
      columns: parentColumns,
      columnMap,
    };
  };

  const { columns: hierarchicalColumns, columnMap } = processTableColumns();
  const useParentChildColumns =
    tableStructure?.tableColumns && tableStructure.tableColumns.length > 0;

  return (
    <div className="overflow-auto max-h-[70vh]">
      <Table className="border-collapse border border-amber-700">
        <TableHeader className="bg-amber-100">
          {useParentChildColumns && hierarchicalColumns.length > 0 ? (
            // For tables with parent-child columns
            (() => {
              const maxLevel = Math.max(
                ...(tableStructure?.tableColumns?.map((col: any) => {
                  const column = columnMap?.get(col.id);
                  return column?.level || 0;
                }) || [0]),
                0
              );
              console.log(
                `Maximum nesting level in TableDataDisplay: ${maxLevel}`
              );

              // Create header rows for each level
              const headerRows = [];

              // First row with the corner cell
              headerRows.push(
                <TableRow key="header-row-0">
                  <TableHead
                    className="px-3 py-2 text-xs font-medium text-gray-700 uppercase tracking-wider border border-amber-700"
                    rowSpan={maxLevel + 1}
                  >
                    {title || "Table"}
                  </TableHead>

                  {/* Render top-level (parent) columns */}
                  {hierarchicalColumns.map((parentCol: any) => {
                    // Count all descendants at all levels
                    const getAllDescendants = (colId: number): any[] => {
                      const directChildren =
                        tableStructure?.tableColumns
                          ?.filter((c: any) => c.parentColumnId === colId)
                          .map((c: any) => ({
                            id: c.id,
                            columnName: c.columnName,
                            parentId: c.parentColumnId,
                            level: 0, // Placeholder, not used here
                          })) || [];

                      let allDescendants = [...directChildren];

                      directChildren.forEach((child: any) => {
                        allDescendants = [
                          ...allDescendants,
                          ...getAllDescendants(child.id),
                        ];
                      });

                      return allDescendants;
                    };

                    const descendants = getAllDescendants(parentCol.id);
                    const leafNodes = descendants.filter(
                      (d: any) =>
                        !tableStructure?.tableColumns?.some(
                          (c: any) => c.parentColumnId === d.id
                        )
                    );

                    // If no descendants, it's a leaf node itself
                    const colSpanCount =
                      descendants.length > 0 ? leafNodes.length || 1 : 1;

                    return (
                      <TableHead
                        key={String(parentCol.id)}
                        colSpan={colSpanCount}
                        className="px-3 py-2 text-xs font-medium text-gray-700 uppercase tracking-wider border border-amber-700"
                      >
                        {parentCol.columnName}
                      </TableHead>
                    );
                  })}
                </TableRow>
              );

              // Create header rows for each additional level
              for (let level = 1; level <= maxLevel; level++) {
                headerRows.push(
                  <TableRow key={`header-row-${level}`}>
                    {/* For each level, we need to find columns at that level */}
                    {(() => {
                      // Get columns at the previous level
                      const columnsAtPrevLevel =
                        tableStructure?.tableColumns?.filter((col: any) => {
                          const column = columnMap?.get(col.id);
                          return column?.level === level - 1;
                        }) || [];

                      return columnsAtPrevLevel.map((col: any) => {
                        // Get direct children of this column
                        const children =
                          tableStructure?.tableColumns?.filter(
                            (c: any) => c.parentColumnId === col.id
                          ) || [];

                        // If no children, render an empty cell to maintain alignment
                        if (children.length === 0) {
                          return (
                            <TableHead
                              key={`empty-${col.id}`}
                              className="px-3 py-2 text-xs font-medium text-gray-700 uppercase tracking-wider border border-amber-700"
                            >
                              {/* Empty cell to maintain column alignment */}
                            </TableHead>
                          );
                        }

                        // Render each child with appropriate colspan
                        return children.map((child: any) => {
                          // Calculate how many leaf nodes are under this child
                          const getAllDescendants = (colId: number): any[] => {
                            const directChildren =
                              tableStructure?.tableColumns?.filter(
                                (c: any) => c.parentColumnId === colId
                              ) || [];

                            let allDescendants = [...directChildren];

                            directChildren.forEach((child: any) => {
                              allDescendants = [
                                ...allDescendants,
                                ...getAllDescendants(child.id),
                              ];
                            });

                            return allDescendants;
                          };

                          const descendants = getAllDescendants(child.id);
                          const leafNodes = descendants.filter(
                            (d: any) =>
                              !tableStructure?.tableColumns?.some(
                                (c: any) => c.parentColumnId === d.id
                              )
                          );

                          // If no descendants, it's a leaf node itself
                          const colSpanCount =
                            descendants.length > 0 ? leafNodes.length || 1 : 1;

                          return (
                            <TableHead
                              key={child.id}
                              colSpan={colSpanCount}
                              className="px-3 py-2 text-xs font-medium text-gray-700 uppercase tracking-wider border border-amber-700"
                            >
                              {child.columnName}
                            </TableHead>
                          );
                        });
                      });
                    })()}
                  </TableRow>
                );
              }

              return headerRows;
            })()
          ) : (
            // Standard table format
            <TableRow>
              <TableHead className="px-3 py-2 text-xs font-medium text-gray-700 uppercase tracking-wider border border-amber-700">
                {title || "Table"}
              </TableHead>
              {processedData.columns.map((column: string, index: number) => (
                <TableHead
                  key={index}
                  className="px-3 py-2 text-xs font-medium text-gray-700 uppercase tracking-wider border border-amber-700"
                >
                  {column}
                </TableHead>
              ))}
            </TableRow>
          )}
        </TableHeader>
        <TableBody>
          {processedData.rows.map((row: string, rowIndex: number) => (
            <TableRow key={rowIndex} className="bg-white">
              <TableCell className="px-3 py-2 text-xs font-medium border border-amber-700 bg-amber-50">
                {row}
              </TableCell>
              {processedData.columns.map((column: string, colIndex: number) => (
                <TableCell
                  key={colIndex}
                  className="px-3 py-2 text-xs border border-amber-700"
                >
                  {String(processedData.data.get(row)?.get(column) || "")}
                </TableCell>
              ))}
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
};

export default TableDataDisplay;

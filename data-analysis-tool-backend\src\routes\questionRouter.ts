import express, { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { authenticate } from "../middleware/auth";
import {
  createQuestion,
  updateQuestion,
  getAllQuestion,
  deleteQuestion,
  duplicateQuestion,
  updateQuestionPositions,
} from "../controllers/questionController";
import { checkPermission } from "../middleware/checkPermission";

const router = express.Router();

// Update multiple question positions (for drag and drop reordering)
// This route must come BEFORE /:id to avoid conflicts
router.patch(
  "/positions",
  (req, res, next) => {
    console.log("Route /positions hit with:", {
      method: req.method,
      url: req.url,
      body: req.body,
      query: req.query,
    });
    next();
  },
  authenticate,
  checkPermission([
    "manageProject",
    "editForm",
  ]) as unknown as express.RequestHandler,
  updateQuestionPositions as unknown as RequestHandler
);

// Create a question for a project
router.post(
  "/:projectId",
  authenticate,
  checkPermission([
    "manageProject",
    "editForm",
  ]) as unknown as express.RequestHandler,
  createQuestion
);

// Update a specific question
router.patch(
  "/:id",
  authenticate,
  checkPermission([
    "manageProject",
    "editForm",
  ]) as unknown as express.RequestHandler,
  updateQuestion
);

// Get all questions for a project (using path parameter)
router.get(
  "/:projectId",
  authenticate,
  checkPermission([
    "manageProject",
    "viewForm",
    "editForm",
  ]) as unknown as express.RequestHandler,
  getAllQuestion as unknown as RequestHandler
);

// Get all questions for a project (using query parameter)
router.get("/", authenticate, getAllQuestion as unknown as RequestHandler);

// Delete all specific question for a project
router.delete(
  "/:id",
  authenticate,
  checkPermission([
    "manageProject",
    "editForm",
  ]) as unknown as express.RequestHandler,

  deleteQuestion as unknown as RequestHandler
);

router.post(
  "/duplicate/:id",
  authenticate,
  checkPermission([
    "manageProject",
    "editForm",
  ]) as unknown as express.RequestHandler,

  duplicateQuestion as unknown as RequestHandler
);

export default router;

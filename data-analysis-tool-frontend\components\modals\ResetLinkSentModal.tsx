import React from "react";
import <PERSON><PERSON> from "./Modal";
import { ArrowLeft, Mail, ShieldCheck } from "lucide-react";
import Link from "next/link";

const ResetLinkSentModal = ({
  email,
  showModal,
  setShowModal,
}: {
  email: string;
  showModal: boolean;
  setShowModal: React.Dispatch<React.SetStateAction<boolean>>;
}) => {
  const steps = [
    "Check your email inbox.",
    "Look for an email from our company",
    "Click on the reset link in the email",
    "Follow the instructions to create a new password",
  ];

  return (
    <Modal
      isOpen={showModal}
      onClose={() => setShowModal(false)}
      className="flex flex-col gap-8"
    >
      <div className="flex flex-col items-center gap-2">
        <ShieldCheck size={36} />
        <h1 className="text-2xl tablet:text-3xl font-semibold text-center">
          Check your email
        </h1>
        <p className="text-neutral-700 text-center">
          We've sent a password reset link to {email}
        </p>
      </div>
      <div className="rounded-md p-4 bg-neutral-200 text-neutral-700 flex flex-col gap-2">
        <span className="flex items-center gap-2 text-lg font-medium">
          <Mail size={18} /> What to do next:
        </span>
        <ol>
          {steps.map((step, index) => (
            <li key={index}>{`${index + 1}. ${step}`}</li>
          ))}
        </ol>
      </div>
      <Link
        href="/"
        className="text-neutral-700 self-center flex items-center gap-2"
      >
        <ArrowLeft size={16} /> Back to signin page
      </Link>
    </Modal>
  );
};

export { ResetLinkSentModal };

// src/validators/answerValidator.js

import { InputType } from "@prisma/client";
import { z } from "zod";

// Zod schema for validating the Answer model
export const createAnswerSchema = z
  .object({
    submissionId: z.number(),
    questionId: z.number(),
    value: z.any(),
    imageUrl: z.string().optional(),
    isOtherOption: z.boolean().optional(),
    answerType: z.nativeEnum(InputType),
    questionOptionId: z.any(), // will be refined below
  })
  .superRefine((data, ctx) => {
    const { answerType, questionOptionId } = data;

    if (answerType === "selectmany") {
      if (!Array.isArray(questionOptionId)) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          path: ["questionOptionId"],
          message: "Expected array of questionOptionIds for selectmany",
        });
      }
    } else {
      if (Array.isArray(questionOptionId)) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          path: ["questionOptionId"],
          message: "Expected a single questionOptionId",
        });
      } else if (
        questionOptionId !== undefined &&
        typeof questionOptionId !== "number"
      ) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          path: ["questionOptionId"],
          message: "questionOptionId must be a number",
        });
      }
    }
  });

export const createMultipleAnswerSchema = z
  .object({
    projectId: z.number(),
    status: z.string().optional(),
    deviceInfo: z.string().optional(),
    loginRequired: z.boolean().optional(),
    location: z.string().optional(),
    metadata: z.record(z.any()).optional(),
    startedAt: z.coerce.date().optional(),
    questionId: z.number(),
    value: z.any(),
    imageUrl: z.string().optional(),
    isOtherOption: z.boolean().optional(),
    answerType: z.nativeEnum(InputType),
    questionOptionId: z.any(), // will be refined below
  })
  .superRefine((data, ctx) => {
    const { answerType, questionOptionId, value } = data;

    if (answerType === "selectmany") {
      if (!Array.isArray(questionOptionId)) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          path: ["questionOptionId"],
          message: "Expected array of questionOptionIds for selectmany",
        });
      }
    } else if (answerType === "table") {
      // For table type, questionOptionId should be undefined or null
      if (questionOptionId !== undefined && questionOptionId !== null) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          path: ["questionOptionId"],
          message: "questionOptionId should be undefined for table type",
        });
      }
      
      // Validate that value is a valid JSON string if provided
      if (value !== undefined && value !== null && value !== "") {
        try {
          if (typeof value === "string") {
            JSON.parse(value);
          }
        } catch (err) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            path: ["value"],
            message: "Table value must be a valid JSON string",
          });
        }
      }
    } else {
      if (Array.isArray(questionOptionId)) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          path: ["questionOptionId"],
          message: "Expected a single questionOptionId",
        });
      } else if (
        questionOptionId !== undefined &&
        questionOptionId !== null &&
        typeof questionOptionId !== "number"
      ) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          path: ["questionOptionId"],
          message: "questionOptionId must be a number",
        });
      }
    }
  });

export const updateAnswerSchema = z
  .object({
    id: z.number().optional(), // Required for identifying the answer to update
    value: z.any(),
    imageUrl: z.string().optional(),
    isOtherOption: z.boolean().optional(),
    answerType: z.nativeEnum(InputType),
    questionOptionId: z.any().optional(),
    formSubmissionId: z.number(),
    projectId: z.number(),
    questionId: z.number(),
  })
  .superRefine((data, ctx) => {
    const { answerType, questionOptionId } = data;

    if (answerType === "selectmany") {
      if (questionOptionId !== undefined && !Array.isArray(questionOptionId)) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          path: ["questionOptionId"],
          message: "Expected array of questionOptionIds for selectmany",
        });
      }
    } else {
      if (
        Array.isArray(questionOptionId) ||
        (questionOptionId !== undefined && typeof questionOptionId !== "number")
      ) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          path: ["questionOptionId"],
          message: "Expected a single number for questionOptionId",
        });
      }
    }
  });

export const updateMultipleAnswerSchema = z.array(updateAnswerSchema);

export type UpdateAnswerInput = z.infer<typeof updateAnswerSchema>;

export const answerArraySchema = z.array(createMultipleAnswerSchema);

export type CreateAnswerInput = z.infer<typeof createAnswerSchema>;

import { configureStore } from "@reduxjs/toolkit";
import notificationReducer from "@/redux/slices/notificationSlice";
import createProjectReducer from "@/redux/slices/createProjectSlice";
import authReducer from "@/redux/slices/authSlice";
import createLibraryReducer from "./slices/createLibrarySlice";
import createLibraryItemReducer from "./slices/createLibraryItemSlice";

export const store = configureStore({
  reducer: {
    notification: notificationReducer,
    createProject: createProjectReducer,
    auth: authReducer,
    createLibrary: createLibraryReducer,
    createLibraryItem: createLibraryItemReducer,
  },
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

@import "tailwindcss";

/* For dark mode */
@custom-variant dark (&:where(.dark, .dark *));

/* Custom themes */
@theme {
  /* Primary color*/
  --color-primary-100: #f6f2ff;
  --color-primary-200: #ddd4fe;
  --color-primary-300: #bbb1f5;
  --color-primary-400: #8f87df;
  --color-primary-500: #5a57b3;
  --color-primary-600: #36388f;
  --color-primary-700: #22286c;
  --color-primary-800: #151d49;
  --color-primary-900: #0b1126;

  /* Accent color */
  --color-accent-100: #f4fff2;
  --color-accent-200: #d2ffc8;
  --color-accent-300: #b3fd9d;
  --color-accent-400: #98f771;
  --color-accent-500: #80ed45;
  --color-accent-600: #60bb21;
  --color-accent-700: #498a10;
  --color-accent-800: #325808;
  --color-accent-900: #182603;

  /* Neutral color*/
  --color-neutral-100: #fbfafc;
  --color-neutral-200: #ecebef;
  --color-neutral-300: #dddce2;
  --color-neutral-400: #cfcdd4;
  --color-neutral-500: #c0bfc7;
  --color-neutral-600: #97969f;
  --color-neutral-700: #6e6e77;
  --color-neutral-800: #48484e;
  --color-neutral-900: #222326;

  /* Breakpoints */
  --breakpoint-mobile: 640px; /* sm */
  --breakpoint-tablet: 768px; /* md */
  --breakpoint-laptop: 1024px; /* lg */
  --breakpoint-desktop: 1280px; /* xl */
  --breakpoint-large-desktop: 1536px;
}

@layer base {
  /* default text color  */
  body {
    @apply text-neutral-900;
  }

  /* Custom Scrollbar */
  *::-webkit-scrollbar-track {
    @apply bg-transparent overflow-clip;
  }
  *::-webkit-scrollbar {
    @apply bg-transparent w-[3px];
  }
  *::-webkit-scrollbar-thumb {
    @apply bg-neutral-500 rounded-full;
  }
}

@layer components {
  /* Buttons */
  .btn-primary {
    @apply bg-primary-500 font-medium rounded-md shadow-md text-neutral-100 flex items-center justify-center gap-2 px-4 py-2 hover:bg-primary-600 cursor-pointer active:scale-95 transition-all duration-300 disabled:pointer-events-none disabled:bg-primary-400;
  }
  .btn-outline {
    @apply border border-primary-500 text-primary-500 px-4 py-2 font-medium flex items-center justify-center gap-2 rounded-md hover:bg-primary-500/10 cursor-pointer active:scale-95 transition-all duration-300;
  }
  .btn-danger {
    @apply bg-red-500 text-neutral-100 px-4 py-2 flex items-center justify-center gap-2 rounded-md font-medium hover:bg-red-600 cursor-pointer active:scale-95 transition-all duration-300 disabled:pointer-events-none disabled:bg-neutral-400;
  }

  /* Typography */

  /* Large text for heading */
  .heading-text {
    @apply text-2xl font-semibold;
  }

  .sub-heading-text {
    @apply text-xl font-semibold;
  }

  .sub-text {
    @apply text-neutral-700;
  }

  /* Form*/
  .label-input-group {
    @apply flex flex-col gap-2;
  }

  .input-field {
    @apply outline-none border border-neutral-400 focus:border-primary-500 duration-300 px-4 py-2 rounded-md placeholder:font-light;
  }

  .label-text {
    @apply group-focus-within:text-primary-500 text-neutral-700 cursor-pointer duration-300 flex items-center gap-2;
  }

  .section {
    @apply p-8 shadow-md rounded-md bg-neutral-100;
  }
}

/* for shadcn */
@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

/* Force table elements to use default display to fix calendar grid bugs */
table,
thead,
tbody,
tfoot,
tr,
th,
td {
  display: revert !important;
}

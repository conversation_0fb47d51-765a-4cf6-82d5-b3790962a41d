"use client";

import React, { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { motion, AnimatePresence } from "framer-motion";
import { RootState } from "@/redux/store";
import { hideNotification } from "@/redux/slices/notificationSlice";
import { CircleAlert, CircleCheck, TriangleAlert } from "lucide-react";

const Notification: React.FC = () => {
  const dispatch = useDispatch();
  const { message, type, visible } = useSelector(
    (state: RootState) => state.notification
  );

  useEffect(() => {
    if (visible) {
      const timer = setTimeout(() => {
        dispatch(hideNotification());
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [visible, dispatch]);

  //Different bg color for different notification types
  const bgColor =
    type === "success"
      ? "bg-green-500 hover:bg-green-600"
      : type === "warning"
      ? "bg-yellow-500 hover:bg-yellow-600"
      : "bg-red-500 hover:bg-red-600";

  const icon: React.ReactNode =
    type === "success" ? (
      <CircleCheck />
    ) : type === "warning" ? (
      <TriangleAlert />
    ) : (
      <CircleAlert />
    );

  return (
    <AnimatePresence>
      {visible && (
        <motion.div
          className={`z-50 fixed top-0 right-0 m-4 px-4 py-2 rounded font-semibold w-auto max-w-xs flex items-center gap-2 cursor-pointer ${bgColor} transition-colors duration-300`}
          onClick={() => dispatch(hideNotification())}
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.3, ease: "easeIn" }}
        >
          <span className="text-2xl">{icon}</span>
          <span className="break-words neutral-100space-normal">{message}</span>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export { Notification };

# Data Analysis Tool Backend Tests

This directory contains the test suite for the Data Analysis Tool backend API. The tests are organized to validate the functionality of controllers, repositories, and other components of the application.

## Test Structure

The tests are organized into the following structure:

```
tests/
├── controllers/         # Tests for API controllers
│   ├── userController.test.ts
│   ├── answerController.test.ts
│   ├── questionController.test.ts
│   ├── formSubmissionController.test.ts
│   └── ...
├── repositories/        # Tests for repository layer
├── validators/          # Tests for validators
└── utils/               # Tests for utility functions
```

## Running Tests

To run the tests, use the following commands:

```bash
# Run all tests
npm test

# Run tests with coverage
npm run test:coverage

# Run tests in watch mode during development
npm run test:watch

# Run specific test file
npm test -- --testPathPattern=userController.test.ts
```

## Test Coverage

Each controller test file covers the following scenarios:

1. **Success cases**: Testing the expected behavior when valid inputs are provided
2. **Validation errors**: Testing how the controller handles invalid inputs
3. **Authorization errors**: Testing access control restrictions
4. **Not found errors**: Testing behavior when requested resources don't exist
5. **Server errors**: Testing error handling for unexpected exceptions

## Mocking Strategy

The tests use <PERSON><PERSON>'s mocking capabilities to isolate the controllers from their dependencies:

- **Repository layer**: Methods are mocked to return predefined responses
- **Authentication**: User authentication is simulated through mock requests
- **External services**: Services like email are mocked to avoid actual API calls

## Writing New Tests

When adding new tests, follow these guidelines:

1. Create a new test file in the appropriate directory
2. Follow the existing patterns for mocking dependencies
3. Test all possible paths through the code, including error conditions
4. Keep test cases focused and descriptive
5. Use descriptive test names that explain the expected behavior

Example test structure:

```typescript
describe("ControllerName", () => {
  // Common setup

  describe("methodName", () => {
    // Setup specific to this method

    it("should do something when conditions are met", async () => {
      // Arrange - set up mocks and input
      // Act - call the method
      // Assert - verify the results
    });

    // More test cases for this method
  });

  // More method tests
});
```

## Continuous Integration

These tests are run automatically as part of our CI/CD pipeline, ensuring that all changes pass tests before being deployed.

"use client";

import React from "react";
import { useParams } from "next/navigation";
import { LuLibrary } from "react-icons/lu";
import { useDispatch } from "react-redux";
import { showCreateLibraryModal } from "@/redux/slices/createLibrarySlice";

const LibraryNotAvailablePage = () => {
  const dispatch = useDispatch();
  const { category } = useParams();
  
  const handleCreateLibraryItem = () => {
    dispatch(showCreateLibraryModal());
  };

  return (
    <div className="flex flex-col items-center justify-center py-16 px-4 min-h-[70vh]">
      <div className="bg-neutral-100 rounded-lg shadow-sm p-8 max-w-md w-full text-center">
        <div className="flex justify-center mb-6">
          <div className="bg-neutral-200 p-5 rounded-full">
            <LuLibrary size={50} className="text-primary-500" />
          </div>
        </div>
        <h2 className="text-2xl font-semibold text-neutral-800 mb-2">
          No Library Items
        </h2>
        <p className="text-neutral-600 mb-8">
          {category === "collections" 
            ? "You don't have any collections yet." 
            : "Your library is empty. Add items to your library to see them here."}
        </p>
        
        <button 
          onClick={handleCreateLibraryItem}
          className="btn-primary w-full"
        >
          Create New Library Item
        </button>
      </div>
    </div>
  );
};

export default LibraryNotAvailablePage; 
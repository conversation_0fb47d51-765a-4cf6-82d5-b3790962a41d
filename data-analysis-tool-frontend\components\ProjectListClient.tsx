"use client";

import React, { JS<PERSON>, useEffect, useState } from "react";
import {
  FaChevronDown,
  FaChevronUp,
  FaArchive,
  FaUserPlus,
} from "react-icons/fa";
import { RiDeleteBin6Fill } from "react-icons/ri";
import { Input } from "@/components/ui/input";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

import { Button } from "@/components/ui/button";

import { Table as ReactTable, VisibilityState } from "@tanstack/react-table";
import { ConfirmationModal } from "@/components/modals/ConfirmationModal";
import { ShareProjectModal } from "@/components/modals/ShareProjectModal";
import { Project } from "@/types";
import { GeneralTable } from "./tables/GeneralTable";
import { ProjectListColumns } from "./tables/columns/ProjectListColumns";
import {
  archiveMultipleProjects,
  deleteMultipleProjects,
} from "@/lib/api/projects";
import { useQueryClient, useMutation } from "@tanstack/react-query";
import { useDispatch } from "react-redux";
import { showNotification } from "@/redux/slices/notificationSlice";
import { useAuth } from "@/hooks/useAuth";

interface ProjectListClientProps {
  data: Project[];
}

const STORAGE_KEY = "data-table-column-visibility";

const ProjectListClient = ({ data }: ProjectListClientProps) => {
  const queryClient = useQueryClient();
  const dispatch = useDispatch();
  const { user } = useAuth();
  const [globalFilter, setGlobalFilter] = useState("");
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [tableInstance, setTableInstance] =
    useState<ReactTable<Project> | null>(null);

  const [isDropdownOpen, setIsDropdownOpen] = React.useState(false);
  const [showConfirmationModal, setShowConfirmationModal] = useState(false);
  const [confirmationModalContent, setConfirmationModalContent] = useState<{
    title: string;
    description: string | JSX.Element;
    confirmButtonText: string;
    confirmButtonClass: string;
    onConfirm: () => void;
  } | null>(null);

  const [showShareModal, setShowShareModal] = useState(false);
  const [selectedProject, setSelectedProject] = useState<Project | undefined>(
    undefined
  );

  // State to track if any rows are selected
  const [isAnyRowSelected, setIsAnyRowSelected] = useState(false);
  const [isSingleRowSelected, setIsSingleRowSelected] = useState(false);
  const [isAnyDeployedSelected, setIsAnyDeployedSelected] = useState(false);

  // Archive mutation
  const archiveMutation = useMutation({
    mutationFn: (projectIds: number[]) => archiveMultipleProjects(projectIds),
    onSuccess: (data, variables) => {
      // Show success notification
      const projectCount = variables.length;
      const message =
        projectCount === 1
          ? "Project archived successfully"
          : `${projectCount} projects archived successfully`;

      dispatch(
        showNotification({
          message,
          type: "success",
        })
      );

      // Invalidate projects query to trigger a refetch
      queryClient.invalidateQueries({ queryKey: ["projects", user?.id] });

      // Clear row selection after successful operation
      tableInstance?.resetRowSelection();

      // Close modal
      setShowConfirmationModal(false);
    },
    onError: (error) => {
      console.error("Error archiving projects:", error);

      // Show error notification
      dispatch(
        showNotification({
          message: "Failed to archive projects. Please try again.",
          type: "error",
        })
      );

      // Close modal
      setShowConfirmationModal(false);
    },
  });

  // Delete mutation
  const deleteMutation = useMutation({
    mutationFn: (projectIds: number[]) => deleteMultipleProjects(projectIds),
    onSuccess: (data, variables) => {
      // Show success notification
      const projectCount = variables.length;
      const message =
        projectCount === 1
          ? "Project deleted successfully"
          : `${projectCount} projects deleted successfully`;

      dispatch(
        showNotification({
          message,
          type: "success",
        })
      );

      // Invalidate projects query to trigger a refetch
      queryClient.invalidateQueries({ queryKey: ["projects", user?.id] });

      // Clear row selection after successful operation
      tableInstance?.resetRowSelection();

      // Close modal
      setShowConfirmationModal(false);
    },
    onError: (error) => {
      console.error("Error deleting projects:", error);

      // Show error notification
      dispatch(
        showNotification({
          message: "Failed to delete projects. Please try again.",
          type: "error",
        })
      );

      // Close modal
      setShowConfirmationModal(false);
    },
  });

  // Callback to monitor row selection changes
  const handleRowSelectionChange = (rowSelection: Record<string, boolean>) => {
    const selectedRowIds = Object.keys(rowSelection);

    const selectedRows = data.filter((_, index) => {
      const isSelected = selectedRowIds.includes(index.toString());
      return isSelected;
    });

    // Check if ALL selected rows have deployed status
    const allAreDeployed =
      selectedRows.length > 0 &&
      selectedRows.every((row) => row.status.toLowerCase() === "deployed");

    setIsAnyRowSelected(selectedRowIds.length > 0);
    setIsSingleRowSelected(selectedRowIds.length === 1);
    setIsAnyDeployedSelected(allAreDeployed);
  };

  const handleDeleteClick = () => {
    setConfirmationModalContent({
      title: "Confirm Deletion",
      description: (
        <>
          <p>
            Are you sure you want to delete this item? This action cannot be
            undone.
          </p>
          <ul className="list-disc list-inside flex flex-col gap-2 mt-2 text-neutral-700">
            <li>All data gathered for these projects will be deleted.</li>
            <li>Forms associated with these projects will be deleted.</li>
            <li>
              You will not be able to recover these projects after deletion.
            </li>
          </ul>
        </>
      ),
      confirmButtonText: "Delete",
      confirmButtonClass: "bg-red-500 hover:bg-red-600 cursor-pointer",
      onConfirm: () => {
        const selectedRowIds =
          tableInstance
            ?.getSelectedRowModel()
            .rows.map((row) => row.original.id) || [];
        // Execute the mutation
        deleteMutation.mutate(selectedRowIds);
      },
    });
    setShowConfirmationModal(true);
  };

  const handleArchiveClick = () => {
    setConfirmationModalContent({
      title: "Confirm Archive",
      description:
        "Are you sure you want to archive this item? You can restore it later if needed.",
      confirmButtonText: "Archive",
      confirmButtonClass: "btn-primary",
      onConfirm: () => {
        const selectedRowIds =
          tableInstance
            ?.getSelectedRowModel()
            .rows.map((row) => row.original.id) || [];
        // Execute the mutation
        archiveMutation.mutate(selectedRowIds);
      },
    });
    setShowConfirmationModal(true);
  };

  const handleShareClick = () => {
    const selectedRow = tableInstance?.getSelectedRowModel().rows[0]?.original;

    if (selectedRow) {
      setSelectedProject(selectedRow);
      setShowShareModal(true);
    }
  };

  useEffect(() => {
    try {
      const saved = localStorage.getItem(STORAGE_KEY);
      if (saved) {
        const parsed = JSON.parse(saved);
        if (parsed && typeof parsed === "object" && !Array.isArray(parsed)) {
          setColumnVisibility(parsed);
        } else {
          console.warn("Invalid format in localstorage for column visibility");
        }
      }
    } catch (error) {
      console.error("Error loading column visibility:", error);
    }
  }, []);

  useEffect(() => {
    if (Object.keys(columnVisibility).length > 0) {
      try {
        localStorage.setItem(STORAGE_KEY, JSON.stringify(columnVisibility));
      } catch (error) {
        console.error("Error saving column visibility:", error);
      }
    }
  }, [columnVisibility]);

  const handleColumnVisibilityChange = (newState: VisibilityState) => {
    setColumnVisibility(newState);
  };

  const handleTableInit = (table: ReactTable<Project>) => {
    setTableInstance(table);

    if (Object.keys(columnVisibility).length > 0) {
      table.setColumnVisibility(columnVisibility);
    }
  };

  return (
    <div className="bg-neutral-100 rounded-md">
      <div className="px-6 py-2">
        <div className="flex flex-col laptop:flex-row justify-between items-center laptop:gap-5 mobile:p-4 laptop:p-2">
          <div className="flex flex-col items-center gap-2 laptop:gap-14 laptop:flex-row  text-neutral-700 laptop:p-4">
            <h2 className=" font-semibold">My Projects</h2>
            <div className="flex flex-col tablet:flex-row gap-4 items-center py-4">
              <Input
                placeholder="Search all columns..."
                value={globalFilter}
                onChange={(e) => setGlobalFilter(e.target.value)}
              />

              {tableInstance && (
                <DropdownMenu
                  open={isDropdownOpen}
                  onOpenChange={(open) => setIsDropdownOpen(open)}
                >
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="outline"
                      className="flex items-center gap-2 cursor-pointer"
                    >
                      Show/Hide Columns
                      {isDropdownOpen ? (
                        <FaChevronUp className="w-3 h-3" />
                      ) : (
                        <FaChevronDown className="w-3 h-3" />
                      )}
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent
                    align="start"
                    className=" border bg-neutral-100 border-neutral-200 shadow-md"
                  >
                    {tableInstance
                      .getAllColumns()
                      .filter((column) => column.getCanHide())
                      .map((column) => (
                        <DropdownMenuCheckboxItem
                          key={column.id}
                          className="capitalize cursor-pointer hover:bg-neutral-200"
                          checked={columnVisibility[column.id] ?? true}
                          onCheckedChange={(value) =>
                            setColumnVisibility((prev) => ({
                              ...prev,
                              [column.id]: value,
                            }))
                          }
                        >
                          {column.id}
                        </DropdownMenuCheckboxItem>
                      ))}
                  </DropdownMenuContent>
                </DropdownMenu>
              )}
            </div>
          </div>

          <div className="flex gap-4 text-neutral-700">
            <div
              title="Archive"
              className={`p-2 rounded-full transition-all duration-300 ease-in-out ${
                isAnyDeployedSelected
                  ? "hover:bg-primary-500 hover:text-neutral-100 cursor-pointer"
                  : "opacity-50"
              }`}
              onClick={isAnyDeployedSelected ? handleArchiveClick : undefined}
            >
              <FaArchive className="h-4 w-4" />
            </div>
            <div
              title="Share"
              className={`p-2 rounded-full transition-all duration-300 ease-in-out ${
                isSingleRowSelected
                  ? "hover:bg-primary-500 hover:text-neutral-100 cursor-pointer"
                  : "opacity-50"
              }`}
              onClick={isSingleRowSelected ? handleShareClick : undefined}
            >
              <FaUserPlus className="h-4 w-4" />
            </div>
            <div
              title="Delete"
              className={`p-2 rounded-full transition-all duration-300 ease-in-out ${
                isAnyRowSelected
                  ? "hover:bg-primary-500 hover:text-neutral-100 cursor-pointer"
                  : "opacity-50"
              }`}
              onClick={isAnyRowSelected ? handleDeleteClick : undefined}
            >
              <RiDeleteBin6Fill className="h-4 w-4" />
            </div>
          </div>
        </div>
        <div className=" mx-auto">
          <GeneralTable
            columns={ProjectListColumns}
            data={data}
            globalFilter={globalFilter}
            setGlobalFilter={setGlobalFilter}
            onTableInit={handleTableInit}
            columnVisibility={columnVisibility}
            setColumnVisibility={handleColumnVisibilityChange}
            onRowSelectionChange={handleRowSelectionChange} // Monitor row selection changes
          />
        </div>
      </div>
      {confirmationModalContent && (
        <ConfirmationModal
          showModal={showConfirmationModal}
          onClose={() => setShowConfirmationModal(false)}
          onConfirm={confirmationModalContent.onConfirm}
          title={confirmationModalContent.title}
          description={confirmationModalContent.description}
          confirmButtonText={confirmationModalContent.confirmButtonText}
          confirmButtonClass={confirmationModalContent.confirmButtonClass}
        />
      )}
      <ShareProjectModal
        showModal={showShareModal}
        selectedProject={selectedProject}
        onClose={() => setShowShareModal(false)}
        onShare={() => {
          setShowShareModal(false);
        }}
      />
    </div>
  );
};

export { ProjectListClient };

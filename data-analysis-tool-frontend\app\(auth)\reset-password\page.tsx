"use client";

import { ResetLinkSentModal } from "@/components/modals/ResetLinkSentModal";
import { <PERSON>Lef<PERSON>, ArrowRight, ShieldCheck } from "lucide-react";
import Link from "next/link";
import React, { useState } from "react";
import { FieldValues, useForm } from "react-hook-form";
import axios from "@/lib/axios";

const PasswordResetPage = () => {
  const {
    register,
    formState: { errors, isSubmitting },
    handleSubmit,
    getValues,
  } = useForm();

  const [showResetLinkSentModal, setShowResetLinkShowModal] =
    useState<boolean>(false);

  const onSubmit = async (data: FieldValues) => {
    try {
      await axios.post(`/users/forgetpassword`, { email: data.email });
      setShowResetLinkShowModal(true);
    } catch (error) {
      console.error(error instanceof Error ? error.message : error);
    }
  };

  return (
    <div className="flex items-center justify-center min-h-screen">
      <ResetLinkSentModal
        email={getValues("email")}
        showModal={showResetLinkSentModal}
        setShowModal={setShowResetLinkShowModal}
      />
      <div className="section flex flex-col gap-8 w-11/12 mobile:w-4/5 tablet:w-lg">
        <div className="flex flex-col items-center gap-2">
          <ShieldCheck size={36} />
          <h1 className="text-2xl tablet:text-3xl font-semibold text-center">
            Reset your password
          </h1>
          <p className="text-neutral-700 text-center">
            Enter your email address and we'll send you a link to reset your
            password
          </p>
        </div>
        <form
          className="flex flex-col gap-4 "
          onSubmit={handleSubmit(onSubmit)}
          noValidate
        >
          <div className="label-input-group group">
            <label htmlFor="email" className="label-text">
              Email
            </label>
            <input
              {...register("email", { required: "Please enter your email" })}
              id="email"
              type="email"
              className="input-field"
              placeholder="eg: <EMAIL>"
            />
            {errors.email && (
              <p className="text-sm text-red-500">{`${errors.email.message}`}</p>
            )}
          </div>
          <button type="submit" className="btn-primary">
            {isSubmitting ? (
              <span className="flex items-center gap-2">
                Sending{" "}
                <div className="animate-spin border-x-2 border-neutral-100 rounded-full size-4"></div>
              </span>
            ) : (
              <span className="flex items-center gap-2">Send reset link</span>
            )}
          </button>
        </form>
        <Link
          href="/"
          className="text-neutral-700 self-center flex items-center gap-2"
        >
          <ArrowLeft size={16} /> Back to signin page
        </Link>
      </div>
    </div>
  );
};

export default PasswordResetPage;

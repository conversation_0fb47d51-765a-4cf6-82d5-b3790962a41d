"use client";

import { showNotification } from "@/redux/slices/notificationSlice";
import axios from "@/lib/axios";
import { ArrowLeft, ShieldCheck, Eye, EyeOff } from "lucide-react";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import React, { useState } from "react";
import { FieldValues, useForm } from "react-hook-form";
import { useDispatch } from "react-redux";

const ChangePasswordPage = () => {
  const {
    register,
    formState: { errors, isSubmitting },
    handleSubmit,
    getValues,
    watch,
  } = useForm();

  // Watch password fields to determine when to show the eye button
  const passwordValue = watch("password");
  const confirmPasswordValue = watch("confirmPassword");

  const searchParams = useSearchParams();
  const token = searchParams.get("token");

  const router = useRouter();
  const dispatch = useDispatch();

  // Password visibility states
  const [showPassword, setShowPassword] = useState<boolean>(false);
  const [showConfirmPassword, setShowConfirmPassword] =
    useState<boolean>(false);

  const onSubmit = async (data: FieldValues) => {
    try {
      await axios.post(`/users/resetpassword`, {
        token,
        newPassword: data.password,
      });
      dispatch(
        showNotification({
          message: "Password changed successfully",
          type: "success",
        })
      );
      router.push("/");
    } catch (error) {
      console.error(error);
    }
  };

  if (!token)
    return (
      <p className="text-red-500">
        No password reset token found in search params
      </p>
    );

  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="section flex flex-col gap-8 w-11/12 mobile:w-4/5 tablet:w-lg">
        <div className="flex flex-col items-center gap-2">
          <ShieldCheck size={36} />
          <h1 className="text-2xl tablet:text-3xl font-semibold text-center">
            Create new password
          </h1>
          <p className="text-neutral-700 text-center">
            Your new password must be different from previously used passwords
            and meet the requirements below.
          </p>
        </div>
        <form
          className="flex flex-col gap-4 "
          onSubmit={handleSubmit(onSubmit)}
          noValidate
        >
          <div className="label-input-group group">
            <label htmlFor="password" className="label-text">
              New Password
            </label>
            <div className="relative">
              <input
                {...register("password", {
                  required: "Please enter your password",
                  validate: {
                    minLength: (value) =>
                      value.length >= 8 ||
                      "Password must be at least 8 characters",
                    hasUppercase: (value) =>
                      /[A-Z]/.test(value) ||
                      "Password must contain at least one uppercase letter",
                    hasNumber: (value) =>
                      /\d/.test(value) ||
                      "Password must contain at least one number",
                    hasSymbol: (value) =>
                      /[\W_]/.test(value) ||
                      "Password must contain at least one symbol",
                  },
                })}
                id="password"
                type={showPassword ? "text" : "password"}
                className="input-field w-full pr-10"
                placeholder="Enter a new password"
              />
              {passwordValue && passwordValue.length > 0 && (
                <button
                  type="button"
                  tabIndex={-1}
                  className="absolute right-2 top-1/2 -translate-y-1/2 text-gray-500"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                  <span className="sr-only">
                    {showPassword ? "Hide" : "Show"} password
                  </span>
                </button>
              )}
            </div>
            {errors.password && (
              <p className="text-sm text-red-500">{`${errors.password.message}`}</p>
            )}
          </div>
          <div className="label-input-group group">
            <label htmlFor="confirm-password" className="label-text">
              Confirm Password
            </label>
            <div className="relative">
              <input
                {...register("confirmPassword", {
                  required: "Please confirm your password",
                  validate: (value) =>
                    value === getValues("password") || "Passwords do not match",
                })}
                id="confirm-password"
                type={showConfirmPassword ? "text" : "password"}
                className="input-field w-full pr-10"
                placeholder="Confirm your password"
              />
              {confirmPasswordValue && confirmPasswordValue.length > 0 && (
                <button
                  type="button"
                  tabIndex={-1}
                  className="absolute right-2 top-1/2 -translate-y-1/2 text-gray-500"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                >
                  {showConfirmPassword ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                  <span className="sr-only">
                    {showConfirmPassword ? "Hide" : "Show"} password
                  </span>
                </button>
              )}
            </div>
            {errors.confirmPassword && (
              <p className="text-sm text-red-500">{`${errors.confirmPassword.message}`}</p>
            )}
          </div>
          <button type="submit" className="btn-primary">
            {isSubmitting ? (
              <span className="flex items-center gap-2">
                Updating{" "}
                <div className="animate-spin border-x-2 border-neutral-100 rounded-full size-4"></div>
              </span>
            ) : (
              <span className="flex items-center gap-2">Reset password</span>
            )}
          </button>
        </form>
        <Link
          href="/"
          className="text-neutral-700 self-center flex items-center gap-2"
        >
          <ArrowLeft size={16} /> Back to signin page
        </Link>
      </div>
    </div>
  );
};

export { ChangePasswordPage };

"use client";

import {
  AlertCircle,
  Briefcase,
  Calendar,
  ChartGantt,
  CircleHelp,
  Clock,
  Globe,
  Rocket,
  Upload,
  User,
} from "lucide-react";
import React, { useEffect, useState } from "react";
import { format } from "date-fns";
import Spinner from "@/components/general/Spinner";
import { Project } from "@/types";
import { useQuery } from "@tanstack/react-query";
import { fetchProjectById } from "@/lib/api/projects";
import { useParams } from "next/navigation";
import { decode } from "@/lib/encodeDecode";
import { useAuth } from "@/hooks/useAuth";
import { useDispatch } from "react-redux";
import { showNotification } from "@/redux/slices/notificationSlice";

const ProjectOverviewPage = () => {
  const [hasMounted, setHasMounted] = useState<boolean>(false);

  useEffect(() => {
    setHasMounted(true);
  }, []);

  const { hashedId } = useParams();

  const hashedIdString = hashedId as string;

  const projectId = decode(hashedIdString);

  const { user } = useAuth();

  const dispatch = useDispatch();

  const handleTestForm = () => {
    // Open a new tab with the test form route
    if (hashedId) {
      window.open(`/form-test/${hashedId}/sign-in`, "_blank");
    } else {
      console.error("hashedId is missing");
    }
  };

  const handleCopy = () => {
    if (hashedId) {
      const url = `${window.location.origin}/form-test/${hashedId}/sign-in`;
      navigator.clipboard.writeText(url)
        .then(() => {
          dispatch(showNotification({ message: "Link copied to clipboard!", type: "success" }));
        })
        .catch((err) => {
          console.error("Failed to copy: ", err);
          dispatch(showNotification({ message: "Failed to copy the link.", type: "error" }));
        });
    } else {
      dispatch(showNotification({ message: "Invalid link. Try again.", type: "warning" }));
    }
  };

  const {
    data: projectData,
    isLoading: projectLoading,
    isError: projectError,
  } = useQuery<Project>({
    queryKey: ["projects", user?.id, projectId],
    queryFn: () => fetchProjectById({ projectId: projectId! }),
    enabled: !!projectId && !!user?.id,
  });

  // To prevent errors from showing when the component is not fully mounted.
  if (!hasMounted) return null;

  if (projectLoading) {
    return <Spinner />;
  }

  // If hashedId is missing, show an error
  if (!hashedId || projectId === null) {
    return (
      <div className="error-message">
        <h1 className="text-red-500">Error: Invalid Project ID (hashedId).</h1>
        <p className="text-neutral-700">
          Please make sure the URL contains a valid project identifier.
        </p>
      </div>
    );
  }

  if (projectError) {
    return (
      <p className="text-red-500">Failed to fetch project. Please try again</p>
    );
  }

  return (
    <div className="flex flex-col gap-8">
      {/* Description section */}
      <section className="flex flex-col gap-2">
        <div className="flex items-center justify-between">
          <span className="text-lg flex items-center gap-2 font-medium capitalize">
            <AlertCircle size={18} /> Description
          </span>
          <span className="rounded-full px-2 py-1 bg-accent-200 text-accent-700 font-medium text-sm flex items-center gap-2">
            <span className="size-2 rounded-full bg-accent-700"></span>
            {projectData?.status}
          </span>
        </div>
        <p>{projectData?.description}</p>
      </section>
      {/* metadata section */}
      <section className="grid grid-cols-3 gap-8">
        <div className="rounded-md border-2 border-neutral-400 p-8 flex items-center justify-center flex-col gap-2">
          <span className="label-text capitalize">
            <CircleHelp size={16} />
            Questions
          </span>
          <span className="text-lg font-medium">
            {projectData?.questions?.length}
          </span>
        </div>
        <div className="rounded-md border-2 border-neutral-400 p-8 flex items-center justify-center flex-col gap-2">
          <span className="label-text capitalize">
            <User size={16} />
            Owner
          </span>
          <span className="text-lg font-medium">{projectData?.user.name}</span>
        </div>
        <div className="rounded-md border-2 border-neutral-400 p-8 flex items-center justify-center text-center flex-col gap-2">
          <span className="label-text capitalize">
            <Briefcase size={16} />
            Sector
          </span>
          <span className="text-lg font-medium w-full truncate">
            {projectData?.sector}
          </span>
        </div>
        {/* timeline */}
        <div className="flex flex-col gap-4">
          <span className="text-lg flex items-center gap-2 font-medium capitalize">
            Collect Data (Online Form Submissions)
          </span>
          <div className="flex gap-3">
            <button className="btn-primary" onClick={handleCopy}>
              Copy
            </button>
            <button className="btn-primary" onClick={handleTestForm}>
              Open
            </button>
          </div>
          <span className="text-lg flex items-center gap-2 font-medium capitalize">
            <ChartGantt size={18} />
            Timeline
          </span>
          <div className="flex items-center gap-4">
            {/* icon */}
            <div className="rounded-full p-2 bg-primary-200">
              <Clock size={16} className="text-primary-500" />
            </div>
            <div>
              <span className="capitalize">Last modified</span>
              <span className="label-text">
                <Calendar size={16} />
                {projectData?.updatedAt
                  ? format(new Date(projectData.updatedAt), "MMMM d, yyyy h:mm a")
                  : "N/A"}

              </span>
            </div>
          </div>
          <div className="flex items-center gap-4">
            {/* icon */}
            <div className="rounded-full p-2 bg-primary-200">
              <Rocket size={16} className="text-primary-500" />
            </div>
            <div>
              <span className="capitalize">Last deployed</span>
              <span className="label-text">
                <Calendar size={16} />
                {projectData?.lastDeployedAt
                  ? format(new Date(projectData.lastDeployedAt), "MMMM d, yyyy h:mm a")
                  : "Not deployed yet"}
              </span>
            </div>
          </div>
          <div className="flex items-center gap-4">
            {/* icon */}
            <div className="rounded-full p-2 bg-primary-200">
              <Upload size={16} className="text-primary-500" />
            </div>
            <div>
              <span className="capitalize">Latest submission</span>
              <span className="label-text">
                <Calendar size={16} />
                {projectData?.lastSubmissionAt
                  ? format(
                    new Date(projectData.lastSubmissionAt),
                    "MMMM d, yyyy h:mm a"
                  )
                  : "No submissions yet"}
              </span>
            </div>
          </div>
        </div>
      </section>
      {/* Country */}
      <section className="label-text cursor-default">
        <Globe size={16} /> {projectData?.country}
      </section>

    </div>
  );
};

export default ProjectOverviewPage;
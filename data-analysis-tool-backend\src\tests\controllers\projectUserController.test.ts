import { Request, Response } from "express";
import {
  createProjectUser,
  updateProjectUser,
  deletProjectUser,
  getAllProjectUser,
  copyProjectUsers,
} from "../../controllers/projectUserController";
import ProjectUserRepository from "../../repositories/projectUserRepository";
import projectRepository from "../../repositories/projectRepository";
import userRepository from "../../repositories/userRepository";

// Mock the repositories
jest.mock("../../repositories/projectUserRepository");
jest.mock("../../repositories/projectRepository");
jest.mock("../../repositories/userRepository");

describe("Project User Controller", () => {
  let mockRequest: Partial<Request & { user?: { id: number } }>;
  let mockResponse: Partial<Response>;
  let responseObject: any = {};

  beforeEach(() => {
    // Reset mocks before each test
    jest.clearAllMocks();
    jest.resetAllMocks();

    // Setup mock response
    mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockImplementation((result) => {
        responseObject = result;
        return mockResponse;
      }),
    };

    // Reset response object
    responseObject = {};

    // Setup default authenticated user
    mockRequest = {
      user: {
        id: 1,
      },
      params: {},
      body: {},
    };
  });

  describe("createProjectUser", () => {
    beforeEach(() => {
      mockRequest.body = {
        userId: 2,
        projectId: 1,
        permission: {
          viewForm: true,
          editForm: false,
        },
      };
    });

    it("should create a project user successfully", async () => {
      const mockUser = {
        id: 2,
        name: "Test User",
        email: "<EMAIL>",
      };

      const mockProject = {
        id: 1,
        name: "Test Project",
        description: "Test Description",
        sector: "other",
        user: {
          id: 1,
          name: "Owner",
          email: "<EMAIL>",
        },
        lastDeployedAt: null,
        lastSubmissionAt: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const mockProjectUser = {
        id: 1,
        userId: 2,
        projectId: 1,
        permission: {
          viewForm: true,
          editForm: false,
        },
        createdAt: new Date(),
        updatedAT: new Date(),
      };

      (userRepository.findById as jest.Mock).mockResolvedValue(mockUser);
      (projectRepository.findById as jest.Mock).mockResolvedValue(mockProject);
      (ProjectUserRepository.findUserProject as jest.Mock).mockResolvedValue(
        null
      );
      (ProjectUserRepository.create as jest.Mock).mockResolvedValue(
        mockProjectUser
      );

      await createProjectUser(mockRequest as any, mockResponse as Response);

      expect(userRepository.findById).toHaveBeenCalledWith(2);
      expect(projectRepository.findById).toHaveBeenCalledWith(1);
      expect(ProjectUserRepository.findUserProject).toHaveBeenCalledWith(2, 1);
      expect(ProjectUserRepository.create).toHaveBeenCalledWith(2, 1, {
        viewForm: true,
        editForm: false,
      });
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(responseObject).toHaveProperty("success", true);
      expect(responseObject).toHaveProperty(
        "message",
        "user added successly to project"
      );
      expect(responseObject.data).toHaveProperty(
        "userProject",
        mockProjectUser
      );
    });

    it("should return 404 when user is not found", async () => {
      (userRepository.findById as jest.Mock).mockResolvedValue(null);

      await createProjectUser(mockRequest as any, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(404);
      expect(responseObject).toHaveProperty("success", false);
      expect(responseObject).toHaveProperty("message", "user not found");
      expect(ProjectUserRepository.create).not.toHaveBeenCalled();
    });

    it("should return 400 when trying to add the owner", async () => {
      mockRequest.body.userId = 1; // Same as the owner ID

      const mockUser = {
        id: 1,
        name: "Owner",
        email: "<EMAIL>",
      };

      (userRepository.findById as jest.Mock).mockResolvedValue(mockUser);

      await createProjectUser(mockRequest as any, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(responseObject).toHaveProperty("success", false);
      expect(responseObject).toHaveProperty(
        "message",
        "You are the owner of the project. No need to add yourself"
      );
      expect(ProjectUserRepository.create).not.toHaveBeenCalled();
    });

    it("should return 404 when project is not found", async () => {
      const mockUser = {
        id: 2,
        name: "Test User",
        email: "<EMAIL>",
      };

      (userRepository.findById as jest.Mock).mockResolvedValue(mockUser);
      (projectRepository.findById as jest.Mock).mockResolvedValue(null);

      await createProjectUser(mockRequest as any, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(404);
      expect(responseObject).toHaveProperty("success", false);
      expect(responseObject).toHaveProperty("message", "project not found");
      expect(ProjectUserRepository.create).not.toHaveBeenCalled();
    });

    it("should return 400 when user is already associated with project", async () => {
      const mockUser = {
        id: 2,
        name: "Test User",
        email: "<EMAIL>",
      };

      const mockProject = {
        id: 1,
        name: "Test Project",
        description: "Test Description",
        sector: "other",
        user: {
          id: 1,
          name: "Owner",
          email: "<EMAIL>",
        },
        lastDeployedAt: null,
        lastSubmissionAt: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const existingProjectUser = {
        id: 1,
        userId: 2,
        projectId: 1,
        permission: {
          viewForm: true,
        },
      };

      (userRepository.findById as jest.Mock).mockResolvedValue(mockUser);
      (projectRepository.findById as jest.Mock).mockResolvedValue(mockProject);
      (ProjectUserRepository.findUserProject as jest.Mock).mockResolvedValue(
        existingProjectUser
      );

      await createProjectUser(mockRequest as any, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(responseObject).toHaveProperty("success", false);
      expect(responseObject).toHaveProperty(
        "message",
        "user already associated with project"
      );
      expect(ProjectUserRepository.create).not.toHaveBeenCalled();
    });

    it("should return 400 for invalid input", async () => {
      mockRequest.body = {}; // Missing required fields

      await createProjectUser(mockRequest as any, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(responseObject).toHaveProperty("success", false);
      expect(responseObject).toHaveProperty("message");
      expect(ProjectUserRepository.create).not.toHaveBeenCalled();
    });

    it("should handle server errors", async () => {
      (userRepository.findById as jest.Mock).mockRejectedValue(
        new Error("Database error")
      );

      await createProjectUser(mockRequest as any, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(responseObject).toHaveProperty("success", false);
      expect(responseObject).toHaveProperty(
        "message",
        "error creating qustion"
      );
    });
  });

  describe("updateProjectUser", () => {
    beforeEach(() => {
      mockRequest.body = {
        userId: 2,
        projectId: 1,
        permission: {
          viewForm: true,
          editForm: true,
        },
      };
    });

    it("should update project user permissions successfully", async () => {
      const updatedProjectUser = {
        id: 1,
        userId: 2,
        projectId: 1,
        permission: {
          viewForm: true,
          editForm: true,
        },
        createdAt: new Date(),
        updatedAT: new Date(),
      };

      (
        ProjectUserRepository.updateUserPermission as jest.Mock
      ).mockResolvedValue(updatedProjectUser);

      await updateProjectUser(mockRequest as any, mockResponse as Response);

      expect(ProjectUserRepository.updateUserPermission).toHaveBeenCalledWith(
        2,
        1,
        { viewForm: true, editForm: true }
      );
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      // Controller returns success: false even on successful update
      expect(responseObject).toHaveProperty("success", false);
      expect(responseObject).toHaveProperty(
        "message",
        "permission updated success"
      );
      expect(responseObject.data).toHaveProperty(
        "updatedProject",
        updatedProjectUser
      );
    });

    it("should return 400 for invalid input", async () => {
      mockRequest.body = {}; // Missing required fields

      await updateProjectUser(mockRequest as any, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(responseObject).toHaveProperty("success", false);
      expect(responseObject).toHaveProperty("message");
      expect(ProjectUserRepository.updateUserPermission).not.toHaveBeenCalled();
    });

    it("should handle server errors", async () => {
      (
        ProjectUserRepository.updateUserPermission as jest.Mock
      ).mockRejectedValue(new Error("Database error"));

      await updateProjectUser(mockRequest as any, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(responseObject).toHaveProperty("success", false);
      expect(responseObject).toHaveProperty(
        "message",
        "error creating qustion"
      );
    });
  });

  describe("deletProjectUser", () => {
    beforeEach(() => {
      mockRequest.body = {
        userId: 2,
        projectId: 1,
      };
    });

    it("should delete a project user successfully", async () => {
      const deletedProjectUser = {
        id: 1,
        userId: 2,
        projectId: 1,
        permission: {
          viewForm: true,
        },
        createdAt: new Date(),
        updatedAT: new Date(),
      };

      (
        ProjectUserRepository.deleteUserFromProject as jest.Mock
      ).mockResolvedValue(deletedProjectUser);

      await deletProjectUser(mockRequest as any, mockResponse as Response);

      expect(ProjectUserRepository.deleteUserFromProject).toHaveBeenCalledWith(
        2,
        1
      );
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      // Controller returns success: false even on successful deletion
      expect(responseObject).toHaveProperty("success", false);
      expect(responseObject).toHaveProperty("message", "user delete succes");
    });

    it("should handle server errors", async () => {
      (
        ProjectUserRepository.deleteUserFromProject as jest.Mock
      ).mockRejectedValue(new Error("Database error"));

      await deletProjectUser(mockRequest as any, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(responseObject).toHaveProperty("success", false);
      expect(responseObject).toHaveProperty(
        "message",
        "error creating qustion"
      );
    });
  });

  describe("getAllProjectUser", () => {
    beforeEach(() => {
      // Set projectId in params instead of body since controller uses req.params.projectId
      mockRequest.params = {
        projectId: "1", // Params are strings in Express
      };
    });

    it("should get all project users successfully", async () => {
      const mockProjectUsers = [
        {
          id: 1,
          userId: 2,
          projectId: 1,
          permission: {
            viewForm: true,
          },
          createdAt: new Date(),
          updatedAT: new Date(),
        },
        {
          id: 2,
          userId: 3,
          projectId: 1,
          permission: {
            viewForm: true,
            editForm: true,
          },
          createdAt: new Date(),
          updatedAT: new Date(),
        },
      ];

      (ProjectUserRepository.allUsers as jest.Mock).mockResolvedValue(
        mockProjectUsers
      );

      await getAllProjectUser(mockRequest as any, mockResponse as Response);

      // Number(req.params.projectId) is used in the controller
      expect(ProjectUserRepository.allUsers).toHaveBeenCalledWith(1);
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(responseObject).toHaveProperty("success", true);
      expect(responseObject).toHaveProperty(
        "message",
        "project user fetched success"
      );
      expect(responseObject.data).toHaveProperty("AllUser", mockProjectUsers);
    });

    it("should handle server errors", async () => {
      (ProjectUserRepository.allUsers as jest.Mock).mockRejectedValue(
        new Error("Database error")
      );

      await getAllProjectUser(mockRequest as any, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(responseObject).toHaveProperty("success", false);
      // Match the actual error message in the controller
      expect(responseObject).toHaveProperty("message", "error fetching users");
    });
  });

  describe("copyProjectUsers", () => {
    beforeEach(() => {
      mockRequest.body = {
        sourceProjectId: 1,
        targetProjectId: 2,
      };
      mockRequest.user = {
        id: 1,
      };
    });

    it("should copy project users successfully", async () => {
      const sourceProject = {
        id: 1,
        name: "Source Project",
        description: "Source Description",
        sector: "other",
        user: {
          id: 1,
          name: "Owner",
          email: "<EMAIL>",
        },
        lastDeployedAt: null,
        lastSubmissionAt: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const targetProject = {
        id: 2,
        name: "Target Project",
        description: "Target Description",
        sector: "other",
        user: {
          id: 1,
          name: "Owner",
          email: "<EMAIL>",
        },
        lastDeployedAt: null,
        lastSubmissionAt: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const sourceUsers = [
        {
          id: 1,
          userId: 2,
          projectId: 1,
          permission: {
            viewForm: true,
            editForm: false,
          },
          createdAt: new Date(),
          updatedAT: new Date(),
        },
        {
          id: 2,
          userId: 3,
          projectId: 1,
          permission: {
            viewForm: true,
            editForm: true,
          },
          createdAt: new Date(),
          updatedAT: new Date(),
        },
        {
          id: 3,
          userId: 1, // Owner, should be skipped
          projectId: 1,
          permission: {
            viewForm: true,
          },
          createdAt: new Date(),
          updatedAT: new Date(),
        },
      ];

      const newUser1 = {
        id: 4,
        userId: 2,
        projectId: 2,
        permission: {
          viewForm: true,
          editForm: false,
        },
        createdAt: new Date(),
        updatedAT: new Date(),
      };

      const newUser2 = {
        id: 5,
        userId: 3,
        projectId: 2,
        permission: {
          viewForm: true,
          editForm: true,
        },
        createdAt: new Date(),
        updatedAT: new Date(),
      };

      (projectRepository.findById as jest.Mock)
        .mockResolvedValueOnce(sourceProject)
        .mockResolvedValueOnce(targetProject);

      (ProjectUserRepository.allUsers as jest.Mock).mockResolvedValue(
        sourceUsers
      );
      (ProjectUserRepository.findUserProject as jest.Mock).mockResolvedValue(
        null
      );
      (ProjectUserRepository.create as jest.Mock)
        .mockResolvedValueOnce(newUser1)
        .mockResolvedValueOnce(newUser2);

      await copyProjectUsers(mockRequest as any, mockResponse as Response);

      expect(projectRepository.findById).toHaveBeenCalledWith(1);
      expect(projectRepository.findById).toHaveBeenCalledWith(2);
      expect(ProjectUserRepository.allUsers).toHaveBeenCalledWith(1);
      expect(ProjectUserRepository.create).toHaveBeenCalledTimes(2);
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(responseObject).toHaveProperty("success", true);
      expect(responseObject).toHaveProperty(
        "message",
        "2 users copied to target project successfully."
      );
      expect(responseObject).toHaveProperty("data", [newUser1, newUser2]);
    });

    it("should return 404 when user is not found", async () => {
      mockRequest.user = undefined;

      await copyProjectUsers(mockRequest as any, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(404);
      expect(responseObject).toHaveProperty("success", false);
      expect(responseObject).toHaveProperty("message", "User not found");
    });

    it("should return 404 when source or target project is not found", async () => {
      (projectRepository.findById as jest.Mock)
        .mockResolvedValueOnce(null) // Source project not found
        .mockResolvedValueOnce({}); // Target project found

      await copyProjectUsers(mockRequest as any, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(404);
      expect(responseObject).toHaveProperty("success", false);
      expect(responseObject).toHaveProperty(
        "message",
        "One or both projects not found"
      );
    });

    it("should return 403 when user is not the owner of target project", async () => {
      const sourceProject = {
        id: 1,
        name: "Source Project",
        description: "Source Description",
        sector: "other",
        user: {
          id: 1,
          name: "Owner",
          email: "<EMAIL>",
        },
        lastDeployedAt: null,
        lastSubmissionAt: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const targetProject = {
        id: 2,
        name: "Target Project",
        description: "Target Description",
        sector: "other",
        user: {
          id: 2, // Different owner
          name: "Another Owner",
          email: "<EMAIL>",
        },
        lastDeployedAt: null,
        lastSubmissionAt: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      (projectRepository.findById as jest.Mock)
        .mockResolvedValueOnce(sourceProject)
        .mockResolvedValueOnce(targetProject);

      await copyProjectUsers(mockRequest as any, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(403);
      expect(responseObject).toHaveProperty("success", false);
      expect(responseObject).toHaveProperty(
        "message",
        "You are not the owner of the target project"
      );
    });

    it("should handle server errors", async () => {
      (projectRepository.findById as jest.Mock).mockRejectedValue(
        new Error("Database error")
      );

      await copyProjectUsers(mockRequest as any, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(responseObject).toHaveProperty("success", false);
      expect(responseObject).toHaveProperty(
        "message",
        "error copying project user"
      );
    });
  });
});

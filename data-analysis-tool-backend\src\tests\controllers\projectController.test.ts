import { Request, Response } from "express";
import {
  createProject,
  getAllProject,
  updateProjects,
  deleteProject,
  getProjectById,
  changeProjectStatus,
  updateManyProjectStatus,
  DeleteMultipleProject,
} from "../../controllers/projectController";
import projectRepository from "../../repositories/projectRepository";
import { Status } from "@prisma/client";

// Mock the dependencies
jest.mock("../../repositories/projectRepository");

describe("Project Controller", () => {
  let mockRequest: Partial<Request & { user?: { id: number } }>;
  let mockResponse: Partial<Response>;
  let responseObject: any = {};

  beforeEach(() => {
    // Reset mocks before each test
    jest.clearAllMocks();
    jest.resetAllMocks();

    // Setup mock response
    mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockImplementation((result) => {
        responseObject = result;
        return mockResponse;
      }),
    };

    // Reset response object
    responseObject = {};

    // Setup default authenticated user
    mockRequest = {
      user: {
        id: 1,
      },
      params: {},
      query: {},
      body: {},
    };

    // Default mock for findProjectByIdAndUser to pass ownership checks (used in other tests)
    (projectRepository.findProjectByIdAndUser as jest.Mock).mockImplementation(
      (id, userId) => Promise.resolve({ id, userId, name: `Project ${id}` })
    );
  });

  describe("createProject", () => {
    beforeEach(() => {
      mockRequest.body = {
        name: "Test Project",
        description: "This is a test project",
        sector: "information_media",
        country: "United States",
      };
    });

    it("should create a project successfully", async () => {
      const mockProject = {
        id: 1,
        name: "Test Project",
        description: "This is a test project",
        sector: "information_media",
        country: "United States",
        userId: 1,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      (projectRepository.findByName as jest.Mock).mockResolvedValue(null);
      (projectRepository.create as jest.Mock).mockResolvedValue(mockProject);

      await createProject(mockRequest as any, mockResponse as Response);

      expect(projectRepository.findByName).toHaveBeenCalledWith(
        "Test Project",
        1
      );
      expect(projectRepository.create).toHaveBeenCalledWith({
        name: "Test Project",
        description: "This is a test project",
        sector: "information_media",
        userId: 1,
        country: "United States",
      });
      expect(mockResponse.status).toHaveBeenCalledWith(201);
      expect(responseObject).toHaveProperty(
        "message",
        "Project created successfully"
      );
      expect(responseObject).toHaveProperty("project", mockProject);
    });

    it("should return 400 when project already exists", async () => {
      const existingProject = {
        id: 1,
        name: "Test Project",
        userId: 1,
      };

      (projectRepository.findByName as jest.Mock).mockResolvedValue(
        existingProject
      );

      await createProject(mockRequest as any, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(responseObject).toHaveProperty("success", false);
      expect(responseObject).toHaveProperty("message", "project already exist");
      expect(projectRepository.create).not.toHaveBeenCalled();
    });

    it("should return 400 for invalid input", async () => {
      // Missing required fields
      mockRequest.body = { name: "Test Project" };

      await createProject(mockRequest as any, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(responseObject).toHaveProperty("success", false);
      expect(projectRepository.create).not.toHaveBeenCalled();
    });

    it("should handle server errors", async () => {
      (projectRepository.findByName as jest.Mock).mockRejectedValue(
        new Error("Database error")
      );

      await createProject(mockRequest as any, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(responseObject).toHaveProperty("success", false);
      expect(responseObject).toHaveProperty(
        "message",
        "error creating project"
      );
    });
  });

  describe("getAllProject", () => {
    it("should get all projects successfully", async () => {
      const mockProjects = [
        {
          id: 1,
          name: "Project 1",
          description: "Description 1",
          userId: 1,
        },
        {
          id: 2,
          name: "Project 2",
          description: "Description 2",
          userId: 1,
        },
      ];

      (projectRepository.findAll as jest.Mock).mockResolvedValue(mockProjects);

      await getAllProject(mockRequest as any, mockResponse as Response);

      expect(projectRepository.findAll).toHaveBeenCalledWith(1);
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(responseObject).toHaveProperty(
        "message",
        "Successfully fetched all projects"
      );
      expect(responseObject).toHaveProperty("projects", mockProjects);
    });

    it("should handle server errors", async () => {
      (projectRepository.findAll as jest.Mock).mockRejectedValue(
        new Error("Database error")
      );

      await getAllProject(mockRequest as any, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(responseObject).toHaveProperty("success", false);
      expect(responseObject).toHaveProperty(
        "message",
        "error getting projects"
      );
    });
  });

  describe("updateProjects", () => {
    beforeEach(() => {
      mockRequest.params = { id: "1" };
      mockRequest.body = {
        name: "Updated Project",
        description: "Updated description",
        sector: "information_media",
        country: "Canada",
      };
    });

    it("should update a project successfully", async () => {
      const updatedProject = {
        id: 1,
        name: "Updated Project",
        description: "Updated description",
        sector: "information_media",
        country: "Canada",
        userId: 1,
      };

      (projectRepository.updateById as jest.Mock).mockResolvedValue(
        updatedProject
      );

      await updateProjects(mockRequest as any, mockResponse as Response);

      expect(projectRepository.findProjectByIdAndUser).toHaveBeenCalledWith(
        1,
        1
      );
      expect(projectRepository.updateById).toHaveBeenCalledWith(
        1,
        expect.objectContaining({
          name: "Updated Project",
          description: "Updated description",
        })
      );
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(responseObject.data).toHaveProperty(
        "updatedProject",
        updatedProject
      );
    });

    it("should return 404 when project not found", async () => {
      (projectRepository.findProjectByIdAndUser as jest.Mock).mockResolvedValue(
        null
      );

      await updateProjects(mockRequest as any, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(404);
      expect(responseObject).toHaveProperty("success", false);
      expect(responseObject).toHaveProperty("message", "no project found");
      expect(projectRepository.updateById).not.toHaveBeenCalled();
    });

    it("should return 400 for invalid input", async () => {
      // Invalid input - missing required fields
      mockRequest.body = {};

      await updateProjects(mockRequest as any, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(responseObject).toHaveProperty("success", false);
      expect(projectRepository.updateById).not.toHaveBeenCalled();
    });

    it("should handle server errors", async () => {
      (projectRepository.findProjectByIdAndUser as jest.Mock).mockRejectedValue(
        new Error("Database error")
      );

      await updateProjects(mockRequest as any, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(responseObject).toHaveProperty("success", false);
      expect(responseObject).toHaveProperty(
        "message",
        "error updating project"
      );
    });
  });

  describe("deleteProject", () => {
    beforeEach(() => {
      mockRequest.params = { id: "1" };
    });

    it("should delete a project successfully", async () => {
      (projectRepository.deleteProject as jest.Mock).mockResolvedValue({
        id: 1,
      });

      await deleteProject(mockRequest as any, mockResponse as Response);

      expect(projectRepository.findProjectByIdAndUser).toHaveBeenCalledWith(
        1,
        1
      );
      expect(projectRepository.deleteProject).toHaveBeenCalledWith(1);
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(responseObject).toHaveProperty(
        "message",
        "project deleted success"
      );
    });

    it("should return 404 when project not found", async () => {
      (projectRepository.findProjectByIdAndUser as jest.Mock).mockResolvedValue(
        null
      );

      await deleteProject(mockRequest as any, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(404);
      expect(responseObject).toHaveProperty("success", false);
      expect(responseObject).toHaveProperty(
        "message",
        "no project found with give id"
      );
      expect(projectRepository.deleteProject).not.toHaveBeenCalled();
    });

    it("should handle server errors", async () => {
      (projectRepository.findProjectByIdAndUser as jest.Mock).mockRejectedValue(
        new Error("Database error")
      );

      await deleteProject(mockRequest as any, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(responseObject).toHaveProperty("success", false);
      expect(responseObject).toHaveProperty(
        "message",
        "error deleting project"
      );
    });
  });

  describe("getProjectById", () => {
    beforeEach(() => {
      mockRequest.params = { id: "1" };
    });

    it("should get a project by ID successfully", async () => {
      const mockProject = {
        id: 1,
        name: "Test Project",
        description: "This is a test project",
        userId: 1,
      };

      (projectRepository.findProjectByIdAndUser as jest.Mock).mockResolvedValue(
        mockProject
      );

      await getProjectById(mockRequest as any, mockResponse as Response);

      expect(projectRepository.findProjectByIdAndUser).toHaveBeenCalledWith(
        1,
        1
      );
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(responseObject).toHaveProperty(
        "message",
        "Successfully fetched project"
      );
      expect(responseObject).toHaveProperty("project", mockProject);
    });

    it("should return 404 when project not found", async () => {
      (projectRepository.findProjectByIdAndUser as jest.Mock).mockResolvedValue(
        null
      );

      await getProjectById(mockRequest as any, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(404);
      expect(responseObject).toHaveProperty("success", false);
      expect(responseObject).toHaveProperty("message", "project not found");
    });

    it("should handle server errors", async () => {
      (projectRepository.findProjectByIdAndUser as jest.Mock).mockRejectedValue(
        new Error("Database error")
      );

      await getProjectById(mockRequest as any, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(responseObject).toHaveProperty("success", false);
      expect(responseObject).toHaveProperty(
        "message",
        "error getting user by id"
      );
    });
  });

  describe("changeProjectStatus", () => {
    beforeEach(() => {
      mockRequest.params = { id: "1" };
      mockRequest.body = {
        status: "deployed",
      };
    });

    it("should change project status successfully", async () => {
      const updatedProject = {
        id: 1,
        name: "Test Project",
        status: "deployed",
      };

      (projectRepository.changeProjectStatus as jest.Mock).mockResolvedValue(
        updatedProject
      );

      await changeProjectStatus(
        mockRequest as Request,
        mockResponse as Response
      );

      expect(projectRepository.changeProjectStatus).toHaveBeenCalledWith(
        1,
        "deployed"
      );
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(responseObject.data).toHaveProperty(
        "updatedProject",
        updatedProject
      );
    });

    it("should return 400 for invalid input", async () => {
      // Invalid status
      mockRequest.body = { status: "INVALID_STATUS" };

      await changeProjectStatus(
        mockRequest as Request,
        mockResponse as Response
      );

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(responseObject).toHaveProperty("success", false);
      expect(projectRepository.changeProjectStatus).not.toHaveBeenCalled();
    });

    it("should handle server errors", async () => {
      (projectRepository.changeProjectStatus as jest.Mock).mockRejectedValue(
        new Error("Database error")
      );

      await changeProjectStatus(
        mockRequest as Request,
        mockResponse as Response
      );

      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(responseObject).toHaveProperty("success", false);
      expect(responseObject).toHaveProperty(
        "message",
        "error creating qustion"
      );
    });
  });

  describe("updateManyProjectStatus", () => {
    beforeEach(() => {
      mockRequest.body = {
        projectIds: [1, 2, 3],
        status: Status.deployed,
      };
    });

    it("should update many project statuses successfully", async () => {
      const mockResult = { count: 3 };

      (
        projectRepository.changeManyProjectStatus as jest.Mock
      ).mockResolvedValue(mockResult);

      await updateManyProjectStatus(
        mockRequest as Request,
        mockResponse as Response
      );

      expect(projectRepository.changeManyProjectStatus).toHaveBeenCalledWith(
        [1, 2, 3],
        Status.deployed
      );
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(responseObject).toHaveProperty("success", false); // Controller bug
      expect(responseObject).toHaveProperty(
        "message",
        "status changed success"
      );
      expect(responseObject.data).toHaveProperty("updatedProject", mockResult);
    });

    it("should return 400 for invalid input", async () => {
      // Missing required fields
      mockRequest.body = { projectIds: [1, 2, 3] };

      await updateManyProjectStatus(
        mockRequest as Request,
        mockResponse as Response
      );

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(responseObject).toHaveProperty("success", false);
      expect(projectRepository.changeManyProjectStatus).not.toHaveBeenCalled();
    });

    it("should handle server errors", async () => {
      (
        projectRepository.changeManyProjectStatus as jest.Mock
      ).mockRejectedValue(new Error("Database error"));

      await updateManyProjectStatus(
        mockRequest as Request,
        mockResponse as Response
      );

      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(responseObject).toHaveProperty("success", false);
      expect(responseObject).toHaveProperty(
        "message",
        "error creating qustion"
      );
    });
  });

  describe("DeleteMultipleProject", () => {
    beforeEach(() => {
      mockRequest.body = {
        projectIds: [1, 2, 3],
      };
    });

    it("should delete multiple projects successfully", async () => {
      const mockResult = { count: 3 };

      (projectRepository.deleteMultipleProject as jest.Mock).mockResolvedValue(
        mockResult
      );

      await DeleteMultipleProject(
        mockRequest as Request,
        mockResponse as Response
      );

      expect(projectRepository.deleteMultipleProject).toHaveBeenCalledWith([
        1, 2, 3,
      ]);
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(responseObject).toHaveProperty("success", false); // Controller bug
      expect(responseObject).toHaveProperty(
        "message",
        "project deleted success"
      );
    });

    it("should return 400 for invalid input", async () => {
      // Invalid input (not an array)
      mockRequest.body = { projectIds: "not an array" };

      await DeleteMultipleProject(
        mockRequest as Request,
        mockResponse as Response
      );

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(responseObject).toHaveProperty("success", false);
      expect(projectRepository.deleteMultipleProject).not.toHaveBeenCalled();
    });

    it("should handle server errors", async () => {
      (projectRepository.deleteMultipleProject as jest.Mock).mockRejectedValue(
        new Error("Database error")
      );

      await DeleteMultipleProject(
        mockRequest as Request,
        mockResponse as Response
      );

      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(responseObject).toHaveProperty("success", false);
      expect(responseObject).toHaveProperty(
        "message",
        "error creating qustion"
      );
    });
  });
});

import express from "express";
import {
  createQuestionGroup,
  updateQuestionGroup,
  deleteQuestionGroup,
  findAllProjectGroup,
  removeQuestionIdFromGroup,
  updateQuestionFromOneGroupToAnother,
  updateOneGroupInsideAnotherGroup,
  removeGroupFromParentGroup,
  deleteQuestionAndGroup,
} from "../controllers/questionGroupController";

const router = express.Router();

router.get("/", findAllProjectGroup as unknown as express.RequestHandler);
router.post("/", createQuestionGroup as unknown as express.RequestHandler);
router.patch("/", updateQuestionGroup as unknown as express.RequestHandler);
router.delete("/:id", deleteQuestionGroup as unknown as express.RequestHandler);
router.delete(
  "/group/question/:id",
  deleteQuestionAndGroup as unknown as express.RequestHandler
);
router.patch(
  "/question/remove",
  removeQuestionIdFromGroup as unknown as express.RequestHandler
);
router.patch(
  "/question/move",
  updateQuestionFromOneGroupToAnother as unknown as express.RequestHandler
);
router.patch(
  "/group/add",
  updateOneGroupInsideAnotherGroup as unknown as express.RequestHandler
);
router.patch(
  "/group/remove",
  removeGroupFromParentGroup as unknown as express.RequestHandler
);

export default router;

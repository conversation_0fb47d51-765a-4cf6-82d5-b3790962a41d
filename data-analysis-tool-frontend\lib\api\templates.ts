import axios from "@/lib/axios";

const fetchTemplateById = async ({ templateId }: { templateId: number }) => {
  const { data } = await axios.get(`/libraries/${templateId}`);
  return data.template;
};

const createTemplate = async (dataToSend: {
  name: string;
  description: string;
  sector: string;
  country: string;
}) => {
  const { data } = await axios.post(`/libraries`, dataToSend);
  return data;
};

const fetchTemplates = async () => {
  const { data } = await axios.get(`/libraries`);
  return data.templates;
};

const deleteTemplate = async (templateId: number) => {
  const { data } = await axios.delete(`/libraries/${templateId}`);
  return data;
};

const deleteTemplates = async ({ templateIds }: { templateIds: number[] }) => {
  return null;
};

export {
  createTemplate,
  fetchTemplateById,
  fetchTemplates,
  deleteTemplates,
  deleteTemplate,
};

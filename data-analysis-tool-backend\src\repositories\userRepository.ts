import bcrypt from "bcryptjs";
import { prisma } from "../utils/prisma";
import { OrganizationType, Sector, User, UserSession } from "@prisma/client";
// Or a relative path like '../../app/generated/prisma'

interface CreateSessionParams {
  userId: number;
  deviceInfo: string;
  ipAddress: string;
  browserInfo: string;
}

class UserRepository {
  /**
   * Find a user by email
   */
  async findByEmail(email: string): Promise<User | null> {
    return await prisma.user.findUnique({ where: { email } });
  }

  /**
   * Find a user by ID (only selected fields)
   */
  async findById(id: number): Promise<Partial<User> | null> {
    return await prisma.user.findUnique({
      where: { id },
      select: {
        id: true,
        email: true,
        name: true,
        country: true,
        city: true,
        bio: true,
        sector: true,
        organizationType: true,
        createdAt: true,
        updatedAt: true,
      },
    });
  }

  /**
   * Get all users
   */
  async findAll(): Promise<Partial<User>[]> {
    return await prisma.user.findMany({});
  }

  /**
   * Create new user
   */
  async create(userData: {
    name: string;
    email: string;
    password: string;
    country: string;
    sector: Sector;
    organizationType: OrganizationType;
  }): Promise<User> {
    const { name, email, password, country, sector, organizationType } =
      userData;

    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(password, salt);

    return await prisma.user.create({
      data: {
        name,
        email,
        password: hashedPassword,
        country,
        sector,
        organizationType,
      },
    });
  }

  /**
   * Update user by ID
   */
  async updateById(
    id: number,
    updateData: {
      name?: string;
      email?: string;
      country?: string;
      city?: string;
      bio?: string;
      sector?: Sector;
      organizationType?: OrganizationType;
    }
  ): Promise<Partial<User> | null> {
    const data: {
      name?: string;
      email?: string;
      country?: string;
      city?: string;
      bio?: string;
      sector?: Sector;
      organizationType?: OrganizationType;
    } = {};
    if (updateData.name !== undefined) data.name = updateData.name;
    if (updateData.email !== undefined) data.email = updateData.email;
    if (updateData.country !== undefined) data.country = updateData.country;
    if (updateData.city !== undefined) data.city = updateData.city;
    if (updateData.bio !== undefined) data.bio = updateData.bio;
    if (updateData.sector !== undefined) data.sector = updateData.sector;
    if (updateData.organizationType !== undefined)
      data.organizationType = updateData.organizationType;

    return await prisma.user.update({
      where: { id },
      data,
      select: {
        id: true,
        email: true,
        name: true,
        country: true,
        city: true,
        bio: true,
        sector: true,
        organizationType: true,
        createdAt: true,
        updatedAt: true,
      },
    });
  }

  /**
   * Update user's password
   */
  async updatePassword(id: number, newPassword: string): Promise<User> {
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(newPassword, salt);

    return await prisma.user.update({
      where: { id },
      data: { password: hashedPassword },
    });
  }

  /**
   * Delete a user by ID
   */
  async deleteById(id: number): Promise<User> {
    return await prisma.user.delete({
      where: { id },
    });
  }

  /**
   * Check if email is in use
   */
  async isEmailInUse(email: string, excludeUserId?: number): Promise<boolean> {
    const where: any = { email };
    if (excludeUserId) {
      where.id = { not: excludeUserId };
    }

    const count = await prisma.user.count({ where });
    return count > 0;
  }

  /**
   * Verify user password
   */
  async verifyPassword(id: number, password: string): Promise<boolean> {
    const user = await prisma.user.findUnique({
      where: { id },
      select: { password: true },
    });

    if (!user) return false;

    return await bcrypt.compare(password, user.password);
  }

  async saveResetToken(
    userId: number,
    hashedToken: string,
    expires: Date
  ): Promise<User | null> {
    return await prisma.user.update({
      where: { id: userId },
      data: {
        resetPasswordToken: hashedToken,
        resetPasswordExpires: expires,
      },
    });
  }

  async checkPasswordToken(hashedToken: string): Promise<User | null> {
    return await prisma.user.findFirst({
      where: {
        resetPasswordToken: hashedToken,
      },
    });
  }

  async checkPasswordTokenExpirey(hashedToken: string): Promise<User | null> {
    return await prisma.user.findFirst({
      where: {
        resetPasswordToken: hashedToken,
        resetPasswordExpires: {
          gte: new Date(),
        },
      },
    });
  }

  async resetUserPassword(
    userId: number,
    password: string
  ): Promise<User | null> {
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(password, salt);
    return await prisma.user.update({
      where: { id: userId },
      data: {
        password: hashedPassword,
        resetPasswordToken: null,
        resetPasswordExpires: null,
      },
    });
  }

  async sendEmailVerificationToken(
    userId: number,
    hasedToken: string,
    expires: Date
  ): Promise<User | null> {
    return await prisma.user.update({
      where: { id: userId },
      data: {
        emailVerificationToken: hasedToken,
        emailVerificationExpires: expires,
      },
    });
  }

  async findByemailVerificationToken(hasedToken: string): Promise<User | null> {
    return await prisma.user.findFirst({
      where: {
        emailVerificationToken: hasedToken,
      },
    });
  }

  async isEmailVerificationTokenExpired(
    hasedToken: string
  ): Promise<User | null> {
    return await prisma.user.findFirst({
      where: {
        emailVerificationToken: hasedToken,
        emailVerificationExpires: {
          gte: new Date(),
        },
      },
    });
  }

  async verifyUser(userId: number): Promise<User | null> {
    return await prisma.user.update({
      where: { id: userId },
      data: {
        isVerified: true,
        emailVerificationToken: null,
        emailVerificationExpires: null,
      },
    });
  }

  async createSession({
    userId,
    deviceInfo,
    ipAddress,
    browserInfo,
  }: CreateSessionParams): Promise<UserSession> {
    return await prisma.userSession.create({
      data: {
        userId,
        deviceInfo,
        ipAddress,
        browserInfo,
      },
    });
  }

  async getUserSessions({
    userId,
  }: {
    userId: number;
  }): Promise<UserSession[]> {
    return await prisma.userSession.findMany({
      where: { userId },
      orderBy: { createdAt: "desc" },
    });
  }

  async logoutAllDevices(userId: number): Promise<{ count: number }> {
    return await prisma.userSession.updateMany({
      where: { userId },
      data: { isActive: false },
    });
  }

  async logoutSingleDevice(
    userId: number,
    sessionId: number
  ): Promise<{
    id: number;
    updatedAt: Date;
    userId: number;
    deviceInfo: string;
    ipAddress: string;
    browserInfo: string;
    isActive: boolean;
    createdAt: Date;
  }> {
    return await prisma.userSession.update({
      where: { userId, id: sessionId },
      data: { isActive: false },
    });
  }

  async changeEmail(id: number, { email }: { email: string }) {
    return await prisma.user.update({
      where: { id },
      data: { email, isVerified: false },
    });
  }
}

export default new UserRepository();

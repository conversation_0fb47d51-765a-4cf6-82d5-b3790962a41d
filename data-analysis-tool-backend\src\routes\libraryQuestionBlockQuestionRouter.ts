import express, { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import {
  createLibraryQustionBlockQuestion,
  updateLibraryQustionBlockQuestion,
  deleteLibraryQustionBlockQuestion,
  getAllLibraryQustionBlockQuestion,
} from "../controllers/libraryQuestionBlockQuestionController";
import { authenticate } from "../middleware/auth";

const router = express.Router();

router.post(
  "/",
  authenticate,
  createLibraryQustionBlockQuestion as unknown as <PERSON><PERSON><PERSON><PERSON><PERSON>
);
router.get(
  "/",
  authenticate,
  getAllLibraryQustionBlockQuestion as unknown as <PERSON>questHandler
);
router.patch(
  "/:id",
  authenticate,
  updateLibraryQustionBlockQuestion as unknown as <PERSON><PERSON><PERSON><PERSON><PERSON>
);
router.delete(
  "/:id",
  authenticate,
  deleteLibraryQustionBlockQuestion as unknown as <PERSON><PERSON><PERSON><PERSON><PERSON>
);

export default router;

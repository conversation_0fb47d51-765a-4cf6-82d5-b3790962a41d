import express, { RequestHand<PERSON> } from "express";
import {
  createProject,
  updateProjects,
  getAllProject,
  getProjectById,
  deleteProject,
  createProjectFromLibraryTemplate,
  changeProjectStatus,
  updateManyProjectStatus,
  DeleteMultipleProject,
  fetchQuestionForForm,
} from "../controllers/projectController";
import { authenticate } from "../middleware/auth";

const router = express.Router();

router.post("/", authenticate, createProject);
router.get("/", authenticate, getAllProject);
router.patch(
  "/update-many-status",
  authenticate,
  updateManyProjectStatus as unknown as express.RequestHandler
);
router.delete(
  "/delete-multiple",
  authenticate,
  DeleteMultipleProject as unknown as express.RequestHandler
);

router.patch("/:id", authenticate, updateProjects);
router.patch(
  "/change-status/:id",
  authenticate,
  changeProjectStatus as unknown as express.RequestHandler
);
router.get("/:id", authenticate, getProjectById);
router.get(
  "/form/:id",
  authenticate,
  fetchQuestionForForm as unknown as express.RequestHandler
);

router.delete("/delete/:id", authenticate, deleteProject);

// Create a project from a library template
router.post(
  "/from-template",
  authenticate,
  createProjectFromLibraryTemplate as unknown as RequestHandler
);

export default router;

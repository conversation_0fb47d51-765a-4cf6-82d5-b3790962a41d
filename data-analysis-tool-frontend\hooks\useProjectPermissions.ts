import { useMemo } from "react";
import { Project, User } from "@/types";

export const useProjectPermissions = ({
  projectData,
  user,
}: {
  projectData: Project | undefined;
  user: User | null;
}) => {
  return useMemo(() => {
    const isOwner = user?.id === projectData?.user?.id;
    const projectUser = projectData?.projectUser?.[0];
    const perm = projectUser?.permission || {};

    return {
      viewForm: isOwner || perm.viewForm || false,
      editForm: isOwner || perm.editForm || false,
      viewSubmissions: isOwner || perm.viewSubmissions || false,
      addSubmissions: isOwner || perm.addSubmissions || false,
      deleteSubmissions: isOwner || perm.deleteSubmissions || false,
      validateSubmissions: isOwner || perm.validateSubmissions || false,
      editSubmissions: isOwner || perm.editSubmissions || false,
      manageProject: isOwner || perm.manageProject || false,
    };
  }, [user?.id, projectData]);
};

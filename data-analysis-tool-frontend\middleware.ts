// middleware.ts
import { NextRequest, NextResponse } from "next/server";

export async function middleware(request: NextRequest) {
  // Getting token from cookies
  const token = request.cookies.get("token")?.value;
  const pathname = request.nextUrl.pathname;
  
  // Define auth pages (public pages)
  const authPages = [
    "/",
    "/signup",
    "/reset-password",
    "/reset-password/change-password",
  ];
  
  const isAuthPage = authPages.includes(pathname);
  
  // Special case for form-test sign-in
  if (pathname.startsWith("/form-test") && pathname.includes("/sign-in")) {
    return NextResponse.next(); // Allow access regardless of auth status
  }
  
  // Check if token is valid (exists and not expired)
  let isValidToken = false;
  
  if (token) {
    try {
      // Try to validate the token without making an actual API call
      // This is a simple check - you might want a more sophisticated validation
      const tokenData = JSON.parse(atob(token.split('.')[1]));
      const expiry = tokenData.exp * 1000; // Convert to milliseconds
      isValidToken = Date.now() < expiry;
    } catch (error) {
      // If token parsing fails, consider it invalid
      isValidToken = false;
    }
  }
  
  // ✅ Authenticated users should not access auth pages
  if (isValidToken && isAuthPage) {
    const url = request.nextUrl.clone();
    url.pathname = "/dashboard";
    return NextResponse.redirect(url);
  }
  
  // ✅ Unauthenticated users should not access protected pages
  if (!isValidToken && !isAuthPage) {
    const url = request.nextUrl.clone();
    url.pathname = "/";
    return NextResponse.redirect(url);
  }
  
  // ✅ All good, continue as normal
  return NextResponse.next();
}

export const config = {
  matcher: ["/((?!_next/static|_next/image|favicon.ico|images|fonts).*)"],
};

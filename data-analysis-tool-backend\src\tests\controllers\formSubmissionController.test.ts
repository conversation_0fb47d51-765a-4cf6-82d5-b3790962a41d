import { Request, Response } from "express";
import {
  createFormSubmission,
  getProjectFormSubmissions,
  getFormSubmission,
  updateFormSubmission,
  deleteFormSubmission,
} from "../../controllers/formSubmissionController";
import formSubmissionRepository from "../../repositories/formSubmissionRepository";

// Mock the dependencies
jest.mock("../../repositories/formSubmissionRepository");

describe("Form Submission Controller", () => {
  let mockRequest: Partial<Request & { user?: { id: number } }>;
  let mockResponse: Partial<Response>;
  let responseObject: any = {};

  beforeEach(() => {
    // Reset mocks before each test
    jest.clearAllMocks();
    jest.resetAllMocks();

    // Setup mock response
    mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockImplementation((result) => {
        responseObject = result;
        return mockResponse;
      }),
    };

    // Reset response object
    responseObject = {};

    // Setup default authenticated user
    mockRequest = {
      user: {
        id: 1,
      },
      params: {},
      query: {},
      body: {},
    };
  });

  describe("createFormSubmission", () => {
    beforeEach(() => {
      mockRequest.body = {
        projectId: 1,
        status: "submitted",
        metadata: { key: "value" },
        answers: [
          {
            questionId: 1,
            value: "Answer 1",
            answerType: "text",
          },
        ],
      };
    });

    it("should create a form submission successfully", async () => {
      const mockSubmission = {
        id: 1,
        projectId: 1,
        status: "submitted",
        metadata: { key: "value" },
        createdAt: new Date(),
      };

      (
        formSubmissionRepository.isProjectAccessible as jest.Mock
      ).mockResolvedValue(true);
      (formSubmissionRepository.create as jest.Mock).mockResolvedValue(
        mockSubmission
      );

      await createFormSubmission(mockRequest as any, mockResponse as Response);

      expect(formSubmissionRepository.isProjectAccessible).toHaveBeenCalledWith(
        1,
        1
      );
      expect(formSubmissionRepository.create).toHaveBeenCalledWith(
        expect.objectContaining({
          projectId: 1,
          status: "submitted",
          metadata: { key: "value" },
        }),
        1,
        "Test Device",
        undefined
      );
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(responseObject).toHaveProperty("success", true);
      expect(responseObject).toHaveProperty(
        "message",
        "Form submission created successfully"
      );
      expect(responseObject.data).toHaveProperty(
        "formSubmission",
        mockSubmission
      );
    });

    it("should return 403 when user cannot access the project", async () => {
      (
        formSubmissionRepository.isProjectAccessible as jest.Mock
      ).mockResolvedValue(false);

      await createFormSubmission(mockRequest as any, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(403);
      expect(responseObject).toHaveProperty("success", false);
      expect(responseObject).toHaveProperty(
        "message",
        "You don't have access to this project"
      );
      expect(formSubmissionRepository.create).not.toHaveBeenCalled();
    });

    it("should return 400 for invalid input", async () => {
      // Missing required fields
      mockRequest.body = {};

      await createFormSubmission(mockRequest as any, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(responseObject).toHaveProperty("success", false);
      expect(responseObject).toHaveProperty("message", "Validation error");
      expect(formSubmissionRepository.create).not.toHaveBeenCalled();
    });

    it("should handle server errors", async () => {
      (
        formSubmissionRepository.isProjectAccessible as jest.Mock
      ).mockRejectedValue(new Error("Database error"));

      await createFormSubmission(mockRequest as any, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(responseObject).toHaveProperty("success", false);
      expect(responseObject).toHaveProperty(
        "message",
        "Error creating form submission"
      );
    });
  });

  describe("getProjectFormSubmissions", () => {
    beforeEach(() => {
      mockRequest.params = { projectId: "1" };
    });

    it("should get all form submissions successfully", async () => {
      const mockSubmissions = [
        {
          id: 1,
          projectId: 1,
          status: "submitted",
          createdAt: new Date(),
        },
        {
          id: 2,
          projectId: 1,
          status: "submitted",
          createdAt: new Date(),
        },
      ];

      (
        formSubmissionRepository.isProjectAccessible as jest.Mock
      ).mockResolvedValue(true);
      (formSubmissionRepository.findByProjectId as jest.Mock).mockResolvedValue(
        mockSubmissions
      );

      await getProjectFormSubmissions(
        mockRequest as any,
        mockResponse as Response
      );

      expect(formSubmissionRepository.isProjectAccessible).toHaveBeenCalledWith(
        1,
        1
      );
      expect(formSubmissionRepository.findByProjectId).toHaveBeenCalledWith(1);
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(responseObject).toHaveProperty("success", true);
      expect(responseObject).toHaveProperty(
        "message",
        "get project form success"
      );
      expect(responseObject.data).toHaveProperty(
        "formSubmissions",
        mockSubmissions
      );
    });

    it("should return 403 when user cannot access the project", async () => {
      (
        formSubmissionRepository.isProjectAccessible as jest.Mock
      ).mockResolvedValue(false);

      await getProjectFormSubmissions(
        mockRequest as any,
        mockResponse as Response
      );

      expect(mockResponse.status).toHaveBeenCalledWith(403);
      expect(responseObject).toHaveProperty("success", false);
      expect(responseObject).toHaveProperty(
        "message",
        "You don't have access to this project"
      );
      expect(formSubmissionRepository.findByProjectId).not.toHaveBeenCalled();
    });

    it("should handle invalid project ID", async () => {
      mockRequest.params = { projectId: "invalid" };

      await getProjectFormSubmissions(
        mockRequest as any,
        mockResponse as Response
      );

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(responseObject).toHaveProperty("success", false);
      expect(responseObject).toHaveProperty("message", "Invalid project ID");
    });

    it("should handle server errors", async () => {
      (
        formSubmissionRepository.isProjectAccessible as jest.Mock
      ).mockRejectedValue(new Error("Database error"));

      await getProjectFormSubmissions(
        mockRequest as any,
        mockResponse as Response
      );

      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(responseObject).toHaveProperty("success", false);
      expect(responseObject).toHaveProperty(
        "message",
        "Error retrieving form submissions"
      );
    });
  });

  describe("getFormSubmission", () => {
    beforeEach(() => {
      mockRequest.params = { id: "1" };
    });

    it("should get a form submission by ID successfully", async () => {
      const mockSubmission = {
        id: 1,
        projectId: 1,
        status: "submitted",
        createdAt: new Date(),
      };

      (formSubmissionRepository.findById as jest.Mock).mockResolvedValue(
        mockSubmission
      );
      (
        formSubmissionRepository.isProjectAccessible as jest.Mock
      ).mockResolvedValue(true);

      await getFormSubmission(mockRequest as any, mockResponse as Response);

      expect(formSubmissionRepository.findById).toHaveBeenCalledWith(1);
      expect(formSubmissionRepository.isProjectAccessible).toHaveBeenCalledWith(
        1,
        1
      );
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(responseObject).toHaveProperty("success", true);
      expect(responseObject).toHaveProperty(
        "message",
        "get form submission success"
      );
      expect(responseObject.data).toHaveProperty(
        "formSubmission",
        mockSubmission
      );
    });

    it("should return 404 when form submission not found", async () => {
      (formSubmissionRepository.findById as jest.Mock).mockResolvedValue(null);

      await getFormSubmission(mockRequest as any, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(404);
      expect(responseObject).toHaveProperty("success", false);
      expect(responseObject).toHaveProperty(
        "message",
        "Form submission not found"
      );
    });

    it("should return 403 when user cannot access the project", async () => {
      const mockSubmission = {
        id: 1,
        projectId: 1,
        status: "submitted",
        createdAt: new Date(),
      };

      (formSubmissionRepository.findById as jest.Mock).mockResolvedValue(
        mockSubmission
      );
      (
        formSubmissionRepository.isProjectAccessible as jest.Mock
      ).mockResolvedValue(false);

      await getFormSubmission(mockRequest as any, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(403);
      expect(responseObject).toHaveProperty("success", false);
      expect(responseObject).toHaveProperty(
        "message",
        "You don't have access to this form submission"
      );
    });

    it("should handle invalid submission ID", async () => {
      mockRequest.params = { id: "invalid" };

      await getFormSubmission(mockRequest as any, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(responseObject).toHaveProperty("success", false);
      expect(responseObject).toHaveProperty("message", "Invalid submission ID");
    });

    it("should handle server errors", async () => {
      (formSubmissionRepository.findById as jest.Mock).mockRejectedValue(
        new Error("Database error")
      );

      await getFormSubmission(mockRequest as any, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(responseObject).toHaveProperty("success", false);
      expect(responseObject).toHaveProperty(
        "message",
        "Error retrieving form submission"
      );
    });
  });

  describe("updateFormSubmission", () => {
    beforeEach(() => {
      mockRequest.params = { id: "1" };
      mockRequest.body = {
        status: "completed",
        metadata: { updatedKey: "updatedValue" },
      };
    });

    it("should update a form submission successfully", async () => {
      const existingSubmission = {
        id: 1,
        projectId: 1,
        status: "submitted",
        createdAt: new Date(),
      };

      const updatedSubmission = {
        ...existingSubmission,
        status: "completed",
        metadata: { updatedKey: "updatedValue" },
      };

      (formSubmissionRepository.findById as jest.Mock).mockResolvedValue(
        existingSubmission
      );
      (
        formSubmissionRepository.isProjectAccessible as jest.Mock
      ).mockResolvedValue(true);
      (formSubmissionRepository.update as jest.Mock).mockResolvedValue(
        updatedSubmission
      );

      await updateFormSubmission(mockRequest as any, mockResponse as Response);

      expect(formSubmissionRepository.findById).toHaveBeenCalledWith(1);
      expect(formSubmissionRepository.isProjectAccessible).toHaveBeenCalledWith(
        1,
        1
      );
      expect(formSubmissionRepository.update).toHaveBeenCalledWith(
        1,
        expect.objectContaining({
          status: "completed",
          metadata: { updatedKey: "updatedValue" },
        })
      );
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(responseObject).toHaveProperty("success", true);
      expect(responseObject).toHaveProperty(
        "message",
        "Form submission updated successfully"
      );
      expect(responseObject.data).toHaveProperty(
        "formSubmission",
        updatedSubmission
      );
    });

    it("should return 404 when form submission not found", async () => {
      (formSubmissionRepository.findById as jest.Mock).mockResolvedValue(null);

      await updateFormSubmission(mockRequest as any, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(404);
      expect(responseObject).toHaveProperty("success", false);
      expect(responseObject).toHaveProperty(
        "message",
        "Form submission not found"
      );
      expect(formSubmissionRepository.update).not.toHaveBeenCalled();
    });

    it("should return 403 when user cannot access the project", async () => {
      const existingSubmission = {
        id: 1,
        projectId: 1,
        status: "submitted",
        createdAt: new Date(),
      };

      (formSubmissionRepository.findById as jest.Mock).mockResolvedValue(
        existingSubmission
      );
      (
        formSubmissionRepository.isProjectAccessible as jest.Mock
      ).mockResolvedValue(false);

      await updateFormSubmission(mockRequest as any, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(403);
      expect(responseObject).toHaveProperty("success", false);
      expect(responseObject).toHaveProperty(
        "message",
        "You don't have access to this form submission"
      );
      expect(formSubmissionRepository.update).not.toHaveBeenCalled();
    });

    it("should handle invalid submission ID", async () => {
      mockRequest.params = { id: "invalid" };

      await updateFormSubmission(mockRequest as any, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(responseObject).toHaveProperty("success", false);
      expect(responseObject).toHaveProperty("message", "Invalid submission ID");
      expect(formSubmissionRepository.update).not.toHaveBeenCalled();
    });

    it("should handle server errors", async () => {
      (formSubmissionRepository.findById as jest.Mock).mockRejectedValue(
        new Error("Database error")
      );

      await updateFormSubmission(mockRequest as any, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(responseObject).toHaveProperty("success", false);
      expect(responseObject).toHaveProperty(
        "message",
        "Error updating form submission"
      );
    });
  });

  describe("deleteFormSubmission", () => {
    beforeEach(() => {
      mockRequest.params = { id: "1" };
    });

    it("should delete a form submission successfully", async () => {
      const existingSubmission = {
        id: 1,
        projectId: 1,
        status: "submitted",
        createdAt: new Date(),
      };

      (formSubmissionRepository.findById as jest.Mock).mockResolvedValue(
        existingSubmission
      );
      (
        formSubmissionRepository.isProjectAccessible as jest.Mock
      ).mockResolvedValue(true);
      (formSubmissionRepository.delete as jest.Mock).mockResolvedValue({
        id: 1,
      });

      await deleteFormSubmission(mockRequest as any, mockResponse as Response);

      expect(formSubmissionRepository.findById).toHaveBeenCalledWith(1);
      expect(formSubmissionRepository.isProjectAccessible).toHaveBeenCalledWith(
        1,
        1
      );
      expect(formSubmissionRepository.delete).toHaveBeenCalledWith(1);
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(responseObject).toHaveProperty("success", true);
      expect(responseObject).toHaveProperty(
        "message",
        "Form submission deleted successfully"
      );
    });

    it("should return 404 when form submission not found", async () => {
      (formSubmissionRepository.findById as jest.Mock).mockResolvedValue(null);

      await deleteFormSubmission(mockRequest as any, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(404);
      expect(responseObject).toHaveProperty("success", false);
      expect(responseObject).toHaveProperty(
        "message",
        "Form submission not found"
      );
      expect(formSubmissionRepository.delete).not.toHaveBeenCalled();
    });

    it("should return 403 when user cannot access the project", async () => {
      const existingSubmission = {
        id: 1,
        projectId: 1,
        status: "submitted",
        createdAt: new Date(),
      };

      (formSubmissionRepository.findById as jest.Mock).mockResolvedValue(
        existingSubmission
      );
      (
        formSubmissionRepository.isProjectAccessible as jest.Mock
      ).mockResolvedValue(false);

      await deleteFormSubmission(mockRequest as any, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(403);
      expect(responseObject).toHaveProperty("success", false);
      expect(responseObject).toHaveProperty(
        "message",
        "You don't have access to this form submission"
      );
      expect(formSubmissionRepository.delete).not.toHaveBeenCalled();
    });

    it("should handle invalid submission ID", async () => {
      mockRequest.params = { id: "invalid" };

      await deleteFormSubmission(mockRequest as any, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(responseObject).toHaveProperty("success", false);
      expect(responseObject).toHaveProperty("message", "Invalid submission ID");
      expect(formSubmissionRepository.delete).not.toHaveBeenCalled();
    });

    it("should handle server errors", async () => {
      (formSubmissionRepository.findById as jest.Mock).mockRejectedValue(
        new Error("Database error")
      );

      await deleteFormSubmission(mockRequest as any, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(responseObject).toHaveProperty("success", false);
      expect(responseObject).toHaveProperty(
        "message",
        "Error deleting form submission"
      );
    });
  });
});

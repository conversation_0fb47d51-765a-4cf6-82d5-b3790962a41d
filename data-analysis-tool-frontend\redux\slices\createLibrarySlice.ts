import { createSlice } from "@reduxjs/toolkit";

type CreateLibraryState = {
  visible: boolean;
};

const initialState: CreateLibraryState = {
  visible: false,
};

const createLibrarySlice = createSlice({
  name: "createLibraryItem",
  initialState,
  reducers: {
    showCreateLibraryModal: (state) => {
      state.visible = true;
    },
    hideCreateLibraryModal: (state) => {
      state.visible = false;
    },
  },
});

export const { showCreateLibraryModal, hideCreateLibraryModal } =
  createLibrarySlice.actions;
export default createLibrarySlice.reducer;

import { createTemplate } from "@/lib/api/templates";
import { showNotification } from "@/redux/slices/notificationSlice";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { Briefcase, FileText, Globe } from "lucide-react";
import React, { useEffect, useState } from "react";
import { FieldValues, useForm } from "react-hook-form";
import { useDispatch } from "react-redux";
import { Select } from "../general/Select";
import countries from "@/constants/countryNames.json";
import { SectorLabelMap } from "@/constants/sectors";
import { labelToKey } from "@/lib/labelToKey";
import { useAuth } from "@/hooks/useAuth";

const CreateLibraryTemplate = ({
  handleClose,
}: {
  handleClose: () => void;
}) => {
  const dispatch = useDispatch();

  const {
    register,
    formState: { isSubmitting, errors, isSubmitted },
    handleSubmit,
    setValue,
  } = useForm();

  const [selectedCountry, setSelectedCountry] = useState<string | null>(null);
  const [selectedSector, setSelectedSector] = useState<string | null>(null);

  useEffect(() => {
    register("country", { required: "Please select a country" });
    register("sector", { required: "Please select a sector" });
  }, [register]);

  useEffect(() => {
    setValue("country", selectedCountry, { shouldValidate: isSubmitted });
    setValue("sector", selectedSector, { shouldValidate: isSubmitted });
  }, [setValue, selectedCountry, selectedSector]);

  const queryClient = useQueryClient();

  const { user } = useAuth();

  const templateMutation = useMutation({
    mutationFn: createTemplate,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["templates", user?.id] });
      handleClose();
      dispatch(
        showNotification({
          type: "success",
          message: "Template created successfully",
        })
      );
    },
    onError: () => {
      dispatch(
        showNotification({
          type: "error",
          message: "Failed to create template. Please try again",
        })
      );
    },
  });

  const onSubmit = async (data: FieldValues) => {
    const dataToSend = {
      name: data.name,
      description: data.description,
      sector: data.sector,
      country: data.country,
    };

    templateMutation.mutate(dataToSend);
  };

  return (
    <form className="flex flex-col gap-8" onSubmit={handleSubmit(onSubmit)}>
      <h1 className="heading-text capitalize">Create new project template</h1>
      <div className="flex flex-col gap-4">
        {/* Project Name */}
        <div className="label-input-group group">
          <label htmlFor="project-name" className="label-text">
            <FileText size={16} /> Template Name
          </label>
          <input
            {...register("name", {
              required: "Template name is required.",
            })}
            id="project-name"
            type="text"
            className="input-field"
            placeholder="Enter a project name"
          />
          {errors.name && (
            <p className="text-red-500 text-sm">{`${errors.name.message}`}</p>
          )}
        </div>
        {/* Project Description */}
        <div className="label-input-group group">
          <label htmlFor="description" className="label-text">
            Description
          </label>
          <textarea
            id="description"
            {...register("description", {
              required: "Please enter the template description",
            })}
            className="input-field resize-none"
            cols={4}
            placeholder="Enter the project description"
          />
          {errors.description && (
            <p className="text-red-500 text-sm">{`${errors.description.message}`}</p>
          )}
        </div>
        {/* Country and Sector */}
        <div className="grid grid-cols-2 gap-4">
          <div className="label-input-group group">
            <label htmlFor="country" className="label-text">
              <Globe size={16} />
              Country
            </label>
            <Select
              id={`country`}
              options={countries}
              value={selectedCountry}
              onChange={setSelectedCountry}
            />
            {errors.country && (
              <p className="text-red-500 text-sm">{`${errors.country.message}`}</p>
            )}
          </div>
          <div className="label-input-group group">
            <label htmlFor="sector" className="label-text">
              <Briefcase size={16} /> Sector
            </label>
            <Select
              id={`sector`}
              options={Object.values(SectorLabelMap)} // Display labels
              value={
                selectedSector && SectorLabelMap[selectedSector]
                  ? SectorLabelMap[selectedSector]
                  : "Select an option"
              }
              onChange={(label) => {
                const selectedKey = labelToKey(label, SectorLabelMap);
                setSelectedSector(selectedKey); // Set the enum key for storage
              }}
            />
            {errors.sector && (
              <p className="text-red-500 text-sm">{`${errors.sector.message}`}</p>
            )}
          </div>
        </div>
        <button type="submit" className="btn-primary self-end">
          {isSubmitting ? (
            <span className="flex items-center gap-2">
              Creating{" "}
              <div className="size-4 animate-spin border-x border-neutral-100 rounded-full"></div>
            </span>
          ) : (
            "Create Project"
          )}
        </button>
      </div>
    </form>
  );
};

export { CreateLibraryTemplate };

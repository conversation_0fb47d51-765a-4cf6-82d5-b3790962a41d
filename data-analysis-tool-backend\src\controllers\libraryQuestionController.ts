import { Request, Response } from "express";
import { ApiResponse } from "../utils/ApiResponse";
import {
  libraryQuestionSchema,
  libraryQuestionOptionSchema,
  libraryQuestionConditionSchema,
} from "../validators/libraryQuestionValidators";
import libraryQuestionRepository from "../repositories/libraryQuestionRepository";
import { z } from "zod";
import libraryTemplateRepository from "../repositories/libraryTemplateRepository";

interface UserRequest extends Request {
  user?: {
    id: number;
  };
}

type LibraryQuestionInput = z.infer<typeof libraryQuestionSchema>;
type LibraryQuestionOption = z.infer<typeof libraryQuestionOptionSchema>;
type LibraryQuestionCondition = z.infer<typeof libraryQuestionConditionSchema>;

export const createLibraryQuestion = async (
  req: UserRequest,
  res: Response
): Promise<void> => {
  try {
    const userId = req.user!.id;
    const libraryTemplateId = Number(req.params.libraryTemplateId);

    // Check if user owns the library template
    const isOwner = await libraryQuestionRepository.isLibraryTemplateOwner(
      userId,
      libraryTemplateId
    );

    if (!isOwner) {
      res.status(403).json({
        success: false,
        message: "User is not associated with this library template",
      });
      return;
    }
    // Validate input data
    const result = libraryQuestionSchema.safeParse(req.body);
    if (!result.success) {
      res.status(400).json({
        success: false,
        errors: result.error.flatten().fieldErrors,
      });
      return;
    }

    // Create the question - transforming options and conditions to match repository format
    const questionData = result.data as LibraryQuestionInput;

    const question = await libraryQuestionRepository.create({
      ...questionData,
      libraryTemplateId,
      questionOptions: questionData.questionOptions
        ? questionData.questionOptions.map((opt: LibraryQuestionOption) => ({
            label: opt.label,
            code: opt.code,
            nextLibraryQuestionId: opt.nextLibraryQuestionId,
          }))
        : undefined,
      conditions: questionData.conditions
        ? questionData.conditions.map((cond: LibraryQuestionCondition) => ({
            operator: cond.operator,
            value: cond.value,
          }))
        : undefined,
    });

    res
      .status(201)
      .json(
        new ApiResponse(
          201,
          { question },
          "Library question created successfully"
        )
      );
    return;
  } catch (error: unknown) {
    res.status(500).json({
      success: false,
      message: "Error creating library question",
      error: error instanceof Error ? error.message : "Unexpected error",
    });
    return;
  }
};

export const getAllLibraryQuestions = async (
  req: UserRequest,
  res: Response
): Promise<void> => {
  try {
    const userId = req.user!.id;
    const libraryTemplateId = Number(req.params.libraryTemplateId);

    // Check if user owns the library template
    const isOwner = await libraryQuestionRepository.isLibraryTemplateOwner(
      userId,
      libraryTemplateId
    );

    if (!isOwner) {
      res.status(403).json({
        success: false,
        message: "User is not associated with this library template",
      });
      return;
    }

    // Get all questions for the template
    const questions = await libraryQuestionRepository.findByLibraryTemplateId(
      libraryTemplateId
    );

    res.status(200).json({
      success: true,
      message: "Successfully fetched template questions",
      questions,
    });
    return;
  } catch (error: unknown) {
    res.status(500).json({
      success: false,
      message: "Error retrieving library questions",
      error: error instanceof Error ? error.message : "Unexpected error",
    });
    return;
  }
};

export const getLibraryQuestionById = async (
  req: UserRequest,
  res: Response
): Promise<void> => {
  try {
    const userId = req.user!.id;
    const questionId = Number(req.params.id);

    // Get the question
    const question = await libraryQuestionRepository.findById(questionId);

    if (!question) {
      res.status(404).json({
        success: false,
        message: "Library question not found",
      });
      return;
    }

    // Check if user owns the library template
    const isOwner = await libraryQuestionRepository.isLibraryTemplateOwner(
      userId,
      question.libraryTemplateId
    );

    if (!isOwner) {
      res.status(403).json({
        success: false,
        message: "User is not associated with this library template",
      });
      return;
    }

    res
      .status(200)
      .json(
        new ApiResponse(
          200,
          { question },
          "Library question retrieved successfully"
        )
      );
    return;
  } catch (error: unknown) {
    res.status(500).json({
      success: false,
      message: "Error retrieving library question",
      error: error instanceof Error ? error.message : "Unexpected error",
    });
    return;
  }
};

export const updateLibraryQuestion = async (
  req: UserRequest,
  res: Response
): Promise<void> => {
  try {
    const userId = req.user!.id;
    const questionId = Number(req.params.id);

    // Get the question to check ownership
    const existingQuestion = await libraryQuestionRepository.findById(
      questionId
    );

    if (!existingQuestion) {
      res.status(404).json({
        success: false,
        message: "Library question not found",
      });
      return;
    }

    // Check if user owns the library template
    const isOwner = await libraryQuestionRepository.isLibraryTemplateOwner(
      userId,
      existingQuestion.libraryTemplateId
    );

    if (!isOwner) {
      res.status(403).json({
        success: false,
        message: "User is not associated with this library template",
      });
      return;
    }

    // Create a schema for update that omits libraryTemplateId and makes all fields optional
    const updateSchema = libraryQuestionSchema.partial();

    // Validate input data
    const result = updateSchema.safeParse(req.body);
    if (!result.success) {
      res.status(400).json({
        success: false,
        errors: result.error.flatten().fieldErrors,
      });
      return;
    }

    // Transform options and conditions to match repository format
    const questionData = result.data as Partial<LibraryQuestionInput>;

    // Update the question
    const updatedQuestion = await libraryQuestionRepository.update(questionId, {
      ...questionData,
      questionOptions: questionData.questionOptions
        ? questionData.questionOptions.map((opt: LibraryQuestionOption) => ({
            id: opt.id,
            label: opt.label,
            code: opt.code,
            nextLibraryQuestionId: opt.nextLibraryQuestionId,
          }))
        : undefined,
      conditions: questionData.conditions
        ? questionData.conditions.map((cond: LibraryQuestionCondition) => ({
            id: cond.id,
            operator: cond.operator,
            value: cond.value,
          }))
        : undefined,
    });

    res
      .status(200)
      .json(
        new ApiResponse(
          200,
          { question: updatedQuestion },
          "Library question updated successfully"
        )
      );
    return;
  } catch (error: unknown) {
    res.status(500).json({
      success: false,
      message: "Error updating library question",
      error: error instanceof Error ? error.message : "Unexpected error",
    });
    return;
  }
};

export const duplicateTemplateQuestion = async (
  req: UserRequest,
  res: Response
) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res
        .status(403)
        .json({ success: false, message: "Unauthorized user" });
    }

    const id = Number(req.params.id);

    if (!id) {
      return res.status(400).json({
        success: false,
        message: "Invalid request: Question ID is missing",
      });
    }

    const currentQuestion = await libraryQuestionRepository.findById(id);
    if (!currentQuestion) {
      return res.status(404).json({
        success: false,
        message: "Question not found with the provided question id",
      });
    }

    const isTemplateOwner = await libraryTemplateRepository.isOwner(
      userId,
      currentQuestion.libraryTemplateId
    );

    if (!isTemplateOwner) {
      return res.status(403).json({
        success: false,
        message: "Unauthorized user",
      });
    }

    const duplicatedQuestion =
      await libraryQuestionRepository.duplicateQuestion(
        id,
        currentQuestion.libraryTemplateId
      );

    return res.status(200).json({
      message: "Successfully duplicated the question",
      success: true,
      duplicatedQuestion,
    });
  } catch (error: unknown) {
    return res.status(500).json({
      message:
        error instanceof Error
          ? `Failed to duplicate question: ${error.message}`
          : "An unexpected error occured while trying to duplicate question",
    });
  }
};

export const deleteLibraryQuestion = async (
  req: UserRequest,
  res: Response
): Promise<void> => {
  try {
    const userId = req.user!.id;
    const questionId = Number(req.params.id);

    // Get the question to check ownership
    const existingQuestion = await libraryQuestionRepository.findById(
      questionId
    );

    if (!existingQuestion) {
      res.status(404).json({
        success: false,
        message: "Library question not found",
      });
      return;
    }

    // Check if user owns the library template
    const isOwner = await libraryQuestionRepository.isLibraryTemplateOwner(
      userId,
      existingQuestion.libraryTemplateId
    );

    if (!isOwner) {
      res.status(403).json({
        success: false,
        message: "User is not associated with this library template",
      });
      return;
    }

    // Delete the question
    await libraryQuestionRepository.delete(questionId);

    res
      .status(200)
      .json(new ApiResponse(200, {}, "Library question deleted successfully"));
    return;
  } catch (error: unknown) {
    res.status(500).json({
      success: false,
      message: "Error deleting library question",
      error: error instanceof Error ? error.message : "Unexpected error",
    });
    return;
  }
};

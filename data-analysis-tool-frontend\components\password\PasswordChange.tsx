import { showNotification } from "@/redux/slices/notificationSlice";
import axios from "@/lib/axios";
import { <PERSON>O<PERSON>, Eye } from "lucide-react";
import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { useDispatch } from "react-redux";

const PasswordChange = () => {
  const [isChangingPassword, setIsChangingPassword] = useState(false);
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const apiUrl = process.env.NEXT_PUBLIC_API_URL || "http://localhost:4000/api";
  const dispatch = useDispatch();

  // Initialize React Hook Form
  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
  } = useForm({
    defaultValues: {
      currentPassword: "",
      newPassword: "",
      confirmPassword: "",
      email: "",
    },
  });

  const watchNewPassword = watch("newPassword");
  const watchConfirmPassword = watch("confirmPassword");

  // Handle form submission
  const handleChangePassword = async (data: any) => {
    const { currentPassword, newPassword, confirmPassword } = data;

    // Client-side validation
    if (newPassword !== confirmPassword) {
      dispatch(
        showNotification({
          message: "New password and confirm password do not match.",
          type: "error",
        })
      );
      return;
    }

    try {
      const response = await axios.post(`/users/changepassword`, {
        currentPassword,
        newPassword,
        confirmPassword,
      });

      if (response.status === 200) {
        dispatch(
          showNotification({
            message: "Password changed successfully",
            type: "success",
          })
        );
        setIsChangingPassword(false);
        // Reset fields
        setValue("currentPassword", "");
        setValue("newPassword", "");
        setValue("confirmPassword", "");
      }
    } catch (error: any) {
      dispatch(
        showNotification({
          message: error.response?.data?.message || "Server error",
          type: "error",
        })
      );
    }
  };

  return (
    <div>
      {isChangingPassword ? (
        <form
          onSubmit={handleSubmit(handleChangePassword)}
          className="flex-col flex gap-4"
        >
          <div className="flex flex-col gap-2">
            {/* CURRENT PASSWORD */}
            <div className="label-input-group group">
              <label htmlFor="current-password" className="label-text">
                Current Password
              </label>
              <div className="relative laptop:w-1/3">
                <input
                  id="current-password"
                  type={showCurrentPassword ? "text" : "password"}
                  placeholder="Enter current password"
                  className="input-field w-full pr-10"
                  {...register("currentPassword", {
                    required: "Current password is required",
                  })}
                />
                <button
                  type="button"
                  tabIndex={-1}
                  className="absolute right-2 top-1/2 -translate-y-1/2 text-gray-500"
                  onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                >
                  {showCurrentPassword ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                  <span className="sr-only">
                    {showCurrentPassword ? "Hide" : "Show"} password
                  </span>
                </button>
              </div>
              {errors.currentPassword && (
                <p className="text-red-500 text-sm">
                  {errors.currentPassword.message}
                </p>
              )}
            </div>

            {/* NEW PASSWORD */}
            <div className="label-input-group group">
              <label htmlFor="new-password" className="label-text">
                New Password
              </label>
              <div className="relative laptop:w-1/3">
                <input
                  id="new-password"
                  type={showNewPassword ? "text" : "password"}
                  placeholder="Enter new password"
                  className="input-field w-full pr-10"
                  {...register("newPassword", {
                    required: "New password is required",
                    minLength: {
                      value: 6,
                      message: "Password must be at least 6 characters",
                    },
                    validate: (value) =>
                      value !== watch("currentPassword") || "New password must be different from current password",
                  })}
                />
                <button
                  type="button"
                  tabIndex={-1}
                  className="absolute right-2 top-1/2 -translate-y-1/2 text-gray-500"
                  onClick={() => setShowNewPassword(!showNewPassword)}
                >
                  {showNewPassword ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                  <span className="sr-only">
                    {showNewPassword ? "Hide" : "Show"} password
                  </span>
                </button>
              </div>
              {errors.newPassword && (
                <p className="text-red-500 text-sm">
                  {errors.newPassword.message}
                </p>
              )}
            </div>

            {/* CONFIRM PASSWORD */}
            <div className="label-input-group group">
              <label htmlFor="confirm-password" className="label-text">
                Confirm Password
              </label>
              <div className="relative laptop:w-1/3">
                <input
                  id="confirm-password"
                  type={showConfirmPassword ? "text" : "password"}
                  placeholder="Enter confirm password"
                  className="input-field w-full pr-10"
                  {...register("confirmPassword", {
                    required: "Please confirm your password",
                    validate: (value) =>
                      value === watchNewPassword || "Passwords do not match",
                  })}
                />
                <button
                  type="button"
                  tabIndex={-1}
                  className="absolute right-2 top-1/2 -translate-y-1/2 text-gray-500"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                >
                  {showConfirmPassword ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                  <span className="sr-only">
                    {showConfirmPassword ? "Hide" : "Show"} password
                  </span>
                </button>
              </div>
              {errors.confirmPassword && (
                <p className="text-red-500 text-sm">
                  {errors.confirmPassword.message}
                </p>
              )}
            </div>
          </div>

          <div className="flex gap-2">
            <button type="submit" className="btn-primary">
              Update Password
            </button>
            <button
              className="btn-outline"
              onClick={() => setIsChangingPassword(false)}
            >
              Cancel
            </button>
          </div>
        </form>
      ) : (
        <div className="flex items-center justify-between">
          <button
            className="btn-primary"
            onClick={() => setIsChangingPassword(true)}
          >
            Change password
          </button>
        </div>
      )}
    </div>
  );
};

export { PasswordChange };

function dispatch(arg0: any) {
  throw new Error("Function not implemented.");
}

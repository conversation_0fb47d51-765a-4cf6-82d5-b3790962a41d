import {
  createAnswer,
  getAnswerById,
  getAnswersBySubmission,
  updateAnswer,
  deleteAnswer,
  submitMultipleAnswer,
  updateMultipleAnswers,
} from "../controllers/answerController";
import express from "express";
import { checkPermission } from "../middleware/checkPermission";

const router = express.Router();

import { authenticate } from "../middleware/auth";

router.use(authenticate);

router.post(
  "/",
  checkPermission([
    "manageProject",
    "addSubmissions",
  ]) as unknown as express.RequestHandler,
  createAnswer as unknown as express.RequestHandler
);
router.get(
  "/",
  checkPermission([
    "manageProject",
    "viewSubmissions",
  ]) as unknown as express.RequestHandler,
  getAnswersBySubmission as unknown as express.RequestHandler
);
router.delete(
  "/",
  checkPermission([
    "manageProject",
    "deleteSubmissions",
  ]) as unknown as express.RequestHandler,
  deleteAnswer as unknown as express.RequestHandler
);

router.patch(
  "/multiple",
  checkPermission([
    "manageProject",
    "editSubmissions",
  ]) as unknown as express.RequestHandler,
  updateMultipleAnswers as unknown as express.RequestHandler
);

router.post(
  "/multiple",
  // checkPermission("addSubmissions") as unknown as express.RequestHandler,
  submitMultipleAnswer as unknown as express.RequestHandler
);

router.get(
  "/:id",
  checkPermission([
    "manageProject",
    "viewSubmissions",
  ]) as unknown as express.RequestHandler,
  getAnswerById as unknown as express.RequestHandler
);
router.patch(
  "/:id",
  checkPermission([
    "manageProject",
    "editSubmissions",
  ]) as unknown as express.RequestHandler,
  updateAnswer as unknown as express.RequestHandler
);

export default router;

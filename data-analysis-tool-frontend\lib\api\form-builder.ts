import axios from "@/lib/axios";
import { ContextType } from "@/types";

const getQuestionsEndPoint = (contextType: ContextType) => {
  if (contextType === "project") return "/questions";
  else if (contextType === "template") return "/template-questions";
  else if (contextType === "questionBlock") return "/question-blocks";
  throw new Error("Unsupported context type");
};

const fetchQuestions = async ({ projectId }: { projectId: number }) => {
  const { data } = await axios.get(`/questions/${projectId}`);
  return data.questions;
};

const fetchTemplateQuestions = async ({
  templateId,
}: {
  templateId: number;
}) => {
  const { data } = await axios.get(`/template-questions/${templateId}`);
  return data.questions;
};

const addQuestion = async ({
  contextType,
  contextId,
  dataToSend,
  position,
}: {
  contextType: ContextType;
  contextId: number;
  dataToSend: {
    label: string;
    isRequired: boolean;
    hint?: string;
    placeholder?: string;
    inputType: string;
    questionOptions?: {
      label: string;
      sublabel?: string;
      code: string;
    }[];
  };
  position?: number;
}) => {
  // For question blocks, we don't need to include the contextId in the URL
  // The userId is taken from the authenticated user in the backend
  const url =
    contextType === "questionBlock"
      ? `${getQuestionsEndPoint(contextType)}`
      : `${getQuestionsEndPoint(contextType)}/${contextId}`;

  const { data } = await axios.post(url, {
    ...dataToSend,
    position: position || 1,
  });
  return data;
};

const deleteQuestion = async ({
  contextType,
  id,
  projectId,
}: {
  contextType: ContextType;
  id: number;
  projectId: number;
}) => {
  const { data } = await axios.delete(
    `${getQuestionsEndPoint(contextType)}/${id}?projectId=${projectId}`
  );
  return data;
};

const duplicateQuestion = async ({
  id,
  contextType,
  contextId,
}: {
  id: number;
  contextType: ContextType;
  contextId: number;
}) => {
  // For question blocks, we don't need to send the contextId in the body
  // The userId is taken from the authenticated user in the backend
  const requestBody =
    contextType === "questionBlock"
      ? {}
      : contextType === "project"
        ? { projectId: contextId }
        : { templateId: contextId };

  const { data } = await axios.post(
    `${getQuestionsEndPoint(
      contextType
    )}/duplicate/${id}?projectId=${contextId}`,
    requestBody
  );

  return data;
};

const updateQuestion = async ({
  id,
  contextType,
  dataToSend,
  contextId,
}: {
  id: number;
  contextType: ContextType;
  dataToSend: {
    label: string;
    isRequired: boolean;
    hint: string;
    placeholder: string;
    position?: number; // Optional position field to preserve question order
    questionOptions?: {
      label: string;
      sublabel?: string;
      code: string;
    }[];
  };
  contextId: number;
}) => {
  const { data } = await axios.patch(
    `${getQuestionsEndPoint(contextType)}/${id}?projectId=${contextId}`,
    dataToSend
  );
  return data;
};

const fetchQuestionBlockQuestions = async () => {
  try {
    const response = await axios.get(`/question-blocks`);
    return response.data.questions || [];
  } catch (error) {
    console.error("Error fetching question block questions:", error);
    throw error;
  }
};

const updateQuestionPositions = async ({
  contextType,
  contextId,
  questionPositions,
}: {
  contextType: ContextType;
  contextId: number;
  questionPositions: { id: number; position: number }[];
}) => {
  // Only support position updates for projects currently
  if (contextType !== "project") {
    throw new Error("Question position updates are only supported for projects");
  }

  const url = `${getQuestionsEndPoint(contextType)}/positions?projectId=${contextId}`;
  const payload = { questionPositions };

  try {
    const { data } = await axios.patch(url, payload);
    return data;
  } catch (error: any) {
    console.error("Update failed - Full error:", error);
    console.error("Update failed - Error details:", {
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      message: error.message,
      config: {
        url: error.config?.url,
        method: error.config?.method,
        data: error.config?.data,
      },
    });
    throw error;
  }
};

export {
  fetchQuestions,
  fetchTemplateQuestions,
  addQuestion,
  deleteQuestion,
  duplicateQuestion,
  updateQuestion,
  fetchQuestionBlockQuestions,
  updateQuestionPositions,
};

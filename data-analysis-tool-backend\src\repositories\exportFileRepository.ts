import { PrismaClient } from "@prisma/client";
const prisma = new PrismaClient();

interface CreateExportFile {
  projectId: number;
  userId?: number | null;
  fileName: string;
  fileType: string;
  contentType: string;
  fileBuffer: Buffer;
}

class ExportFileRepository {
  async create(data: CreateExportFile) {
    return await prisma.exportedFile.create({
      data: {
        projectId: data.projectId,
        userId: data.userId,
        fileName: data.fileName,
        fileType: data.fileType,
        contentType: data.contentType,
        fileBuffer: data.fileBuffer,
      },
    });
  }

  async findById(id: number) {
    return await prisma.exportedFile.findUnique({
      where: { id },
    });
  }

  async findAll(userId: number, projectId: number) {
    return await prisma.exportedFile.findMany({
      where: {
        userId: userId,
        projectId: projectId,
      },
    });
  }

  async deleteFile(formId: number) {
    return await prisma.exportedFile.delete({
      where: {
        id: formId,
      },
    });
  }
}

export default new ExportFileRepository();

"use client";

import { useAuth } from "@/hooks/useAuth";
import axios from "@/lib/axios";
import { log } from "console";
import { Globe, LogOut } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import React, { useEffect, useRef, useState } from "react";
import { TbLayoutDashboardFilled } from "react-icons/tb";

type NavbarProps = {
  toggleSidebar: () => void;
  isSidebarOpen: boolean;
  navbarRef: React.RefObject<HTMLElement | null>;
};

const Navbar: React.FC<NavbarProps> = ({ toggleSidebar, navbarRef }) => {
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const router = useRouter();

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const { user, logout } = useAuth();

  return (
    <header className="bg-primary-800 p-4 sticky top-0 z-40" ref={navbarRef}>
      <div className=" flex items-center justify-between">
        <div className="flex items-center gap-4">
          <button
            onClick={toggleSidebar}
            className="laptop:hidden text-neutral-100 p-2 rounded hover:bg-primary-700 "
          >
            <TbLayoutDashboardFilled size={24} />
          </button>

          <Link
            href={"/dashboard"}
            className="text-neutral-100 text-2xl font-bold cursor-pointer hover:text-neutral-300 transition-all ease-in-out"
          >
            Data Analysis
          </Link>
        </div>

        <div className="relative" ref={dropdownRef}>
          <button
            onClick={() => setIsDropdownOpen(!isDropdownOpen)}
            className="w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center text-neutral-100 font-semibold cursor-pointer"
          >
            {user?.name[0].toUpperCase()}
          </button>
          {isDropdownOpen && (
            <div className="absolute right-0 mt-2 w-64 bg-neutral-100 rounded-md shadow-lg p-4 flex flex-col gap-2 ">
              <div className="flex flex-col gap-4">
                <div className="flex items-center space-x-2">
                  <div className="size-10 bg-orange-500 rounded-full flex items-center justify-center text-neutral-100 text-xl font-semibold shrink-0">
                    {user?.name[0].toUpperCase()}
                  </div>
                  <div className="flex flex-col min-w-0">
                    <div className="font-medium capitalize">{user?.name}</div>
                    <div className="text-sm text-neutral-700 truncate">
                      {user?.email}
                    </div>
                  </div>
                </div>

                <Link
                  href="/account/profile"
                  className="btn-primary"
                  onClick={() => {
                    setIsDropdownOpen(false);
                  }}
                >
                  Profile
                </Link>
              </div>
              <hr className="border-neutral-400" />

              <div className="flex flex-col">
                <Link
                  onClick={() => setIsDropdownOpen(false)}
                  href={"/terms"}
                  className="text-sm text-neutral-700 hover:bg-neutral-700/10 px-4 py-1 rounded-md"
                >
                  Terms of Service
                </Link>
                <Link
                  onClick={() => setIsDropdownOpen(false)}
                  href={"/policy"}
                  className="block px-4 py-2 text-sm text-neutral-700 hover:bg-neutral-200"
                >
                  Privacy Policy
                </Link>
              </div>
              <hr className="border-neutral-400" />
              <Link
                href="#"
                className="flex items-center px-4 py-2 gap-2 text-sm rounded-md text-neutral-700 hover:bg-neutral-700/10 transition-all duration-300"
              >
                <Globe size={16} />
                Language
              </Link>
              <hr className="border-neutral-400" />

              <button
                onClick={logout}
                className="flex items-center text-neutral-700 gap-2 px-4 py-2 hover:bg-neutral-700/10 rounded-md active:scale-95 transition-all duration-300 w-full cursor-pointer "
              >
                <LogOut size={16} />
                Logout
              </button>
            </div>
          )}
        </div>
      </div>
    </header>
  );
};

export default Navbar;

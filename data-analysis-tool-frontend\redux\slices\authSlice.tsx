import { AuthState, UserSession } from "@/types/authTypes";
import { createSlice, PayloadAction } from "@reduxjs/toolkit";

const initialState: AuthState = {
  status: "loading",
  user: null,
  error: null,
};

const authSlice = createSlice({
  name: "auth",
  initialState,
  reducers: {
    setAuthenticatedUser: (state, action: PayloadAction<UserSession>) => {
      state.status = "authenticated";
      state.user = action.payload;
      state.error = null;
    },
    setUnauthenticated: (state) => {
      state.status = "unauthenticated";
      state.user = null;
      state.error = null;
    },
    setAuthLoading: (state) => {
      state.status = "loading";
    },
    setAuthError: (state, action: PayloadAction<string>) => {
      state.status = "unauthenticated";
      state.error = action.payload;
      state.user = null;
    },
  },
});

export const {
  setAuthenticatedUser,
  setUnauthenticated,
  setAuthLoading,
  setAuthError,
} = authSlice.actions;

export default authSlice.reducer;

/*
  Warnings:

  - You are about to drop the column `library_question_id` on the `answers` table. All the data in the column will be lost.
  - Added the required column `code` to the `LibraryQuestionOption` table without a default value. This is not possible if the table is not empty.
  - Added the required column `code` to the `QuestionOption` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE "answers" DROP CONSTRAINT "answers_library_question_id_fkey";

-- AlterTable
ALTER TABLE "LibraryQuestionOption" ADD COLUMN     "code" TEXT NOT NULL;

-- AlterTable
ALTER TABLE "QuestionOption" ADD COLUMN     "code" TEXT NOT NULL;

-- AlterTable
ALTER TABLE "answers" DROP COLUMN "library_question_id";

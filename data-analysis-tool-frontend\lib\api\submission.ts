import axios from "@/lib/axios";

// Delete form submission
const deleteFormSubmission = async (submissionId: number, projectId: number) => {
  try {
    const { data } = await axios.delete(`/form-submissions/${submissionId}?projectId=${projectId}`);
    return data;
  } catch (error) {
    console.error("Error deleting form submission:", error);
    throw error;
  }
};

// Delete multiple form submissions
const deleteMultipleFormSubmissions = async (submissionIds: number[], projectId: number) => {
  try {
    const deletePromises = submissionIds.map((id) =>
      axios.delete(`/form-submissions/${id}?projectId=${projectId}`)
    );
    const results = await Promise.all(deletePromises);
    return results.map((result) => result.data);
  } catch (error) {
    console.error("Error deleting multiple form submissions:", error);
    throw error;
  }
};

// Update a single answer
const updateAnswer = async (
  answerData: {
    submissionId: number;
    questionId: number | null | undefined;
    answerType: string;
    value: string | string[] | number;
    questionOptionId?: number | number[] | null;
  },
  projectId: number
) => {
  try {
    if (!answerData.submissionId || !answerData.questionId) {
      throw new Error("submissionId and questionId are required");
    }

    const formattedData = { ...answerData };
    if (formattedData.questionOptionId === null) {
      delete formattedData.questionOptionId;
    } else if (Array.isArray(formattedData.questionOptionId)) {
      formattedData.questionOptionId = formattedData.questionOptionId.filter((id) => id != null);
      if (formattedData.questionOptionId.length === 0) {
        delete formattedData.questionOptionId;
      }
    }

    const { data } = await axios.patch(
      `/answers/${answerData.questionId}?projectId=${projectId}`,
      formattedData
    );
    return data;
  } catch (error) {
    console.error("Error updating answer:", error);
    throw error;
  }
};

// Update multiple answers for a single question (for select_many type)
const updateMultipleAnswers = async (answerData: {
  submissionId: number;
  questionId: number;
  answerType: string;
  value: string[];
  questionOptionId?: number[];
}) => {
  try {
    const { data } = await axios.patch(`/answers/${answerData.questionId}`, {
      ...answerData,
      answerType: "selectmany",
    });
    return data;
  } catch (error) {
    console.error("Error updating multiple answers:", error);
    throw error;
  }
};

// Update multiple answers using the /answers/multiple endpoint
const updateMultipleAnswersWithEndpoint = async (
  answers: Array<{
    id?: number;
    questionId?: number;
    projectId: number;
    value: any;
    answerType: string;
    questionOptionId?: number | number[];
    isOtherOption?: boolean;
    formSubmissionId: number;
  }>,
  projectId: number
) => {
  try {
    const { data } = await axios.patch(`/answers/multiple?projectId=${projectId}`, answers);
    return data;
  } catch (error) {
    console.error("Error updating multiple answers with endpoint:", error);
    throw error;
  }
};

export {
  deleteFormSubmission,
  deleteMultipleFormSubmissions,
  updateAnswer,
  updateMultipleAnswers,
  updateMultipleAnswersWithEndpoint,
};
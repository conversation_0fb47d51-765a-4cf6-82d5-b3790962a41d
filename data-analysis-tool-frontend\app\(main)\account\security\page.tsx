"use client";

import React, { useEffect, useState } from "react";
import { Shield, Mail, Lock, TrendingUp } from "lucide-react";
import { PasswordChange } from "@/components/password/PasswordChange";
import { DataTable } from "./data-table";
import { columns } from "./columns";
import { useAuth } from "@/hooks/useAuth";
import { FieldValues, useForm } from "react-hook-form";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  changeEmail,
  fetchSessionInformations,
  sendVerificationEmail,
} from "@/lib/api/users";
import { useDispatch } from "react-redux";
import { showNotification } from "@/redux/slices/notificationSlice";
import Spinner from "@/components/general/Spinner";
import { Session } from "@/types";
import { ConfirmationModal } from "@/components/modals/ConfirmationModal";
import { AxiosError } from "axios";

const page = () => {
  const [isChangingEmail, setIsChangingEmail] = useState(false);

  const { user, logout } = useAuth();

  const {
    register,
    formState: { errors },
    handleSubmit,
    getValues,
    reset,
    setError,
  } = useForm();

  const {
    data: sessionsData,
    isLoading: sessionsLoading,
    isError: sessionsError,
  } = useQuery<Session[]>({
    queryKey: ["sessions", user?.id],
    queryFn: fetchSessionInformations,
    enabled: !!user?.id,
  });

  const dispatch = useDispatch();
  const queryClient = useQueryClient();

  const [
    showEmailChangeConfirmationModal,
    setShowEmailChangeConfirmationModal,
  ] = useState<boolean>(false);

  const profileMutation = useMutation({
    mutationFn: changeEmail,
    onSuccess: async () => {
      try {
        await queryClient.invalidateQueries({
          queryKey: ["profile", user?.id],
        });
        await sendVerificationEmail(getValues("email"));
        setIsChangingEmail(false);
        dispatch(
          showNotification({
            message:
              "Email changed successfully. A verification email has been sent to your new address.",
            type: "success",
          })
        );
        logout();
      } catch (err) {
        dispatch(
          showNotification({
            message:
              "Email changed, but we couldn't send the verification email. Please try again manually.",
            type: "warning",
          })
        );
      }
    },
    onError: (error) => {
      if (error instanceof AxiosError) {
        setError(error.response?.data.errorField, {
          message: error.response?.data.message,
        });
      } else {
        dispatch(
          showNotification({
            message: "Failed to change email. Please try again",
            type: "error",
          })
        );
      }
    },
  });

  useEffect(() => {
    if (!isChangingEmail) {
      reset();
    }
  }, [isChangingEmail, reset]);

  const onConfirm = () => {
    handleSubmit(onSubmit)();
    setShowEmailChangeConfirmationModal(false);
  };

  // this prevents the page to show errors before completely mounting
  const [mounted, setMounted] = useState<boolean>(false);
  useEffect(() => {
    setMounted(true);
  }, []);

  const onSubmit = async (data: FieldValues) => {
    profileMutation.mutate({ email: data.email });
  };

  if (!mounted) return null;

  if (sessionsLoading) {
    return <Spinner />;
  }

  if (sessionsError || !sessionsData) {
    return <p className="text-sm text-red-500">Error loading data</p>;
  }

  return (
    <>
      <ConfirmationModal
        showModal={showEmailChangeConfirmationModal}
        onClose={() => setShowEmailChangeConfirmationModal(false)}
        onConfirm={onConfirm}
        title="Confirm email change?"
        description="Changing email will log you out and you will need to verify your new email before logging in. The verification email will be sent to your new email address."
        confirmButtonText="Change"
        confirmButtonClass="btn-primary"
      />
      <div className="flex flex-col gap-10">
        <div className="flex flex-col gap-4">
          <div className="flex items-center gap-2">
            <Shield className="h-8 w-8" />
            <h2 className="heading-text">Security Settings</h2>
          </div>
          <p className="sub-text">
            Manage your account security settings and preferences
          </p>
        </div>

        <div className="flex-col gap-10 flex">
          {/* PASSWORD SECTION */}
          <div className="flex flex-col gap-5 shadow-sm border-muted p-4">
            <div className="flex flex-col gap-2">
              <div className="flex items-center gap-2">
                <Lock className="h-5 w-5" />
                <h2 className="sub-heading-text">Password</h2>
              </div>
              <p className="sub-text">
                Update your password to keep your account secure
              </p>
            </div>

            <div>
              <PasswordChange />
            </div>
          </div>

          {/* EMAIL SECTION */}
          <div className="flex flex-col gap-5 shadow-sm border-muted p-4">
            <div className="flex flex-col gap-2 ">
              <div className="flex items-center gap-2">
                <Mail className="h-5 w-5" />
                <h2 className="sub-heading-text">Email Address</h2>
              </div>
              <p className="sub-text">
                Your email address is used for notifications and account
                recovery
              </p>
            </div>

            <div>
              {isChangingEmail ? (
                <form
                  className="space-y-4"
                  noValidate
                  onSubmit={(e) => e.preventDefault()}
                >
                  <input
                    {...register("email", {
                      required: "Please enter your new email.",
                    })}
                    type="email"
                    placeholder="eg: <EMAIL>"
                    className="input-field"
                  />
                  {errors.email && (
                    <p className="text-sm text-red-500">{`${errors.email?.message}`}</p>
                  )}
                  <div className="flex gap-2">
                    <button
                      type="button"
                      className="btn-primary"
                      onClick={() => setShowEmailChangeConfirmationModal(true)}
                    >
                      save
                    </button>
                    <button
                      type="button"
                      className="btn-outline"
                      onClick={() => setIsChangingEmail(false)}
                    >
                      Cancel
                    </button>
                  </div>
                </form>
              ) : (
                <div className="flex justify-between items-center ">
                  <span>{user?.email}</span>
                  <button
                    type="button"
                    className="btn-primary"
                    onClick={() => setIsChangingEmail(true)}
                  >
                    Change
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="flex flex-col gap-4">
          <h2 className="heading-text">Recent Account Activity</h2>
          <DataTable columns={columns} data={sessionsData} />
        </div>
      </div>
    </>
  );
};

export default page;

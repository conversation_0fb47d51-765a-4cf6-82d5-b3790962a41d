-- CreateTable
CREATE TABLE "LibraryQuestionCondition" (
    "id" SERIAL NOT NULL,
    "operator" "Operator" NOT NULL,
    "value" TEXT NOT NULL,
    "libraryQuestionId" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "LibraryQuestionCondition_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "LibraryQuestionCondition" ADD CONSTRAINT "LibraryQuestionCondition_libraryQuestionId_fkey" FOREIGN KEY ("libraryQuestionId") REFERENCES "LibraryQuestion"("id") ON DELETE CASCADE ON UPDATE CASCADE;

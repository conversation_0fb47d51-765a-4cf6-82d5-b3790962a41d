{"name": "data-analysis", "version": "0.1.0", " vate": true, "scripts": {"dev": "next dev --turbopack -H 0.0.0.0", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.2", "@headlessui/react": "^2.2.2", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-checkbox": "^1.2.3", "@radix-ui/react-dropdown-menu": "^2.1.7", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-radio-group": "^1.3.4", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.2.2", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-tooltip": "^1.2.6", "@reduxjs/toolkit": "^2.7.0", "@tanstack/react-query": "^5.74.4", "@tanstack/react-query-devtools": "^5.74.4", "@tanstack/react-table": "^8.21.2", "axios": "^1.8.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "framer-motion": "^12.7.3", "hashids": "^2.3.0", "js-cookie": "^3.0.5", "jwt-decode": "^4.0.0", "lodash": "^4.17.21", "lucide-react": "^0.487.0", "next": "15.3.0", "react": "^19.0.0", "react-day-picker": "^9.6.7", "react-dom": "^19.0.0", "react-hook-form": "^7.55.0", "react-icons": "^5.5.0", "react-redux": "^9.2.0", "react-window": "^1.8.11", "recharts": "^2.15.3", "tailwind-merge": "^3.2.0", "tw-animate-css": "^1.2.5", "uuid": "^9.0.1", "zod": "^3.24.2"}, "devDependencies": {"@tailwindcss/postcss": "^4", "@tanstack/eslint-plugin-query": "^5.73.3", "@types/js-cookie": "^3.0.6", "@types/lodash": "^4.17.17", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-window": "^1.8.8", "@types/uuid": "^9.0.8", "tailwindcss": "^4", "typescript": "^5"}}
/*
  Warnings:

  - You are about to drop the `answers` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `submissions` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropForeignKey
ALTER TABLE "answers" DROP CONSTRAINT "answers_question_id_fkey";

-- DropForeignKey
ALTER TABLE "answers" DROP CONSTRAINT "answers_submission_id_fkey";

-- DropForeignKey
ALTER TABLE "submissions" DROP CONSTRAINT "submissions_library_template_id_fkey";

-- DropForeignKey
ALTER TABLE "submissions" DROP CONSTRAINT "submissions_project_id_fkey";

-- DropForeignKey
ALTER TABLE "submissions" DROP CONSTRAINT "submissions_user_id_fkey";

-- AlterTable
ALTER TABLE "LibraryQuestion" ADD COLUMN     "libraryTemplateQuestionGroupId" INTEGER;

-- AlterTable
CREATE SEQUENCE questiongroup_order_seq;
ALTER TABLE "QuestionGroup" ALTER COLUMN "order" SET DEFAULT nextval('questiongroup_order_seq');
ALTER SEQUENCE questiongroup_order_seq OWNED BY "QuestionGroup"."order";

-- DropTable
DROP TABLE "answers";

-- DropTable
DROP TABLE "submissions";

-- CreateTable
CREATE TABLE "LibraryTemplateQuestionGroup" (
    "id" SERIAL NOT NULL,
    "title" TEXT NOT NULL,
    "order" SERIAL NOT NULL,
    "libraryTemplateId" INTEGER NOT NULL,
    "parentGroupId" INTEGER,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "LibraryTemplateQuestionGroup_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "FormSubmission" (
    "id" SERIAL NOT NULL,
    "projectId" INTEGER,
    "userId" INTEGER,
    "submittedAt" TIMESTAMP(3),
    "startedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "completedAt" TIMESTAMP(3),
    "durationSeconds" INTEGER,
    "status" TEXT NOT NULL DEFAULT 'draft',
    "deviceInfo" TEXT,
    "location" TEXT,
    "metadata" JSONB DEFAULT '{}',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "FormSubmission_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Answer" (
    "id" SERIAL NOT NULL,
    "formSubmissionId" INTEGER,
    "questionId" INTEGER,
    "value" TEXT NOT NULL,
    "answerType" TEXT,
    "imageUrl" TEXT,
    "questionOptionId" INTEGER,
    "isOtherOption" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Answer_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "LibraryQuestion" ADD CONSTRAINT "LibraryQuestion_libraryTemplateQuestionGroupId_fkey" FOREIGN KEY ("libraryTemplateQuestionGroupId") REFERENCES "LibraryTemplateQuestionGroup"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "LibraryTemplateQuestionGroup" ADD CONSTRAINT "LibraryTemplateQuestionGroup_libraryTemplateId_fkey" FOREIGN KEY ("libraryTemplateId") REFERENCES "LibraryTemplate"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "LibraryTemplateQuestionGroup" ADD CONSTRAINT "LibraryTemplateQuestionGroup_parentGroupId_fkey" FOREIGN KEY ("parentGroupId") REFERENCES "LibraryTemplateQuestionGroup"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "FormSubmission" ADD CONSTRAINT "FormSubmission_projectId_fkey" FOREIGN KEY ("projectId") REFERENCES "Project"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "FormSubmission" ADD CONSTRAINT "FormSubmission_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Answer" ADD CONSTRAINT "Answer_formSubmissionId_fkey" FOREIGN KEY ("formSubmissionId") REFERENCES "FormSubmission"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Answer" ADD CONSTRAINT "Answer_questionId_fkey" FOREIGN KEY ("questionId") REFERENCES "Question"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Answer" ADD CONSTRAINT "Answer_questionOptionId_fkey" FOREIGN KEY ("questionOptionId") REFERENCES "QuestionOption"("id") ON DELETE SET NULL ON UPDATE CASCADE;

import { Checkbox } from "@/components/ui";
import { Template } from "@/types";
import { ColumnDef } from "@tanstack/react-table";

type Props = {
  selectedRowId: number | null;
  setSelectedRowId: (id: number | null) => void;
};

const getTemplateListColumnsForProjectCreation = ({
  selectedRowId,
  setSelectedRowId,
}: Props): ColumnDef<Template>[] => [
  {
    id: "select",
    header: "",
    cell: ({ row }) => {
      const rowId = row.original.id;
      const isSelected = rowId === selectedRowId;

      return (
        <Checkbox
          className="w-6 h-6 bg-neutral-100 rounded border border-neutral-400 data-[state=checked]:bg-neutral-100 data-[state=checked]:text-primary-500 data-[state=checked]:border-primary-500 cursor-pointer"
          checked={isSelected}
          onCheckedChange={(value) => setSelectedRowId(value ? rowId : null)}
          aria-label="Select row"
        />
      );
    },
  },
  {
    accessorKey: "name",
    header: "Name",
  },
  {
    id: "owner",
    accessorFn: (row) => row.user?.name ?? "unknown",
    header: "Owner",
    cell: ({ getValue }) => getValue(),
  },
  {
    id: "questions",
    accessorFn: (row) => row.libraryQuestions?.length.toString() ?? "0",
    header: "Questions",
    cell: ({ getValue }) => getValue(),
  },
  {
    accessorKey: "updatedAt",
    header: "Last Modified",
  },
];

export { getTemplateListColumnsForProjectCreation };

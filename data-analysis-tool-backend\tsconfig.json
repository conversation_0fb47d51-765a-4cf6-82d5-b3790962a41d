{
  "compilerOptions": {
    "target": "ES6",
    "module": "commonjs",
    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "rootDir": "src",
    "outDir": "dist",
    "allowImportingTsExtensions": false,
    // "noEmit": true,
    "typeRoots": ["./node_modules/@types", "./types"]
  },
  "include": ["src/**/*.ts"],
  "exclude": ["node_modules"]
}

"use client";

import React, { useState } from "react";
import { Question } from "@/types/formBuilder";
import { QuestionItem } from "./QuestionItem";
import { ChevronDown, ChevronRight, Edit, Trash, Plus } from "lucide-react";
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from "@dnd-kit/core";
import {
  SortableContext,
  verticalListSortingStrategy,
  arrayMove,
} from "@dnd-kit/sortable";

interface QuestionGroupItemProps {
  id: number;
  title: string;
  questions: Question[];
  onEditGroup: (groupId: number) => void;
  onDeleteGroup: (groupId: number) => void;
  onAddQuestionToGroup: (groupId: number) => void;
  onEditQuestion: (question: Question) => void;
  onDeleteQuestion: (question: Question) => void;
  onDuplicateQuestion: (question: Question) => void;
  onReorderQuestions?: (questionPositions: { id: number; position: number }[]) => void;
  isEditing?: boolean;
  onStartEditing?: (groupId: number, currentName: string) => void;
  onSaveGroupName?: (groupId: number) => void;
  onCancelEditing?: () => void;
  editingName?: string;
  onEditingNameChange?: (name: string) => void;
  selectionMode?: boolean;
}

const QuestionGroupItem = ({
  id,
  title,
  questions,
  onEditGroup, // Kept for future use
  onDeleteGroup,
  onAddQuestionToGroup,
  onEditQuestion,
  onDeleteQuestion,
  onDuplicateQuestion,
  onReorderQuestions,
  isEditing = false,
  onStartEditing,
  onSaveGroupName,
  onCancelEditing,
  editingName = "",
  onEditingNameChange,
  selectionMode = false,
}: QuestionGroupItemProps) => {
  const [isExpanded, setIsExpanded] = useState(true);

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(KeyboardSensor)
  );

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (!over || active.id === over.id || !onReorderQuestions) {
      return;
    }

    // Sort questions by position to maintain order
    const sortedQuestions = [...questions].sort((a, b) => a.position - b.position);

    const oldIndex = sortedQuestions.findIndex((q) => q.id === active.id);
    const newIndex = sortedQuestions.findIndex((q) => q.id === over.id);

    if (oldIndex === -1 || newIndex === -1) {
      return;
    }

    // Reorder the questions array
    const reorderedQuestions = arrayMove(sortedQuestions, oldIndex, newIndex);

    // Calculate new positions for all questions in this group
    const questionPositions = reorderedQuestions.map((question, index) => ({
      id: Number(question.id), // Ensure it's a number
      position: index + 1, // Start positions from 1
    }));

    // Call the parent handler to update positions
    onReorderQuestions(questionPositions);
  };

  return (
    <div className="border border-neutral-400 rounded-md bg-card shadow-sm mb-4">
      {/* Group Header */}
      <div className="flex items-center p-4 bg-neutral-100 border-b border-neutral-300 rounded-t-md">
        <button
          onClick={() => setIsExpanded(!isExpanded)}
          className="mr-2 text-neutral-700 hover:text-primary-500 transition-colors"
          aria-label={isExpanded ? "Collapse group" : "Expand group"}
        >
          {isExpanded ? (
            <ChevronDown className="h-5 w-5" />
          ) : (
            <ChevronRight className="h-5 w-5" />
          )}
        </button>

        {isEditing ? (
          <div className="flex-1 mr-4">
            <input
              type="text"
              value={editingName}
              onChange={(e) => onEditingNameChange && onEditingNameChange(e.target.value)}
              className="w-full p-2 border border-gray-300 rounded"
              autoFocus
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  onSaveGroupName && onSaveGroupName(id);
                } else if (e.key === 'Escape') {
                  onCancelEditing && onCancelEditing();
                }
              }}
              placeholder="Enter group name"
            />
          </div>
        ) : (
          <h3
            className="flex-1 font-medium text-lg cursor-pointer hover:text-primary-500"
            onClick={() => onStartEditing && onStartEditing(id, title)}
            title="Click to edit group name"
          >
            {title}
          </h3>
        )}

        <div className="flex items-center space-x-3">
          {isEditing ? (
            <div className="flex space-x-2">
              <button
                onClick={() => onCancelEditing && onCancelEditing()}
                title="Cancel Editing"
                className="cursor-pointer px-3 py-1 rounded btn-outline"
              >
                Cancel
              </button>
              <button
                onClick={() => onSaveGroupName && onSaveGroupName(id)}
                title="Save Group Name"
                className="cursor-pointer px-3 py-1 rounded btn-primary"
              >
                Save
              </button>
            </div>
          ) : (
            <>
              <button
                onClick={() => onAddQuestionToGroup(id)}
                title="Add Question to Group"
                className="cursor-pointer p-2 rounded-full hover:bg-primary-100 text-neutral-700 hover:text-primary-500 transition-colors"
              >
                <Plus size={16} />
              </button>
              <button
                onClick={() => onStartEditing && onStartEditing(id, title)}
                title="Edit Group Name"
                className="cursor-pointer p-2 rounded-full hover:bg-primary-100 text-neutral-700 hover:text-primary-500 transition-colors"
              >
                <Edit size={16} />
              </button>
              <button
                onClick={() => onDeleteGroup(id)}
                title="Delete Group"
                className="cursor-pointer p-2 rounded-full hover:bg-destructive/10 text-neutral-700 hover:text-destructive transition-colors"
              >
                <Trash size={16} />
              </button>
            </>
          )}
        </div>
      </div>

      {/* Group Content */}
      {isExpanded && (
        <div className="p-4 space-y-4">
          {questions.length > 0 ? (
            <DndContext
              sensors={sensors}
              collisionDetection={closestCenter}
              onDragEnd={handleDragEnd}
            >
              <SortableContext
                items={questions.map((question) => question.id)}
                strategy={verticalListSortingStrategy}
              >
                {questions
                  .sort((a, b) => a.position - b.position)
                  .map((question) => (
                    <div key={question.id} className="mb-4">
                      <QuestionItem
                        question={question}
                        onEdit={() => onEditQuestion(question)}
                        onDelete={() => onDeleteQuestion(question)}
                        onDuplicate={() => onDuplicateQuestion(question)}
                        selectionMode={selectionMode}
                        isSelected={false}
                        onToggleSelect={() => {}}
                      />
                    </div>
                  ))}
              </SortableContext>
            </DndContext>
          ) : (
            <div className="text-center py-4 text-neutral-500">
              No questions in this group. Click the + button to add questions.
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export { QuestionGroupItem };

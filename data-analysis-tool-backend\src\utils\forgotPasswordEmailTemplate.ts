const forgetPasswordEmailTemplate = (verificationLink: string) => {
  return `<!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Reset Your Password</title>
      </head>
      <body style="margin: 0; padding: 20px; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif; background-color: #f5f7f9; color: #333;">
        <table role="presentation" style="width: 100%; max-width: 600px; margin: 0 auto; background: white; border-radius: 6px; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);">
          <tr>
            <td style="padding: 40px 40px 32px 40px; text-align: center;">
              <!-- Logo -->
              
              <!-- Title -->
              <h1 style="margin: 0 0 16px 0; font-size: 24px; font-weight: 500; color: #333;">
                Reset Your Password
              </h1>
              
              <!-- Message -->
              <p style="margin: 0 0 24px 0; font-size: 16px; line-height: 24px; color: #666;">
                In order to change your password, you need to confirm your email address.
              </p>
              
              <!-- Button -->
              <a href="${verificationLink}" style="display: inline-block; width: 100%; max-width: 320px; padding: 14px 0; background-color: #1652f0; color: white; text-decoration: none; font-size: 16px; font-weight: 500; border-radius: 4px; text-align: center;">
                Reset Your Password
              </a>
              
              <!-- Disclaimer -->
              <p style="margin: 24px 0 0 0; font-size: 14px; line-height: 20px; color: #999; font-style: italic;">
                If you did not click reset password link for this account you can ignore this email.
              </p>
            </td>
          </tr>
        </table>
        
        <!-- App Download Section -->
        <table role="presentation" style="width: 100%; max-width: 600px; margin: 24px auto 0;">
          <tr>
            <td style="text-align: center;">
              <p style="margin: 0 0 16px 0; font-size: 14px; color: #666;">
                Get the latest App for your phone
              </p>
              <div>
                <!-- App Store -->
                <a href="#" style="display: inline-block; margin: 0 8px;">
                  <img src="https://developer.apple.com/app-store/marketing/guidelines/images/badge-example-preferred.png" 
                       alt="Download on the App Store" 
                       style="height: 40px;">
                </a>
                <!-- Google Play -->
                <a href="#" style="display: inline-block; margin: 0 8px;">
                  <img src="https://play.google.com/intl/en_us/badges/static/images/badges/en_badge_web_generic.png" 
                       alt="Get it on Google Play" 
                       style="height: 40px;">
                </a>
              </div>
              
              <!-- Copyright -->
              <p style="margin: 24px 0 0 0; font-size: 12px; color: #999;">
                © 2024 Your Company. All rights reserved.
              </p>
            </td>
          </tr>
        </table>
      </body>
      </html>`;
};
export { forgetPasswordEmailTemplate };

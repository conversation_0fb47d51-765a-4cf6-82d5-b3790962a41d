import React from "react";
import { useQuery } from "@tanstack/react-query";
import {
  fetchQuestions,
  fetchTemplateQuestions,
  fetchQuestionBlockQuestions,
} from "@/lib/api/form-builder";
import { ContextType } from "@/types";
import { Question } from "@/types/formBuilder";

interface QuestionSelectorProps {
  contextType: ContextType;
  contextId: number;
  value?: number | null;
  onChange: (questionId: number | null) => void;
  currentQuestionId?: number; // To exclude current question from options
  placeholder?: string;
}

const QuestionSelector: React.FC<QuestionSelectorProps> = ({
  contextType,
  contextId,
  value,
  onChange,
  currentQuestionId,
  placeholder = "Select next question (optional)",
}) => {
  // Fetch questions based on context type
  const {
    data: questions = [],
    isLoading,
    error,
  } = useQuery<Question[]>({
    queryKey:
      contextType === "project"
        ? ["questions", contextId]
        : contextType === "template"
        ? ["templateQuestions", contextId]
        : ["questionBlockQuestions", contextId],
    queryFn: () => {
      if (contextType === "project") {
        return fetchQuestions({ projectId: contextId });
      } else if (contextType === "template") {
        return fetchTemplateQuestions({ templateId: contextId });
      } else if (contextType === "questionBlock") {
        return fetchQuestionBlockQuestions();
      }
      return [];
    },
    enabled: !!contextId,
  });


  // Filter out current question to prevent self-reference
  const availableQuestions = questions.filter(
    (q) => q.id !== currentQuestionId
  );

  const selectedQuestion = availableQuestions.find((q) => q.id === value);

  return (
    <div className="flex flex-col gap-1">
      <select
        value={value || ""}
        onChange={(e) => {
          const selectedValue = e.target.value;
          onChange(selectedValue ? parseInt(selectedValue) : null);
        }}
        className="input-field text-sm"
        disabled={isLoading}
      >
        <option value="">
          {isLoading ? "Loading questions..." : placeholder}
        </option>
        {availableQuestions.map((question) => (
          <option key={question.id} value={question.id}>
            {question.label || `Question ${question.id}`}
          </option>
        ))}
      </select>
      {selectedQuestion && (
        <div className="text-xs text-gray-500 mt-1">
          Type: {selectedQuestion.inputType}
        </div>
      )}
    </div>
  );
};

export { QuestionSelector };

export type QuestionType = 
  | 'text' 
  | 'number'
  | 'decimal' 
  | 'select' 
  | 'multiselect' 
  | 'date' 
  | 'time'
  | 'location'
  | 'image'
  | 'rating';

export interface QuestionOption {
  id: string;
  value: string;
  code: string;
}

export interface Question {
  id: string;
  text: string;
  type: QuestionType;
  required: boolean;
  hint?: string;
  options?: QuestionOption[];
}

export interface Form {
  id: string;
  title: string;
  description?: string;
  questions: Question[];
}

export interface SavedFormSubmission {
  id: string;
  formId: string;
  formTitle: string;
  submissionDate: string;
  answers: Record<string, any>;
  form: Form;
} 
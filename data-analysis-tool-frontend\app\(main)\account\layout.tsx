"use client";

import Spinner from "@/components/general/Spinner";
import { UserNavbar } from "@/components/user/UserNavbar";
import { useAuth } from "@/hooks/useAuth";
import { fetchUserProfile } from "@/lib/api/users";
import { Profile } from "@/types";
import { useQuery } from "@tanstack/react-query";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";
import React from "react";

const layout = ({ children }: { children: React.ReactNode }) => {
  const { user } = useAuth();

  const {
    data: profileData,
    isLoading: profileLoading,
    isError: profileError,
  } = useQuery<Profile>({
    queryKey: ["profile", user?.id],
    queryFn: fetchUserProfile,
    enabled: !!user?.id,
  });

  if (profileLoading) {
    return <Spinner />;
  }
  if (profileError) {
    return (
      <p className="text-red-500">
        Error loading profile data. Please try again
      </p>
    );
  }

  return (
    <div className="section flex flex-col gap-4">
      <div className="flex items-center justify-between">
        <h1 className="heading-text capitalize">{profileData?.name}</h1>
        <Link href="/dashboard" className="flex items-center gap-2">
          <ArrowLeft size={16} />
          Back to dashboard
        </Link>
      </div>
      <UserNavbar />
      <div className="px-8">{children}</div>
    </div>
  );
};

export default layout;

import { Request, Response, NextFunction } from "express";
import jwt from "jsonwebtoken";

interface jwtPayload {
  id: number;
  sessionId: number
}

interface AuthRequest extends Request {
  user?: {
    id: number;
    sessionId: number;
  };
}

export const authenticate = (
  req: AuthRequest,
  res: Response,
  next: NextFunction
) => {
  const token = req.cookies?.token;

  if (!token) {
    res
      .status(401)
      .json({ message: "Unauthorized: No token found in cookies" });
    return;
  }

  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET!) as jwtPayload;
    const userId =
      typeof decoded.id === "string" ? parseInt(decoded.id) : decoded.id;

    if (!userId || isNaN(userId)) {
      res.status(401).json({
        success: false,
        message: "Unauthorized: invalid token payload / user not found",
      });
      return;
    }

    req.user = { id: userId, sessionId: decoded.sessionId };

    next();
  } catch (error) {
    res.status(401).json({ message: "invalid token" });
    return;
  }
};

"use client";

import { usePathname } from "next/navigation";
import Link from "next/link";
import React from "react";

type NavbarItemType = {
  label: string;
  icon: React.ReactNode;
  route: string;
  disabled?: boolean;
};

type NavbarProps = {
  items: NavbarItemType[];
};

const Navbar = ({ items }: NavbarProps) => {
  const pathname = usePathname();

  const isActive = (route: string) => pathname.startsWith(route);
  return (
    <div className="border-y border-neutral-400 rounded-md bg-primary-500 my-4 shadow-md">
      <div className="flex items-center">
        {items.map((item) =>
          item.disabled ? (
            <div
              key={item.route}
              className="flex items-center gap-2 font-medium transition-all duration-300 p-4 border-b-2 text-gray-400 cursor-not-allowed"
            >
              {item.icon}
              {item.label}
            </div>
          ) : (
            <Link
              href={item.route}
              key={item.route}
              className={`flex items-center gap-2 font-medium transition-all duration-300 p-4 border-b-2 text-neutral-100 ${
                isActive(item.route)
                  ? "border-neutral-100"
                  : "border-transparent hover:border-neutral-400"
              }`}
            >
              {item.icon}
              {item.label}
            </Link>
          )
        )}
      </div>
    </div>
  );
};

export { Navbar };

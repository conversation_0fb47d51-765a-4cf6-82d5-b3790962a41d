"use client";

import React, { useState } from "react";
import {
  Dnd<PERSON>ontext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from "@dnd-kit/core";
import {
  SortableContext,
  verticalListSortingStrategy,
  arrayMove,
} from "@dnd-kit/sortable";
import { Question, QuestionGroup } from "@/types/formBuilder";
import { QuestionItem } from "@/components/form-builder/QuestionItem";
import { QuestionGroupItem } from "@/components/form-builder/QuestionGroupItem";
import {
  Eye,
  FileDown,
  FileUp,
  PlusCircle,
  Settings,
  BookOpen,
  FolderPlus,
} from "lucide-react";
import { AddQuestionModal } from "../modals/AddQuestionModal";
import { EditQuestionModal } from "../modals/EditQuestionModal";
import { EditTableQuestionModal } from "../modals/EditTableQuestionModal";
import { ConfirmationModal } from "../modals/ConfirmationModal";
import { QuestionGroupModal } from "../modals/QuestionGroupModal";
import { DeleteQuestionGroupModal } from "../modals/DeleteQuestionGroupModal";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  deleteQuestion,
  duplicateQuestion,
  addQuestion,
  updateQuestionPositions,
} from "@/lib/api/form-builder";
import {
  fetchQuestionGroups,
  createQuestionGroup,
  updateQuestionGroup,
  deleteQuestionGroup,
  deleteQuestionAndGroup,
  // removeQuestionFromGroup - will be used in future implementation
} from "@/lib/api/question-groups";
import { useDispatch } from "react-redux";
import { showNotification } from "@/redux/slices/notificationSlice";
import { ContextType } from "@/types";
import { LoadingOverlay } from "../general/LoadingOverlay";
import LibraryQuestionsSidebar from "./LibraryQuestionsSidebar";
import { ProjectPermissionFlags } from "@/types";
const FormBuilder = ({
  setIsPreviewMode,
  questions,
  contextType,
  contextId,
  permissions,
}: {
  setIsPreviewMode: React.Dispatch<React.SetStateAction<boolean>>;
  questions: Question[];
  contextType: ContextType;
  contextId: number;
  permissions: ProjectPermissionFlags;
}) => {
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(KeyboardSensor)
  );

  const editFormPermission = permissions.manageProject || permissions.editForm;

  // Question modals state
  const [showAddQuestionModal, setShowAddQuestionModal] =
    useState<boolean>(false);
  const [showEditQuestionModal, setShowEditQuestionModal] =
    useState<boolean>(false);
  const [showEditTableQuestionModal, setShowEditTableQuestionModal] =
    useState<boolean>(false);
  const [showLibrarySidebar, setShowLibrarySidebar] = useState<boolean>(false);
  const [isAddingQuestions, setIsAddingQuestions] = useState<boolean>(false);
  const [currentQuestion, setCurrentQuestion] = useState<Question | null>(null);
  const [showDeleteConfirmationModal, setShowDeleteConfirmationModal] =
    useState<boolean>(false);

  // Question group modals state
  const [showAddGroupModal, setShowAddGroupModal] = useState<boolean>(false);
  const [showEditGroupModal, setShowEditGroupModal] = useState<boolean>(false);
  const [showDeleteGroupModal, setShowDeleteGroupModal] =
    useState<boolean>(false);
  const [currentGroupId, setCurrentGroupId] = useState<number | null>(null);

  // Question selection state
  const [selectedQuestionIds, setSelectedQuestionIds] = useState<number[]>([]);
  const [isEditingGroupName, setIsEditingGroupName] = useState<number | null>(
    null
  );
  const [editingGroupName, setEditingGroupName] = useState<string>("");
  const [selectionMode, setSelectionMode] = useState<boolean>(false);

  // State for tracking when questions are being added
  const [isProcessingGroup, setIsProcessingGroup] = useState(false);

  const dispatch = useDispatch();
  const queryClient = useQueryClient();

  const questionsQueryKey =
    contextType === "project"
      ? ["questions", contextId]
      : contextType === "template"
      ? ["templateQuestions", contextId]
      : ["questionBlockQuestions", contextId];

  const groupsQueryKey = ["questionGroups", contextId];

  // Fetch question groups
  const { data: questionGroups = [], isLoading: isLoadingGroups } = useQuery({
    queryKey: groupsQueryKey,
    queryFn: () => fetchQuestionGroups({ projectId: contextId }),
    enabled: contextType === "project", // Only fetch for projects, not templates or question blocks
  });

  // Group questions by their group ID and sort by position within each group
  const groupedQuestions = questionGroups.reduce(
    (acc: Record<number, Question[]>, group: QuestionGroup) => {
      acc[group.id] = questions
        .filter((q) => q.questionGroupId === group.id)
        .sort((a, b) => a.position - b.position);
      return acc;
    },
    {} as Record<number, Question[]>
  );

  // Get ungrouped questions
  const ungroupedQuestions = questions.filter((q) => !q.questionGroupId);

  // Create a unified list of form items (groups and individual questions) for dynamic ordering
  const createUnifiedFormItems = () => {
    const items: Array<{
      type: 'group' | 'question';
      data: QuestionGroup | Question;
      order: number;
      originalPosition?: number; // Track original position for better sorting
    }> = [];

    // Only add question groups for projects (not templates or question blocks)
    if (contextType === "project") {
      questionGroups.forEach((group: QuestionGroup) => {
        // For groups, find the minimum position of questions in the group
        // This ensures the group appears where the first question was originally positioned
        const groupQuestions = questions
          .filter(q => q.questionGroupId === group.id)
          .sort((a, b) => a.position - b.position);
        const minQuestionPosition = groupQuestions.length > 0
          ? Math.min(...groupQuestions.map(q => q.position))
          : group.order;

        items.push({
          type: 'group',
          data: group,
          order: minQuestionPosition, // Use the position of the first question in the group
          originalPosition: minQuestionPosition
        });
      });
    }

    // Add ungrouped questions (or all questions if not in project context)
    const questionsToAdd = contextType === "project" ? ungroupedQuestions : questions;
    questionsToAdd.forEach((question: Question) => {
      items.push({
        type: 'question',
        data: question,
        order: question.position,
        originalPosition: question.position
      });
    });

    // Sort by order/position, with a secondary sort by type to ensure consistent ordering
    // when groups and questions have the same position
    return items.sort((a, b) => {
      if (a.order === b.order) {
        // If positions are equal, prioritize based on original question positions
        // This helps maintain the original flow when grouping questions
        return (a.originalPosition || a.order) - (b.originalPosition || b.order);
      }
      return a.order - b.order;
    });
  };

  const unifiedFormItems = createUnifiedFormItems();

  // Force refresh of question groups if they're empty but we have questions with questionGroupId
  React.useEffect(() => {
    const hasGroupedQuestions = questions.some(
      (q) => q.questionGroupId !== null && q.questionGroupId !== undefined
    );
    const hasNoGroups = questionGroups.length === 0;

    if (hasGroupedQuestions && hasNoGroups && contextType === "project") {
      queryClient.invalidateQueries({ queryKey: groupsQueryKey });
    }
  }, [questions, questionGroups, contextType, queryClient, groupsQueryKey]);

  // Question mutations
  const deleteQuestionMutation = useMutation({
    mutationFn: deleteQuestion,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: questionsQueryKey });
      dispatch(
        showNotification({
          message: "Question deleted successfully",
          type: "success",
        })
      );
    },
    onError: () => {
      dispatch(
        showNotification({
          message: "Failed to delete question. Please try again",
          type: "error",
        })
      );
    },
    onSettled: () => {
      setShowDeleteConfirmationModal(false);
    },
  });

  const duplicateQuestionMutation = useMutation({
    mutationFn: duplicateQuestion,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: questionsQueryKey });
      dispatch(
        showNotification({
          message: "Question duplicated successfully",
          type: "success",
        })
      );
    },
    onError: () => {
      dispatch(
        showNotification({
          message: "Failed to duplicate question. Please try again",
          type: "error",
        })
      );
    },
    onSettled: () => {
      setShowDeleteConfirmationModal(false);
    },
  });

  // Question group mutations
  const deleteGroupMutation = useMutation({
    mutationFn: deleteQuestionGroup,
    onSuccess: (data, variables) => {

      // Find the group that was deleted
      const deletedGroup = questionGroups.find(
        (g: QuestionGroup) => g.id === variables.id
      );

      if (deletedGroup) {
        // Find questions that belonged to this group
        const questionsInGroup = questions.filter(
          (q) => q.questionGroupId === deletedGroup.id
        );

        if (questionsInGroup.length > 0) {
          // Update the local state to mark these questions as ungrouped
          const updatedQuestions = questions.map((q) =>
            q.questionGroupId === deletedGroup.id
              ? { ...q, questionGroupId: undefined }
              : q
          );

          // Update the questions in the local state
          queryClient.setQueryData(questionsQueryKey, updatedQuestions);

          // Remove the deleted group from the local state
          const updatedGroups = questionGroups.filter(
            (g: QuestionGroup) => g.id !== variables.id
          );
          queryClient.setQueryData(groupsQueryKey, updatedGroups);
        }
      }

      // Then invalidate queries to get the latest data from the server
      queryClient.invalidateQueries({ queryKey: groupsQueryKey });
      queryClient.invalidateQueries({ queryKey: questionsQueryKey });

      dispatch(
        showNotification({
          message: "Question group deleted successfully",
          type: "success",
        })
      );

      setShowDeleteGroupModal(false);
      setIsProcessingGroup(false);
    },
    onError: (error) => {
      console.error("Error deleting question group:", error);
      dispatch(
        showNotification({
          message: "Failed to delete question group. Please try again",
          type: "error",
        })
      );
      setIsProcessingGroup(false);
    },
  });

  const deleteGroupWithQuestionsMutation = useMutation({
    mutationFn: deleteQuestionAndGroup,
    onSuccess: (data, variables) => {

      // Find the group that was deleted
      const deletedGroup = questionGroups.find(
        (g: QuestionGroup) => g.id === variables.id
      );

      if (deletedGroup) {
        // Remove the deleted group from the local state
        const updatedGroups = questionGroups.filter(
          (g: QuestionGroup) => g.id !== variables.id
        );
        queryClient.setQueryData(groupsQueryKey, updatedGroups);

        // Remove questions that belonged to this group from the local state
        const updatedQuestions = questions.filter(
          (q) => q.questionGroupId !== deletedGroup.id
        );
        queryClient.setQueryData(questionsQueryKey, updatedQuestions);
      }

      // Then invalidate queries to get the latest data from the server
      queryClient.invalidateQueries({ queryKey: groupsQueryKey });
      queryClient.invalidateQueries({ queryKey: questionsQueryKey });

      dispatch(
        showNotification({
          message: "Question group and its questions deleted successfully",
          type: "success",
        })
      );

      setShowDeleteGroupModal(false);
      setIsProcessingGroup(false);
    },
    onError: (error) => {
      console.error("Error deleting question group and questions:", error);
      dispatch(
        showNotification({
          message:
            "Failed to delete question group and questions. Please try again",
          type: "error",
        })
      );
      setIsProcessingGroup(false);
    },
  });

  // Note: We'll need a mutation for removing questions from groups in the future
  // This functionality will be implemented when needed

  // Question position update mutation
  const updatePositionsMutation = useMutation({
    mutationFn: updateQuestionPositions,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: questionsQueryKey });
      dispatch(
        showNotification({
          message: "Question order updated successfully",
          type: "success",
        })
      );
    },
    onError: (error: any) => {
      console.error("Failed to update question positions:", error);
      console.error("Error response:", error.response?.data);
      dispatch(
        showNotification({
          message: `Failed to update question order: ${error.response?.data?.message || error.message || "Please try again"}`,
          type: "error",
        })
      );
    },
  });

  // Question handlers
  const handleDelete = () => {
    if (currentQuestion && currentQuestion.id) {
      deleteQuestionMutation.mutate({
        contextType,
        id: currentQuestion?.id,
        projectId: contextId,
      });
    }
  };

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (!over || active.id === over.id) {
      return;
    }

    // Only handle drag and drop in project context
    if (contextType !== "project") {
      return;
    }

    // Find the active and over questions
    const activeQuestion = questions.find((q) => q.id === active.id);
    const overQuestion = questions.find((q) => q.id === over.id);

    if (!activeQuestion || !overQuestion) {
      return;
    }

    // Handle reordering within the same group or ungrouped questions
    const isSameGroup = activeQuestion.questionGroupId === overQuestion.questionGroupId;

    if (!isSameGroup) {
      return;
    }

    // Get questions in the same context (same group or ungrouped)
    const contextQuestions = questions
      .filter((q) => q.questionGroupId === activeQuestion.questionGroupId)
      .sort((a, b) => a.position - b.position);

    const oldIndex = contextQuestions.findIndex((q) => q.id === active.id);
    const newIndex = contextQuestions.findIndex((q) => q.id === over.id);

    if (oldIndex === -1 || newIndex === -1) {
      return;
    }

    // Reorder the questions array
    const reorderedQuestions = arrayMove(contextQuestions, oldIndex, newIndex);

    // Calculate new positions for all affected questions
    const questionPositions = reorderedQuestions.map((question, index) => ({
      id: Number(question.id), // Ensure it's a number
      position: index + 1, // Start positions from 1
    }));


    // Update positions in the backend
    updatePositionsMutation.mutate({
      contextType,
      contextId,
      questionPositions,
    });
  };

  // Group handlers
  const handleEditGroup = (groupId: number) => {
    setCurrentGroupId(groupId);
    setShowEditGroupModal(true);
  };

  const handleDeleteGroup = (groupId: number) => {
    setCurrentGroupId(groupId);
    setShowDeleteGroupModal(true);
  };

  const handleConfirmDeleteGroup = () => {
    if (currentGroupId) {
      setIsProcessingGroup(true);
      deleteGroupMutation.mutate({ id: currentGroupId });
    }
  };

  const handleConfirmDeleteGroupWithQuestions = () => {
    if (currentGroupId) {
      setIsProcessingGroup(true);
      deleteGroupWithQuestionsMutation.mutate({ id: currentGroupId });
    }
  };

  const handleAddQuestionToGroup = (groupId: number) => {
    setCurrentGroupId(groupId);
    setShowEditGroupModal(true);
  };

  // Handle toggling question selection
  const toggleQuestionSelection = (questionId: number) => {
    setSelectedQuestionIds((prev) =>
      prev.includes(questionId)
        ? prev.filter((id) => id !== questionId)
        : [...prev, questionId]
    );
  };

  // Create group mutation
  const createGroupMutation = useMutation({
    mutationFn: createQuestionGroup,
    onSuccess: (data, variables) => {

      // Update local state immediately for a smoother UI experience
      // This will show the grouped questions before the server refetch completes
      const newGroupId = data.data?.questionGroup?.id;

      if (newGroupId && variables.selectedQuestionIds) {

        // Update the questionGroupId for selected questions in the local state
        const updatedQuestions = questions.map((q) =>
          variables.selectedQuestionIds?.includes(q.id)
            ? { ...q, questionGroupId: newGroupId }
            : q
        );

        // Update the local state with the new questions
        queryClient.setQueryData(questionsQueryKey, updatedQuestions);

        // Also update the groups in the local state
        const newGroup = {
          id: newGroupId,
          title: variables.title,
          order: variables.order,
          projectId: variables.projectId,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          question: updatedQuestions.filter(
            (q) => q.questionGroupId === newGroupId
          ),
        };

        queryClient.setQueryData(groupsQueryKey, [...questionGroups, newGroup]);
      }

      // Then invalidate queries to get the latest data from the server
      queryClient.invalidateQueries({ queryKey: groupsQueryKey });
      queryClient.invalidateQueries({ queryKey: questionsQueryKey });

      dispatch(
        showNotification({
          message: "Question group created successfully",
          type: "success",
        })
      );

      // Clear selection after creating group
      setSelectedQuestionIds([]);
      setSelectionMode(false);
      setIsProcessingGroup(false);
    },
    onError: (error) => {
      console.error("Error creating question group:", error);
      dispatch(
        showNotification({
          message: "Failed to create question group",
          type: "error",
        })
      );
      setIsProcessingGroup(false);
    },
  });

  // Handle creating a group from selected questions
  const handleCreateGroupFromSelected = () => {
    if (selectedQuestionIds.length === 0) {
      dispatch(
        showNotification({
          message: "Please select at least one question to create a group",
          type: "warning",
        })
      );
      return;
    }

    setIsProcessingGroup(true);

    // Calculate the group order based on the minimum position of selected questions
    // This ensures the group appears in the same position as the first selected question
    const selectedQuestions = questions.filter(q => selectedQuestionIds.includes(q.id));
    const minPosition = selectedQuestions.length > 0
      ? Math.min(...selectedQuestions.map(q => q.position))
      : questionGroups.length + 1;

    // Create a new group with the selected questions
    createGroupMutation.mutate({
      title: "New Group",
      order: minPosition,
      projectId: contextId,
      selectedQuestionIds,
    });
  };

  // Handle inline editing of group name
  const startEditingGroupName = (groupId: number, currentName: string) => {
    setIsEditingGroupName(groupId);
    setEditingGroupName(currentName);
  };

  // Handle canceling the editing of group name
  const cancelEditingGroupName = () => {
    setIsEditingGroupName(null);
    setEditingGroupName("");
  };

  // Update group mutation
  const updateGroupMutation = useMutation({
    mutationFn: updateQuestionGroup,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: groupsQueryKey });
      dispatch(
        showNotification({
          message: "Group name updated successfully",
          type: "success",
        })
      );
      setIsEditingGroupName(null);
      setEditingGroupName("");
      setIsProcessingGroup(false);
    },
    onError: () => {
      dispatch(
        showNotification({
          message: "Failed to update group name",
          type: "error",
        })
      );
      setIsProcessingGroup(false);
    },
  });

  const saveGroupName = (groupId: number) => {
    if (!editingGroupName.trim()) {
      dispatch(
        showNotification({
          message: "Group name cannot be empty",
          type: "warning",
        })
      );
      return;
    }

    setIsProcessingGroup(true);

    const group = questionGroups.find((g: QuestionGroup) => g.id === groupId);
    if (!group) return;

    // Update the group name in the local state immediately for better UX
    const updatedGroups = questionGroups.map((g: QuestionGroup) =>
      g.id === groupId ? { ...g, title: editingGroupName } : g
    );
    queryClient.setQueryData(groupsQueryKey, updatedGroups);

    // Then send the update to the server
    updateGroupMutation.mutate({
      id: groupId,
      title: editingGroupName,
      order: group.order,
    });
  };

  // Mutation for adding a single question
  const addQuestionMutation = useMutation({
    mutationFn: addQuestion,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: questionsQueryKey });
    },
    onError: (error) => {
      console.error("Error adding question:", error);
      dispatch(
        showNotification({
          message: "Failed to add a question. Please try again.",
          type: "error",
        })
      );
    },
  });

  // Function to handle adding questions from the library
  const handleAddQuestionsFromLibrary = async (
    selectedQuestions: Question[]
  ) => {
    if (selectedQuestions.length === 0) return;

    setIsAddingQuestions(true);

    try {
      // Calculate the starting position for new questions
      const maxPosition = questions.length > 0 ? Math.max(...questions.map(q => q.position)) : 0;

      // Process each selected question sequentially
      for (let i = 0; i < selectedQuestions.length; i++) {
        const question = selectedQuestions[i];
        const dataToSend = {
          label: question.label,
          isRequired: question.isRequired,
          hint: question.hint || "",
          placeholder: question.placeholder || "",
          inputType: String(question.inputType), // Convert to string to ensure compatibility
          questionOptions: question.questionOptions || [],
        };

        await addQuestion({
          contextType,
          contextId,
          dataToSend,
          position: maxPosition + i + 1, // Add questions at the end
        });
      }

      // Refresh the questions list
      queryClient.invalidateQueries({ queryKey: questionsQueryKey });

      dispatch(
        showNotification({
          message: `${selectedQuestions.length} question(s) added successfully`,
          type: "success",
        })
      );
    } catch (error) {
      console.error("Error adding questions:", error);
      dispatch(
        showNotification({
          message: "Failed to add questions from library",
          type: "error",
        })
      );
    } finally {
      setIsAddingQuestions(false);
    }
  };
  return (
    <div className="min-h-[60vh] relative">
      {(deleteQuestionMutation.isPending ||
        duplicateQuestionMutation.isPending ||
        deleteGroupMutation.isPending ||
        deleteGroupWithQuestionsMutation.isPending ||
        updatePositionsMutation.isPending ||
        isAddingQuestions) && <LoadingOverlay />}

      {/* For adding new questions */}
      <AddQuestionModal
        showModal={showAddQuestionModal}
        setShowModal={setShowAddQuestionModal}
        contextType={contextType}
        contextId={contextId}
        position={questions.length > 0 ? Math.max(...questions.map(q => q.position)) + 1 : 1}
      />

      {/* for editing existing questions, it requires a question object as props */}
      {currentQuestion && currentQuestion.inputType !== "table" && (
        <EditQuestionModal
          showModal={showEditQuestionModal}
          setShowModal={setShowEditQuestionModal}
          contextType={contextType}
          question={currentQuestion}
          contextId={contextId}
        />
      )}

      {/* for editing table questions specifically */}
      {currentQuestion && currentQuestion.inputType === "table" && (
        <EditTableQuestionModal
          showModal={showEditTableQuestionModal}
          setShowModal={setShowEditTableQuestionModal}
          contextType={contextType}
          question={currentQuestion}
          contextId={contextId}
        />
      )}

      {/* For adding/editing question groups */}
      <QuestionGroupModal
        showModal={showAddGroupModal}
        setShowModal={setShowAddGroupModal}
        contextType={contextType}
        contextId={contextId}
        questions={questions}
        questionGroups={questionGroups}
      />

      {/* For editing existing question groups */}
      {currentGroupId && (
        <QuestionGroupModal
          showModal={showEditGroupModal}
          setShowModal={setShowEditGroupModal}
          contextType={contextType}
          contextId={contextId}
          existingGroup={questionGroups.find(
            (g: QuestionGroup) => g.id === currentGroupId
          )}
          questions={questions}
          questionGroups={questionGroups}
        />
      )}

      {/* Delete question confirmation modal */}
      <ConfirmationModal
        showModal={showDeleteConfirmationModal}
        onClose={() => setShowDeleteConfirmationModal(false)}
        onConfirm={handleDelete}
        title="Delete Question"
        description="Are you sure you want to delete this question? This action cannot be undone."
        confirmButtonText="Delete"
        cancelButtonText="Cancel"
        confirmButtonClass="btn-danger"
      />

      {/* Delete group confirmation modal */}
      <DeleteQuestionGroupModal
        showModal={showDeleteGroupModal}
        setShowModal={setShowDeleteGroupModal}
        onConfirmDelete={handleConfirmDeleteGroup}
        onConfirmDeleteWithQuestions={handleConfirmDeleteGroupWithQuestions}
        isDeleting={
          deleteGroupMutation.isPending ||
          deleteGroupWithQuestionsMutation.isPending
        }
      />

      {/* Library questions sidebar */}
      <LibraryQuestionsSidebar
        isOpen={showLibrarySidebar}
        onClose={() => setShowLibrarySidebar(false)}
        onAddQuestions={handleAddQuestionsFromLibrary}
      />

      {/* Header with actions */}
      <div className="flex justify-between items-center mb-4">
        <div className="flex items-center">
          <h1 className="heading-text mr-4">Form Builder</h1>

          {/* Create Group button */}
          {selectedQuestionIds.length > 0 ? (
            <button
              className="btn-primary flex items-center gap-2"
              onClick={handleCreateGroupFromSelected}
              disabled={isProcessingGroup}
            >
              <FolderPlus size={16} />
              Create Group ({selectedQuestionIds.length})
            </button>
          ) : (
            <div className="flex gap-2">
              <button
                className="btn-outline flex items-center gap-2"
                onClick={() => {
                  // Toggle selection mode
                  if (!selectionMode) {
                    setSelectionMode(true);
                  } else {
                    setSelectionMode(false);
                    setSelectedQuestionIds([]);
                  }
                }}
              >
                <FolderPlus size={16} />
                {selectionMode ? "Cancel Selection" : "Select Questions"}
              </button>

              <button
                className="btn-outline flex items-center gap-2"
                onClick={() => setShowAddGroupModal(true)}
              >
                <FolderPlus size={16} />
                Create Empty Group
              </button>
            </div>
          )}
        </div>

        <div className="flex items-center space-x-2">
          <button
            className="btn-outline p-2"
            onClick={() => setIsPreviewMode(true)}
            title="Preview Form"
          >
            <Eye size={16} />
          </button>
          <button
            className="btn-outline p-2"
            onClick={() => setShowLibrarySidebar(true)}
            title="Question Library"
          >
            <BookOpen size={16} />
          </button>
          {/* <button
            className="btn-outline p-2"
            onClick={() => {}}
            title="Export Form"
          >
            <FileDown size={16} />
          </button>
          <button
            className="btn-outline p-2"
            onClick={() => {}}
            title="Import Form"
          >
            <FileUp size={16} />
          </button>
          <button className="btn-outline p-2" title="Settings">
            <Settings size={16} />
          </button> */}
        </div>
      </div>

      {/* Main content area */}
      <div className="section shadow-none border border-neutral-400">
        <DndContext
          sensors={sensors}
          collisionDetection={closestCenter}
          onDragEnd={handleDragEnd}
        >
          <SortableContext
            items={questions.map((question) => question.id)}
            strategy={verticalListSortingStrategy}
          >
            <div className="space-y-4">
              {questions.length === 0 ? (
                // No questions at all
                <div className="text-center py-16 px-4">
                  <h3 className="heading-text text-muted-foreground">
                    No questions yet
                  </h3>
                  <p className="mt-1 text-sm sub-text">
                    Get started by adding your first question
                  </p>
                  <div className="p-4 flex justify-center">
                    <button
                      onClick={() => setShowAddQuestionModal(true)}
                      className="btn-primary"
                      disabled={!editFormPermission}
                    >
                      <PlusCircle size={16} />
                      Add First Question
                    </button>
                  </div>
                </div>
              ) : (
                // Render unified form items (groups and individual questions) in order
                unifiedFormItems.map((item) => {
                  if (item.type === 'group') {
                    const group = item.data as QuestionGroup;
                    const groupQuestions = questions
                      .filter((q) => q.questionGroupId === group.id)
                      .sort((a, b) => a.position - b.position);

                    return (
                      <div key={`group-${group.id}`} className="mb-4">
                        <QuestionGroupItem
                          id={group.id}
                          title={group.title}
                          questions={groupQuestions}
                          onEditGroup={handleEditGroup}
                          onDeleteGroup={handleDeleteGroup}
                          onAddQuestionToGroup={handleAddQuestionToGroup}
                          onEditQuestion={(question) => {
                            setCurrentQuestion(question);
                            if (question.inputType === "table") {
                              setShowEditTableQuestionModal(true);
                            } else {
                              setShowEditQuestionModal(true);
                            }
                          }}
                          onDeleteQuestion={(question) => {
                            setCurrentQuestion(question);
                            setShowDeleteConfirmationModal(true);
                          }}
                          onDuplicateQuestion={(question) => {
                            setCurrentQuestion(question);
                            duplicateQuestionMutation.mutate({
                              id: question.id,
                              contextType,
                              contextId,
                            });
                          }}
                          onReorderQuestions={(questionPositions) => {
                            updatePositionsMutation.mutate({
                              contextType,
                              contextId,
                              questionPositions,
                            });
                          }}
                          isEditing={isEditingGroupName === group.id}
                          onStartEditing={startEditingGroupName}
                          onSaveGroupName={saveGroupName}
                          onCancelEditing={cancelEditingGroupName}
                          editingName={editingGroupName}
                          onEditingNameChange={setEditingGroupName}
                          selectionMode={selectionMode}
                        />
                      </div>
                    );
                  } else {
                    const question = item.data as Question;
                    return (
                      <div key={`question-${question.id}`} className="mb-4">
                        <QuestionItem
                          question={question}
                          onEdit={() => {
                            setCurrentQuestion(question);
                            if (question.inputType === "table") {
                              setShowEditTableQuestionModal(true);
                            } else {
                              setShowEditQuestionModal(true);
                            }
                          }}
                          onDelete={() => {
                            setCurrentQuestion(question);
                            setShowDeleteConfirmationModal(true);
                          }}
                          onDuplicate={() => {
                            setCurrentQuestion(question);
                            duplicateQuestionMutation.mutate({
                              id: question.id,
                              contextType,
                              contextId,
                            });
                          }}
                          selectionMode={selectionMode}
                          isSelected={selectedQuestionIds.includes(question.id)}
                          onToggleSelect={() =>
                            toggleQuestionSelection(question.id)
                          }
                        />
                      </div>
                    );
                  }
                })
              )}
            </div>
          </SortableContext>
        </DndContext>
      </div>

      {/* Bottom action bar */}
      {questions.length > 0 && (
        <div className="sticky bottom-0 p-4 flex justify-center">
          <button
            className={`btn-primary  max-w-md flex items-center justify-center gap-2 ${
              !editFormPermission && "text-gray-400 cursor-not-allowed"
            }`}
            onClick={() => setShowAddQuestionModal(true)}
            disabled={!editFormPermission}
          >
            <PlusCircle size={16} />
            Add Question
          </button>
        </div>
      )}
    </div>
  );
};

export { FormBuilder };

import { Request, Response } from "express";
import * as reportController from "../../controllers/reportController";
import reportRepository from "../../repositories/reportRepository";
import formSubmissionRepository from "../../repositories/formSubmissionRepository";

// Mock dependencies
jest.mock("../../repositories/reportRepository");
jest.mock("../../repositories/formSubmissionRepository");

// Mock the hashids decode function
const mockDecode = jest.fn().mockReturnValue([123]);
reportController.hashids.decode = mockDecode;

// Get the getProjectReport function
const { getProjectReport } = reportController;

describe("Report Controller", () => {
  let mockRequest: Partial<Request & { user?: { id: number } }>;
  let mockResponse: Partial<Response>;
  let responseObject: any = {};

  beforeEach(() => {
    // Reset mocks before each test
    jest.clearAllMocks();
    jest.resetAllMocks();

    // Setup mock response
    mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockImplementation((result) => {
        responseObject = result;
        return mockResponse;
      }),
    };

    // Reset response object
    responseObject = {};

    // Setup default authenticated user
    mockRequest = {
      user: {
        id: 1,
      },
      params: {
        projectId: "hashedProjectId",
      },
      query: {},
    };

    // Reset the mockDecode function
    mockDecode.mockReturnValue([123]);
  });

  describe("getProjectReport", () => {
    it("should generate a project report successfully", async () => {
      // Mock data
      const mockReport = {
        summary: {
          totalSubmissions: 10,
          totalQuestions: 2, // Only selectone and selectmany questions
          averageResponseRate: 80,
        },
        data: [
          {
            question: "Select One Question",
            type: "selectone",
            answered: 8,
            total: 10,
          },
          {
            question: "Select Many Question",
            type: "selectmany",
            answered: 7,
            total: 10,
          },
        ],
        metadata: {
          projectName: "Test Project",
          generatedAt: new Date(),
        },
      };

      // Mock repository responses
      (
        formSubmissionRepository.isProjectAccessible as jest.Mock
      ).mockResolvedValue(true);
      (reportRepository.generateReport as jest.Mock).mockResolvedValue(
        mockReport
      );

      await getProjectReport(mockRequest as any, mockResponse as Response);

      expect(formSubmissionRepository.isProjectAccessible).toHaveBeenCalledWith(
        1,
        123
      );
      expect(reportRepository.generateReport).toHaveBeenCalledWith(123, {
        type: "default",
        startDate: undefined,
        endDate: undefined,
      });

      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(responseObject).toHaveProperty("statusCode", 200);
      expect(responseObject).toHaveProperty("data", mockReport);
      expect(responseObject).toHaveProperty(
        "message",
        "Report generated successfully"
      );
    });

    it("should generate a report with date filters", async () => {
      // Setup date filters
      const startDate = "2023-01-01";
      const endDate = "2023-12-31";
      mockRequest.query = {
        startDate,
        endDate,
        type: "detailed",
      };

      // Mock data
      const mockReport = {
        summary: { totalSubmissions: 5 },
        data: [],
        metadata: { projectName: "Test Project" },
      };

      // Mock repository responses
      (
        formSubmissionRepository.isProjectAccessible as jest.Mock
      ).mockResolvedValue(true);
      (reportRepository.generateReport as jest.Mock).mockResolvedValue(
        mockReport
      );

      await getProjectReport(mockRequest as any, mockResponse as Response);

      expect(reportRepository.generateReport).toHaveBeenCalledWith(123, {
        type: "detailed",
        startDate: new Date(startDate),
        endDate: new Date(endDate),
      });

      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(responseObject).toHaveProperty("data", mockReport);
    });

    it("should return 401 when user is not authenticated", async () => {
      // User not authenticated
      mockRequest.user = undefined;

      await getProjectReport(mockRequest as any, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(401);
      expect(responseObject).toHaveProperty("statusCode", 401);
      expect(responseObject).toHaveProperty("message", "Unauthorized");
      expect(reportRepository.generateReport).not.toHaveBeenCalled();
    });

    it("should return 400 when project ID is invalid", async () => {
      // Mock invalid project ID decoding
      mockDecode.mockReturnValue([]);

      await getProjectReport(mockRequest as any, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(responseObject).toHaveProperty("statusCode", 400);
      expect(responseObject).toHaveProperty("message", "Invalid project ID");
      expect(reportRepository.generateReport).not.toHaveBeenCalled();
    });

    it("should return 403 when user doesn't have access to the project", async () => {
      // Mock no access to project
      (
        formSubmissionRepository.isProjectAccessible as jest.Mock
      ).mockResolvedValue(false);

      await getProjectReport(mockRequest as any, mockResponse as Response);

      expect(formSubmissionRepository.isProjectAccessible).toHaveBeenCalledWith(
        1,
        123
      );
      expect(mockResponse.status).toHaveBeenCalledWith(403);
      expect(responseObject).toHaveProperty("statusCode", 403);
      expect(responseObject).toHaveProperty(
        "message",
        "You don't have access to this project"
      );
      expect(reportRepository.generateReport).not.toHaveBeenCalled();
    });

    it("should handle errors and return 500", async () => {
      // Mock repository error
      (
        formSubmissionRepository.isProjectAccessible as jest.Mock
      ).mockResolvedValue(true);
      (reportRepository.generateReport as jest.Mock).mockRejectedValue(
        new Error("Database error")
      );

      await getProjectReport(mockRequest as any, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(responseObject).toHaveProperty("statusCode", 500);
      expect(responseObject.message).toContain("Error generating report");
    });

    it("should include all question types in the report", async () => {
      // Mock data with mixed question types
      const mockReport = {
        summary: {
          totalSubmissions: 5,
          totalQuestions: 4, // All question types
          averageResponseRate: 90,
        },
        data: [
          {
            question: "Select One Question",
            type: "selectone",
            answered: 4,
            total: 5,
          },
          {
            question: "Select Many Question",
            type: "selectmany",
            answered: 5,
            total: 5,
          },
          {
            question: "Text Question",
            type: "text",
            answered: 3,
            total: 5,
          },
          {
            question: "Number Question",
            type: "number",
            answered: 4,
            total: 5,
          },
        ],
        metadata: {
          projectName: "Test Project",
          generatedAt: new Date(),
        },
      };

      // Mock repository responses
      (
        formSubmissionRepository.isProjectAccessible as jest.Mock
      ).mockResolvedValue(true);
      (reportRepository.generateReport as jest.Mock).mockResolvedValue(
        mockReport
      );

      await getProjectReport(mockRequest as any, mockResponse as Response);

      // Verify the report contains all question types
      expect(responseObject.data.data.length).toBe(4);

      // Verify each question type is included
      const selectOneQuestion = responseObject.data.data.find(
        (item: any) => item.type === "selectone"
      );
      const selectManyQuestion = responseObject.data.data.find(
        (item: any) => item.type === "selectmany"
      );
      const textQuestion = responseObject.data.data.find(
        (item: any) => item.type === "text"
      );
      const numberQuestion = responseObject.data.data.find(
        (item: any) => item.type === "number"
      );

      expect(selectOneQuestion).toBeDefined();
      expect(selectManyQuestion).toBeDefined();
      expect(textQuestion).toBeDefined();
      expect(numberQuestion).toBeDefined();
    });
  });
});

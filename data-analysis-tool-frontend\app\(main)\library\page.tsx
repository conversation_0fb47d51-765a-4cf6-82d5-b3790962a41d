"use client";

import { Template } from "@/types";
import { GeneralTable } from "@/components/tables/GeneralTable";
import { TemplateList } from "@/components/library/TemplateList";
import { TemplateListColumns } from "@/components/tables/columns/TemplateListColumns";

export default function LibraryPage() {
  // change this after creating question blocks
  const questionBlockData: Template[] = [];

  return (
    <div className="p-6 space-y-6 section gap-8">
      <h1 className="text-2xl font-semibold">My Library</h1>
      <TemplateList />
      {/* change this after creating question blocks */}
      <div className="flex flex-col gap-4">
        <h1 className="sub-heading-text">Question Blocks</h1>
        {questionBlockData.length > 0 ? (
          <GeneralTable
            columns={TemplateListColumns}
            data={questionBlockData}
          />
        ) : (
          <div className="text-center py-16 space-y-4">
            <p className="text-lg">
              Let's get started by creating your first library question, block,
              template or collection. Click the New button to create it.
            </p>
            <p className="text-sm text-gray-500">
              Advanced users: You can also drag and drop XLSForms here and they
              will be uploaded and converted to library items.
            </p>
          </div>
        )}
      </div>
    </div>
  );
}

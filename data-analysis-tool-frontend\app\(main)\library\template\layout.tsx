"use client";

import Spinner from "@/components/general/Spinner";
import { TemplateNavbar } from "@/components/template/TemplateNavbar";
import { useAuth } from "@/hooks/useAuth";
import { fetchTemplateById } from "@/lib/api/templates";
import { decode } from "@/lib/encodeDecode";
import { Template } from "@/types";
import { useQuery } from "@tanstack/react-query";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";
import { useParams } from "next/navigation";
import React, { useEffect, useState } from "react";

const TemplateLayout = ({ children }: { children: React.ReactNode }) => {
  const [hasMounted, setHasMounted] = useState<boolean>(false);

  useEffect(() => {
    setHasMounted(true);
  }, []);

  const { hashedId } = useParams();

  const hashedIdString = hashedId as string;

  const templateId = decode(hashedIdString);

  const { user } = useAuth();

  const {
    data: templateData,
    isLoading: templateLoading,
    isError: templateError,
  } = useQuery<Template>({
    queryKey: ["templates", user?.id, templateId],
    queryFn: () => fetchTemplateById({ templateId: templateId! }),
    enabled: !!templateId && !!user?.id,
  });

  // To prevent errors from showing when the component is not fully mounted.
  if (!hasMounted) return null;

  if (templateLoading) {
    return <Spinner />;
  }

  // If hashedId is missing, show an error
  if (!hashedId || templateId === null) {
    return (
      <div className="error-message">
        <h1 className="text-red-500">Error: Invalid Template ID (hashedId).</h1>
        <p className="text-neutral-700">
          Please make sure the URL contains a valid project identifier.
        </p>
      </div>
    );
  }

  if (templateError) {
    return (
      <p className="text-red-500">Failed to fetch template. Please try again</p>
    );
  }
  return (
    <div className="section flex flex-col gap-4">
      <div className="flex items-center justify-between">
        <h1 className="heading-text capitalize">{templateData?.name}</h1>
        <Link href="/library" className="flex items-center gap-2">
          <ArrowLeft size={16} />
          Back to library
        </Link>
      </div>
      <TemplateNavbar />
      <div className="px-8">{children}</div>
    </div>
  );
};

export default TemplateLayout;

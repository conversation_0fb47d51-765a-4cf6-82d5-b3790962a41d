"use client";

import React from "react";
import Modal from "./Modal";

const ConfirmationModal = ({
  showModal,
  onClose,
  onConfirm,
  title,
  description,
  confirmButtonText,
  cancelButtonText,
  confirmButtonClass,
  children,
}: {
  showModal: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  description: React.ReactNode; // Accept ReactNode for flexible content
  confirmButtonText: string;
  cancelButtonText?: string;
  confirmButtonClass?: string;
  children?: React.ReactNode; // Additional content like warnings or icons
}) => {
  return (
    <Modal
      isOpen={showModal}
      onClose={onClose}
      className="p-6 rounded-md max-w-xl"
    >
      <h2 className="text-lg font-semibold text-neutral-700">{title}</h2>
      <div className="text-neutral-700 mt-2">{description}</div>
      {children && <div className="mt-6 space-y-4">{children}</div>}
      <div className="flex justify-end gap-4 mt-6">
        <button className="btn-outline" onClick={onClose} type="button">
          {cancelButtonText || "Cancel"}
        </button>
        <button
          className={`font-medium rounded-md shadow-md text-neutral-100 flex items-center justify-center gap-2 px-4 py-2 active:scale-95 transition-all duration-300 ${confirmButtonClass}`}
          onClick={onConfirm}
          type="button"
        >
          {confirmButtonText}
        </button>
      </div>
    </Modal>
  );
};

export { ConfirmationModal };

import axios from "@/lib/axios";

/**
 * Fetch all question groups for a project
 */
export const fetchQuestionGroups = async ({ projectId }: { projectId: number }) => {
  try {
    // Use the project endpoint to fetch the project with its question groups
    const { data } = await axios.get(`/projects/form/${projectId}`);
    console.log("Fetched project with question groups:", data);

    // Extract question groups from the project data
    const questionGroups = data.data?.project?.questionGroup || [];
    console.log("Extracted question groups:", questionGroups);

    return questionGroups;
  } catch (error) {
    console.error("Error fetching question groups from project endpoint:", error);

    // Fallback to direct question groups endpoint
    try {
      console.log("Trying fallback to direct question groups endpoint");
      const { data } = await axios.post(`/question-groups`, { projectId });
      console.log("Fallback response:", data);
      return data.data?.projectGroup || [];
    } catch (fallbackError) {
      console.error("Error in fallback fetch:", fallbackError);

      // Last resort: create a dummy group for debugging
      if (process.env.NODE_ENV === 'development') {
        console.log("Creating dummy group for debugging");
        return [];
      }

      return [];
    }
  }
};

/**
 * Create a new question group
 */
export const createQuestionGroup = async ({
  title,
  order,
  projectId,
  selectedQuestionIds,
}: {
  title: string;
  order: number;
  projectId: number;
  selectedQuestionIds?: number[];
}) => {
  try {
    console.log("Creating question group with data:", {
      title,
      order,
      projectId,
      selectedQuestionIds
    });

    const { data } = await axios.post(`/question-groups`, {
      title,
      order,
      projectId,
      selectedQuestionIds: selectedQuestionIds || [],
    });

    console.log("Create question group response:", data);
    return data;
  } catch (error) {
    console.error("Error creating question group:", error);
    throw error;
  }
};

/**
 * Update an existing question group
 */
export const updateQuestionGroup = async ({
  id,
  title,
  order,
  selectedQuestionIds,
}: {
  id: number;
  title: string;
  order: number;
  selectedQuestionIds?: number[];
}) => {
  try {
    console.log("Updating question group with data:", {
      id,
      title,
      order,
      selectedQuestionIds
    });

    const { data } = await axios.patch(`/question-groups`, {
      id,
      title,
      order,
      selectedQuestionIds,
    });

    console.log("Update question group response:", data);
    return data;
  } catch (error) {
    console.error("Error updating question group:", error);
    throw error;
  }
};

/**
 * Delete a question group
 */
export const deleteQuestionGroup = async ({ id }: { id: number }) => {
  try {
    console.log(`Deleting question group with ID: ${id}`);
    const { data } = await axios.delete(`/question-groups/${id}`);
    console.log("Delete question group response:", data);
    return data;
  } catch (error) {
    console.error("Error deleting question group:", error);
    throw error;
  }
};

/**
 * Delete a question group and all its questions
 */
export const deleteQuestionAndGroup = async ({ id }: { id: number }) => {
  try {
    console.log(`Deleting question group and its questions with ID: ${id}`);
    const { data } = await axios.delete(`/question-groups/group/question/${id}`);
    console.log("Delete question group and questions response:", data);
    return data;
  } catch (error) {
    console.error("Error deleting question group and questions:", error);
    throw error;
  }
};

/**
 * Remove a question from a group
 */
export const removeQuestionFromGroup = async ({
  groupId,
  questionId,
}: {
  groupId: number;
  questionId: number;
}) => {
  const { data } = await axios.patch(`/question-groups/question/remove`, {
    groupId,
    questionId,
  });
  return data;
};

/**
 * Move a question from one group to another
 */
export const moveQuestionBetweenGroups = async ({
  groupId,
  newGroupId,
  questionId,
}: {
  groupId: number;
  newGroupId: number;
  questionId: number;
}) => {
  const { data } = await axios.patch(`/question-groups/question/move`, {
    groupId,
    newGroupId,
    questionId,
  });
  return data;
};

// Parent group functionality removed for simplicity

import express from "express";
import { authenticate } from "../middleware/auth";
import {
  createFormSubmission,
  getProjectFormSubmissions,
  getFormSubmission,
  updateFormSubmission,
  deleteFormSubmission,
  ToggleFormSubmissionLoginRequired,
  DeleteManyFormSubmission,
} from "../controllers/formSubmissionController";
import { checkPermission } from "../middleware/checkPermission";

const router = express.Router();

// Protect all routes with authentication
router.use(authenticate);

// Create a new form submission
router.post(
  "/",
  checkPermission([
    "manageProject",
    "addSubmissions",
  ]) as unknown as express.RequestHandler,
  createFormSubmission as unknown as express.RequestHandler
);
router.delete(
  "/deleteMultiple",
  checkPermission([
    "manageProject",
    "deleteSubmissions",
  ]) as unknown as express.RequestHandler,

  DeleteManyFormSubmission as unknown as express.RequestHandler
);

// Get submissions for a project
router.get(
  "/:projectId",
  checkPermission([
    "manageProject",
    "viewSubmissions",
  ]) as unknown as express.RequestHandler,
  getProjectFormSubmissions as unknown as express.RequestHandler
);

// Get a specific submission
router.get(
  "/:id",
  checkPermission([
    "manageProject",
    "viewSubmissions",
  ]) as unknown as express.RequestHandler,
  getFormSubmission as unknown as express.RequestHandler
);

// Update a submission
router.patch(
  "/:id",
  checkPermission([
    "manageProject",
    "editSubmissions",
  ]) as unknown as express.RequestHandler,
  updateFormSubmission as unknown as express.RequestHandler
);
router.delete(
  "/:id",
  checkPermission([
    "manageProject",
    "deleteSubmissions",
  ]) as unknown as express.RequestHandler,

  deleteFormSubmission as unknown as express.RequestHandler
);

router.patch(
  "/change-login-required/:id",
  checkPermission([
    "manageProject",
    "editSubmissions",
  ]) as unknown as express.RequestHandler,

  ToggleFormSubmissionLoginRequired as unknown as express.RequestHandler
);

// Delete a submission

export default router;

import { z } from "zod";

export const formSubmissionSchema = z.object({
  projectId: z.number(),
  status: z.string().optional(),
  deviceInfo: z.string().optional(),
  loginRequired: z.boolean().optional(),
  location: z.string().optional(),
  metadata: z.record(z.any()).optional(),
  startedAt: z.coerce.date().optional(),
});

export const updateFormSubmissionSchema = z.object({
  status: z.string().optional(),
  completedAt: z.coerce.date().optional(),
  durationSeconds: z.number().optional(),
  loginRequired: z.boolean().optional(),
  metadata: z.record(z.any()).optional(),
  submittedAt: z.coerce.date().optional(),
});

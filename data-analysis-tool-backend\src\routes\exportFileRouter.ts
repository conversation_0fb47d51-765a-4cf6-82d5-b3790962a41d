import {
  downloadExportedFile,
  generateAndSaveExport,
  findAllExportFile,
  DeleteExportFile,
} from "../controllers/exportFileController";
import express from "express";

const router = express.Router();

import { authenticate } from "../middleware/auth";

router.use(authenticate);
router.get("/", findAllExportFile as unknown as express.RequestHandler);

router.post("/:id", generateAndSaveExport as unknown as express.RequestHandler);
router.delete("/:id", DeleteExportFile as unknown as express.RequestHandler);

router.get(
  "/download/:fileId",
  downloadExportedFile as unknown as express.RequestHandler
);

router.delete("/:id", DeleteExportFile as unknown as express.RequestHandler);

export default router;

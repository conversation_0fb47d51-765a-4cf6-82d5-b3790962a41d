import express from "express";

import {
  createLibraryQuestionBlockQuestionGroup,
  updateLibraryQuestionBlockQuestionGroup,
  deleteLibraryQuestionBlockQuestionGroup,
  deleteLibraryQuestionBlockQuestionAndGroup,
  removeLibraryQuestionBlockQuestionIdFromGroup,
  updateLibraryQuestionBlockQuestionFromOneGroupToAnother,
  updateLibraryQuestionBlockOneGroupInsideAnotherGroup,
  removeLibraryQuestionBlockGroupFromParentGroup,
} from "../controllers/libraryQuestionBlockQuestionGroupController";

const router = express.Router();

router.post(
  "/",
  createLibraryQuestionBlockQuestionGroup as unknown as express.RequestHandler
);
router.patch(
  "/",
  updateLibraryQuestionBlockQuestionGroup as unknown as express.RequestHandler
);
router.delete(
  "/:id",
  deleteLibraryQuestionBlockQuestionGroup as unknown as express.RequestHandler
);
router.delete(
  "/group/question/:id",
  deleteLibraryQuestionBlockQuestionAndGroup as unknown as express.RequestHandler
);
router.patch(
  "/question/remove",
  removeLibraryQuestionBlockQuestionIdFromGroup as unknown as express.RequestHandler
);
router.patch(
  "/question/move",
  updateLibraryQuestionBlockQuestionFromOneGroupToAnother as unknown as express.RequestHandler
);
router.patch(
  "/group/add",
  updateLibraryQuestionBlockOneGroupInsideAnotherGroup as unknown as express.RequestHandler
);
router.patch(
  "/group/remove",
  removeLibraryQuestionBlockGroupFromParentGroup as unknown as express.RequestHandler
);

export default router;

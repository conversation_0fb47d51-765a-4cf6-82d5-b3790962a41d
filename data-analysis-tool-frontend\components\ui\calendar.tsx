"use client";

import * as React from "react";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { DayPicker, useNavigation } from "react-day-picker";
import { cn } from "@/lib/utils";
import "react-day-picker/dist/style.css";

export type CalendarProps = React.ComponentProps<typeof DayPicker>;

function CustomNav() {
  const { previousMonth, nextMonth, goToMonth } = useNavigation();
  return (
    <div className="space-x-1 flex items-center">
      <button
        type="button"
        onClick={() => previousMonth && goToMonth(previousMonth)}
        className="h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100"
      >
        <ChevronLeft className="h-4 w-4" />
      </button>
      <button
        type="button"
        onClick={() => nextMonth && goToMonth(nextMonth)}
        className="h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100"
      >
        <ChevronRight className="h-4 w-4" />
      </button>
    </div>
  );
}

function Calendar({
  className,
  classNames,
  showOutsideDays = true,
  ...props
}: CalendarProps) {
  return (
    <DayPicker
      showOutsideDays={showOutsideDays}
      className={cn(
        "p-4 bg-neutral-100 rounded-2xl shadow-xl max-w-md mx-auto border border-gray-100",
        className
      )}
      components={{
        Nav: CustomNav,
      }}
      {...props}
    />
  );
}
Calendar.displayName = "Calendar";

export { Calendar };

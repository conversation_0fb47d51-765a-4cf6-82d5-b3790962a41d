"use client";
import React, { useEffect, useState } from "react";
import Modal from "./Modal";
import { AlertTriangle, Mail } from "lucide-react";
import axios from "@/lib/axios";
import { useDispatch } from "react-redux";
import { showNotification } from "@/redux/slices/notificationSlice";

const VerificationModal = ({
  email,
  showModal,
  setShowModal,
}: {
  email: string;
  showModal: boolean;
  setShowModal: React.Dispatch<React.SetStateAction<boolean>>;
}) => {
  const dispatch = useDispatch();

  const sendVerificationEmail = async () => {
    try {
      await axios.post(`/users/sendverificationemail`, {
        email: email,
      });
    } catch (error) {
      dispatch(
        showNotification({
          message:
            "Failed to send verification email. Please try again in a minute.",
          type: "error",
        })
      );
    }
  };
  const [isResendDisabled, setIsResendDisabled] = useState<boolean>(true);
  const [countDown, setCountDown] = useState<number>(60);

  useEffect(() => {
    let timer: number;
    if (isResendDisabled && countDown > 0) {
      timer = window.setInterval(() => {
        setCountDown((prev) => prev - 1);
      }, 1000);
    } else if (countDown === 0) {
      setIsResendDisabled(false);
      setCountDown(60);
    }
    return () => clearInterval(timer);
  }, [isResendDisabled, countDown]);

  useEffect(() => {
    if (showModal && email) {
      sendVerificationEmail();
    }
    return () => {
      setCountDown(60), setIsResendDisabled(true);
    };
  }, [showModal]);

  const handleResend = async () => {
    setIsResendDisabled(true);
    const apiUrl =
      process.env.NEXT_PUBLIC_API_URL || "http://localhost:4000/api";
    try {
      await axios.post(`/users/sendverificationemail`, { email });
    } catch (error) {
      console.error("error sending email", error);
    }
  };

  return (
    <Modal
      isOpen={showModal}
      onClose={() => setShowModal(false)}
      className="flex flex-col items-center gap-4"
    >
      <div className="rounded-full p-2 bg-primary-300">
        <Mail className="text-primary-500" />
      </div>
      <h1 className="heading-text">Check your email</h1>
      <p className="flex flex-col items-center">
        We've sent a verification email to:{" "}
        <span className="font-medium">{email}</span>
      </p>
      <div className="flex gap-2 items-center bg-yellow-100 text-yellow-900 px-4 py-2 rounded-md">
        <AlertTriangle size={16} /> Didn't receive the email? Check your spam
        folder
      </div>
      <button
        className="btn-primary"
        onClick={handleResend}
        disabled={isResendDisabled}
      >
        {isResendDisabled ? (
          <div className="flex items-center gap-2">
            <span>Resend in {countDown}s</span>
            <div className="size-4 animate-spin border-x-2 rounded-full"></div>
          </div>
        ) : (
          <span>Resend</span>
        )}
      </button>
    </Modal>
  );
};

export { VerificationModal };

"use client";

import React from "react";
import { Question } from "@/types/formBuilder";
import { Label } from "@/components/ui/label";

interface NestedQuestionRendererProps {
  questionGroup: {
    question: Question;
    isVisible: boolean;
    isFollowUp: boolean;
    followUps: Array<{
      question: Question;
      isVisible: boolean;
    }>;
  };
  renderQuestionInput: (question: Question) => React.ReactNode;
  errors: Record<number, string>;
  className?: string;
}

const NestedQuestionRenderer: React.FC<NestedQuestionRendererProps> = ({
  questionGroup,
  renderQuestionInput,
  errors,
  className = "",
}) => {
  const { question: parentQuestion, isVisible: isParentVisible, followUps } = questionGroup;
  
  // Don't render anything if neither parent nor any follow-ups are visible
  if (!isParentVisible && !followUps.some(f => f.isVisible)) {
    return null;
  }

  return (
    <div className={`${className}`}>
      {/* Parent Question */}
      {isParentVisible && (
        <div className="border border-neutral-500 dark:border-gray-700 rounded-md p-4 bg-neutral-100 dark:bg-gray-800">
          <div className="mb-2">
            <Label className="text-base font-medium">
              {parentQuestion.label}
              {parentQuestion.isRequired && (
                <span className="text-red-500 ml-1">*</span>
              )}
            </Label>
            {parentQuestion.hint && (
              <p className="text-sm text-muted-foreground mt-1">
                {parentQuestion.hint}
              </p>
            )}
            {errors[parentQuestion.id] && (
              <p className="text-sm text-red-500 mt-1">
                {errors[parentQuestion.id]}
              </p>
            )}
          </div>
          <div className="mt-2">{renderQuestionInput(parentQuestion)}</div>
          
          {/* Follow-up Questions */}
          {followUps.some(f => f.isVisible) && (
            <div className="mt-4 ml-4 space-y-3 border-l-2 border-blue-200 dark:border-blue-700 pl-4">
              {followUps.map(({ question: followUpQuestion, isVisible: isFollowUpVisible }) => (
                isFollowUpVisible && (
                  <div
                    key={followUpQuestion.id}
                    className="border border-gray-100 dark:border-gray-600 rounded-md p-3 bg-blue-50 dark:bg-blue-900/20"
                  >
                    <div className="mb-2">
                      <Label className="text-sm font-medium text-blue-900 dark:text-blue-100">
                        {followUpQuestion.label}
                        {followUpQuestion.isRequired && (
                          <span className="text-red-500 ml-1">*</span>
                        )}
                      </Label>
                      {followUpQuestion.hint && (
                        <p className="text-xs text-blue-700 dark:text-blue-300 mt-1">
                          {followUpQuestion.hint}
                        </p>
                      )}
                      {errors[followUpQuestion.id] && (
                        <p className="text-xs text-red-500 mt-1">
                          {errors[followUpQuestion.id]}
                        </p>
                      )}
                    </div>
                    <div className="mt-2">{renderQuestionInput(followUpQuestion)}</div>
                  </div>
                )
              ))}
            </div>
          )}
        </div>
      )}
      
      {/* Render follow-ups without parent if parent is not visible but follow-ups are */}
      {!isParentVisible && followUps.some(f => f.isVisible) && (
        <div className="space-y-3">
          {followUps.map(({ question: followUpQuestion, isVisible: isFollowUpVisible }) => (
            isFollowUpVisible && (
              <div
                key={followUpQuestion.id}
                className="border border-gray-200 dark:border-gray-700 rounded-md p-4 bg-white dark:bg-gray-800"
              >
                <div className="mb-2">
                  <Label className="text-base font-medium">
                    {followUpQuestion.label}
                    {followUpQuestion.isRequired && (
                      <span className="text-red-500 ml-1">*</span>
                    )}
                  </Label>
                  {followUpQuestion.hint && (
                    <p className="text-sm text-muted-foreground mt-1">
                      {followUpQuestion.hint}
                    </p>
                  )}
                  {errors[followUpQuestion.id] && (
                    <p className="text-sm text-red-500 mt-1">
                      {errors[followUpQuestion.id]}
                    </p>
                  )}
                </div>
                <div className="mt-2">{renderQuestionInput(followUpQuestion)}</div>
              </div>
            )
          ))}
        </div>
      )}
    </div>
  );
};

export default NestedQuestionRenderer;

"use client";
import React, { useEffect, useState } from "react";
import Modal from "./Modal";
import { FieldValues, useForm } from "react-hook-form";
import { Select } from "../general/Select";
import countries from "@/constants/countryNames.json";
import { SectorLabelMap } from "@/constants/sectors";
import { Briefcase, FileText, Globe } from "lucide-react";
import { useDispatch } from "react-redux";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { showNotification } from "@/redux/slices/notificationSlice";
import { useRouter } from "next/navigation";
import { labelToKey } from "@/lib/labelToKey";
import { createProjectFromTemplate } from "@/lib/api/projects";
import { useAuth } from "@/hooks/useAuth";
import { Template } from "@/types";
import { fetchTemplateById } from "@/lib/api/templates";

const CreateProjectFromTemplateModal = ({
  showModal,
  closeModal,
  back,
  templateId,
}: {
  showModal: boolean;
  closeModal: () => void;
  back: () => void;
  templateId: number;
}) => {
  const {
    register,
    formState: { isSubmitting, errors, isSubmitted },
    handleSubmit,
    setValue,
    reset,
  } = useForm();

  const dispatch = useDispatch();
  const router = useRouter();

  const [selectedCountry, setSelectedCountry] = useState<string | null>(null);
  const [selectedSector, setSelectedSector] = useState<string | null>(null);

  useEffect(() => {
    register("country", { required: "Please select a country" });
    register("sector", { required: "Please select a sector" });
  }, [register]);

  useEffect(() => {
    setValue("country", selectedCountry, { shouldValidate: isSubmitted });
    setValue("sector", selectedSector, { shouldValidate: isSubmitted });
  }, [setValue, selectedCountry, selectedSector]);

  const { user, isLoading: sessionLoading } = useAuth();

  const {
    data: templateData,
    isLoading: templateLoading,
    isError: templateError,
  } = useQuery<Template>({
    queryKey: ["templates", user?.id, templateId],
    queryFn: () => fetchTemplateById({ templateId }),
    enabled: !!user?.id,
  });

  useEffect(() => {
    if (templateData) {
      reset({
        projectName: templateData.name,
        description: templateData.description,
        sector: templateData.sector,
        country: templateData.country,
      });
      setSelectedCountry(templateData.country);
      setSelectedSector(templateData.sector);
    }
  }, [templateData, reset]);

  const queryClient = useQueryClient();

  const projectMutation = useMutation({
    mutationFn: createProjectFromTemplate,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["projects"], exact: false });
      closeModal();
      router.push("/dashboard");
      dispatch(
        showNotification({
          message: "Project has been created successfully.",
          type: "success",
        })
      );
    },
    onError: (error) => {
      dispatch(
        showNotification({
          message: "Failed to create project" + error.message,
          type: "error",
        })
      );
    },
  });

  const onSubmit = async (data: FieldValues) => {
    const dataToSend = {
      templateId,
      name: data.projectName,
      description: data.description,
      sector: data.sector,
      country: data.country,
    };
    projectMutation.mutate(dataToSend);
  };

  if (sessionLoading || templateLoading || templateError) {
    return null;
  }

  return (
    <Modal isOpen={showModal} onClose={closeModal} className="w-3/6">
      <form className="flex flex-col gap-8" onSubmit={handleSubmit(onSubmit)}>
        <h1 className="heading-text">Create a new project</h1>
        <div className="flex flex-col gap-4">
          {/* Project Name */}
          <div className="label-input-group group">
            <label htmlFor="project-name" className="label-text">
              <FileText size={16} /> Project Name
            </label>
            <input
              {...register("projectName", {
                required: "Project name is required.",
              })}
              id="project-name"
              type="text"
              className="input-field"
              placeholder="Enter a project name"
            />
            {errors.projectName && (
              <p className="text-red-500 text-sm">{`${errors.projectName.message}`}</p>
            )}
          </div>
          {/* Project Description */}
          <div className="label-input-group group">
            <label htmlFor="description" className="label-text">
              Description
            </label>
            <textarea
              id="description"
              {...register("description", {
                required: "Please enter the project description",
              })}
              className="input-field resize-none"
              cols={4}
              placeholder="Enter the project description"
            />
            {errors.description && (
              <p className="text-red-500 text-sm">{`${errors.description.message}`}</p>
            )}
          </div>
          {/* Country and Sector */}
          <div className="grid grid-cols-2 gap-4">
            <div className="label-input-group group">
              <label htmlFor="country" className="label-text">
                <Globe size={16} />
                Country
              </label>
              <Select
                id={`country`}
                options={countries}
                value={selectedCountry}
                onChange={setSelectedCountry}
              />
              {errors.country && (
                <p className="text-red-500 text-sm">{`${errors.country.message}`}</p>
              )}
            </div>
            <div className="label-input-group group">
              <label htmlFor="sector" className="label-text">
                <Briefcase size={16} /> Sector
              </label>
              <Select
                id={`sector`}
                options={Object.values(SectorLabelMap)} // Display labels
                value={
                  selectedSector && SectorLabelMap[selectedSector]
                    ? SectorLabelMap[selectedSector]
                    : "Select an option"
                }
                onChange={(label) => {
                  const selectedKey = labelToKey(label, SectorLabelMap);
                  setSelectedSector(selectedKey); // Set the enum key for storage
                }}
              />
              {errors.sector && (
                <p className="text-red-500 text-sm">{`${errors.sector.message}`}</p>
              )}
            </div>
          </div>
          <div className="flex justify-end gap-3 mt-4">
            <button type="button" onClick={back} className="btn-outline">
              Back
            </button>
            <button type="submit" className="btn-primary">
              {isSubmitting ? (
                <span className="flex items-center gap-2">
                  Creating{" "}
                  <div className="size-4 animate-spin border-x border-neutral-100 rounded-full"></div>
                </span>
              ) : (
                "Create Project"
              )}
            </button>
          </div>
        </div>
      </form>
    </Modal>
  );
};

export { CreateProjectFromTemplateModal };

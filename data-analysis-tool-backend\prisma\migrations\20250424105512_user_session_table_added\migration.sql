/*
  Warnings:

  - You are about to drop the column `deviceId` on the `UserSession` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[userId,id]` on the table `UserSession` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `deviceInfo` to the `UserSession` table without a default value. This is not possible if the table is not empty.
  - Made the column `userId` on table `UserSession` required. This step will fail if there are existing NULL values in that column.

*/
-- AlterTable
ALTER TABLE "UserSession" DROP COLUMN "deviceId",
ADD COLUMN     "deviceInfo" TEXT NOT NULL,
ALTER COLUMN "userId" SET NOT NULL;

-- CreateIndex
CREATE INDEX "UserSession_userId_id_idx" ON "UserSession"("userId", "id");

-- CreateIndex
CREATE UNIQUE INDEX "UserSession_userId_id_key" ON "UserSession"("userId", "id");

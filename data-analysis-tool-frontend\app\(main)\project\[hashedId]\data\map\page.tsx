"use client";

import {
  RefreshCw,
  Search,
  Filter,
  Layers,
  ZoomIn,
  ZoomOut,
  MapPin,
} from "lucide-react";
import React, { useState } from "react";

export default function MapPage() {
  const [searchTerm, setSearchTerm] = useState("");

  return (
    <div className="flex flex-col space-y-6">
      {/* Header with title and action buttons */}
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-semibold text-neutral-800">Map</h1>
        <div className="flex gap-2">
          <button className="btn-primary" title="Refresh map">
            <RefreshCw className="w-4 h-4" />
            <span>Refresh</span>
          </button>
        </div>
      </div>

      {/* Map controls and search */}
      <div className="flex justify-between gap-4 flex-wrap">
        <div className="flex gap-2">
          <select className="border border-neutral-300 rounded px-3 py-2 bg-neutral-100 text-neutral-800 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 focus:outline-none transition-colors cursor-pointer">
            <option>Base Map</option>
            <option>Satellite</option>
            <option>Street Map</option>
            <option>Terrain</option>
          </select>
          <button className="btn-primary">
            <Layers className="w-4 h-4" />
            <span>Layers</span>
          </button>
          <button className="btn-primary">
            <Filter className="w-4 h-4" />
            <span>Filter</span>
          </button>
        </div>
        <div className="relative">
          <input
            type="text"
            placeholder="Search location..."
            className="pl-9 pr-4 py-2 border border-neutral-300 rounded w-64 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 focus:outline-none transition-colors"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
          <Search className="absolute left-3 top-2.5 w-4 h-4 text-neutral-400" />
        </div>
      </div>

      {/* Map placeholder */}
      <div className="relative bg-neutral-100 border border-neutral-200 rounded-md h-[500px] shadow-sm overflow-hidden flex items-center justify-center">
        <div className="absolute inset-0 bg-[url('https://via.placeholder.com/1200x800/f3f4f6/d1d5db?text=Map+Placeholder')] bg-cover bg-center opacity-40"></div>

        {/* Map pins placeholder */}
        <div className="absolute left-1/4 top-1/2 transform -translate-y-1/2">
          <MapPin className="w-8 h-8 text-red-500" />
        </div>
        <div className="absolute left-1/2 top-1/3 transform -translate-x-1/2">
          <MapPin className="w-8 h-8 text-red-500" />
        </div>
        <div className="absolute right-1/4 top-2/3 transform -translate-y-1/2">
          <MapPin className="w-8 h-8 text-red-500" />
        </div>

        {/* Zoom controls */}
        <div className="absolute right-4 top-4 flex flex-col gap-2">
          <button className="p-2 bg-primary-500 rounded-full shadow hover:bg-primary-600 text-neutral-100 transition-colors cursor-pointer">
            <ZoomIn className="w-4 h-4" />
          </button>
          <button className="p-2 bg-primary-500 rounded-full shadow hover:bg-primary-600 text-neutral-100 transition-colors cursor-pointer">
            <ZoomOut className="w-4 h-4" />
          </button>
        </div>

        {/* Message overlay for demonstration */}
        <div className="relative z-10 bg-neutral-100 px-6 py-4 rounded-lg shadow-lg">
          <p className="font-medium text-neutral-800">
            Map will be displayed here
          </p>
          <p className="text-sm text-neutral-500">
            We need to integrate mapping library here.
          </p>
        </div>
      </div>

      {/* Legend */}
      <div className="bg-neutral-100 rounded-md border border-neutral-200 p-4 shadow-sm">
        <h3 className="font-medium text-neutral-800 mb-2">Legend</h3>
        <div className="flex items-center gap-2 text-sm text-neutral-600 mb-1">
          <MapPin className="w-4 h-4 text-red-500" />
          <span>Survey Location</span>
        </div>
        <div className="flex items-center gap-2 text-sm text-neutral-600">
          <div className="w-4 h-4 bg-primary-300 rounded"></div>
          <span>High Density Area</span>
        </div>
      </div>
    </div>
  );
}

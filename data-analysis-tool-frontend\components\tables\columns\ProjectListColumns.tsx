"use client";

import { ColumnDef } from "@tanstack/react-table";
import { LuArrowUpDown } from "react-icons/lu";
import { Checkbox } from "@/components/ui/checkbox";
import { Project } from "@/types";
import Link from "next/link";
import { encode } from "@/lib/encodeDecode";
import { formatDate } from "@/lib/utils";
import { Eye } from "lucide-react";

// Function to format cell value based on data type
const formatCellValue = (value: any, type?: string): string => {
  if (value === null || value === undefined) return "-";

  if (typeof value === "boolean") {
    return value ? "Yes" : "No";
  }

  if (value instanceof Date) {
    return formatDate(value);
  }

  if (type === "date" && typeof value === "string") {
    try {
      return formatDate(new Date(value));
    } catch {
      return value;
    }
  }

  return String(value);
};

const ProjectListColumns: ColumnDef<Project>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        className="w-6 h-6 data-[state=checked]:bg-neutral-100 data-[state=checked]:text-primary-500 border-neutral-100 cursor-pointer"
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => {
      const projectId = row.original.id;
      const encryptedProjectId = encode(projectId);

      return (
        <div className="flex items-center gap-2">
          <Checkbox
            className="w-6 h-6 bg-neutral-100 border-neutral-400 data-[state=checked]:bg-neutral-100 data-[state=checked]:text-primary-500 cursor-pointer"
            checked={row.getIsSelected()}
            onCheckedChange={(value) => row.toggleSelected(!!value)}
            aria-label="Select row"
          />
          <Link
            title="View project"
            href={`project/${encryptedProjectId}/overview`}
            className=" hover:text-primary-500 transition-colors"
          >
            <Eye className="w-5 h-5" />
          </Link>
        </div>
      );
    },
    enableHiding: false,
  },
  {
    accessorKey: "name",
    header: ({ column }) => {
      return (
        <button
          className="flex items-center text-left"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Project name
          <LuArrowUpDown className="h-4 w-4" />
        </button>
      );
    },
    cell: ({ row }) => {
      const projectId = row.original.id;
      const encryptedProjectId = encode(projectId);
      return (
        <Link
          href={`project/${encryptedProjectId}/overview`}
          className="cursor-pointer text-primary-500 hover:text-primary-600 hover:underline transition-all duration-300"
        >
          {row.getValue("name")}
        </Link>
      );
    },
    enableSorting: true,
    sortingFn: (rowA, rowB, columnId) => {
      const a = rowA.getValue(columnId)?.toString().toLowerCase() || "";
      const b = rowB.getValue(columnId)?.toString().toLowerCase() || "";
      return a.localeCompare(b);
    },
  },

  {
    accessorKey: "description",
    header: "Description",
  },
  {
    accessorKey: "status",
    header: "Status",
  },
  {
    accessorKey: "updatedAt",
    header: "Date modified",
    cell: ({ getValue }) => {
      const value = getValue();
      return (
        <div className="font-medium text-neutral-700">
          {formatCellValue(value, "date") || "Not recorded"}
        </div>
      );
    },
  },
  {
    accessorKey: "lastDeployedAt",
    header: "Date deployed",
  },
  {
    accessorKey: "sector",
    header: "Sector",
  },
  {
    accessorKey: "country",
    header: "Countries",
  },
  {
    accessorKey: "lastSubmittionAt",
    header: "Last submission at",
    sortingFn: "basic",
  },
];

export { ProjectListColumns };

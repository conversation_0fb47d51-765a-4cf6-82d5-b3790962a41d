"use client";

import { VerificationModal } from "@/components/modals/VerificationModal";
import { showNotification } from "@/redux/slices/notificationSlice";
import { zodResolver } from "@hookform/resolvers/zod";
import { <PERSON><PERSON>heck, Eye, EyeOff } from "lucide-react";
import { useRouter, useParams } from "next/navigation";
import React, { useState } from "react";
import { FieldValues, useForm } from "react-hook-form";
import { useDispatch } from "react-redux";
import { z } from "zod";
import { useAuth } from "@/hooks/useAuth";

const signInSchema = z.object({
  email: z
    .string()
    .min(1, "Email is required")
    .email("Please enter a valid email address"),
  password: z.string().min(1, "Password is required"),
});

type SignInFormValues = z.infer<typeof signInSchema>;

const FormSignInPage = () => {
  const {
    register,
    formState: { errors, isSubmitting },
    handleSubmit,
    getValues,
    watch,
  } = useForm<SignInFormValues>({ resolver: zodResolver(signInSchema) });

  // Watch password field to determine when to show the eye button
  const passwordValue = watch("password");

  const router = useRouter();
  const dispatch = useDispatch();
  const { hashedId } = useParams();
  const redirectTo = `/form-test/${hashedId}`;

  const [showVerificationModal, setShowVerificationModal] =
    useState<boolean>(false);
  const [showPassword, setShowPassword] = useState<boolean>(false);

  const { signin } = useAuth({ skipFetchUser: true });

  const onSubmit = async (data: FieldValues) => {
    signin(
      { email: data.email, password: data.password },
      () => {
        dispatch(
          showNotification({ message: "Sign in successful.", type: "success" })
        );
        router.push(redirectTo);
      },
      (errorType) => {
        if (errorType === "unverified") {
          setShowVerificationModal(true);
        } else {
          dispatch(
            showNotification({
              message: "Invalid email or password. Please try again.",
              type: "error",
            })
          );
        }
      }
    );
  };

  return (
    <div className="min-h-screen flex items-center justify-center">
      <VerificationModal
        email={getValues("email")}
        showModal={showVerificationModal}
        setShowModal={setShowVerificationModal}
      />
      <div className="flex flex-col section w-11/12 mobile:w-4/5 tablet:w-lg">
        <div className="flex flex-col items-center gap-2 mb-8">
          <ShieldCheck size={36} />
          <h1 className="text-2xl tablet:text-3xl font-semibold text-center">
            Sign in to access this form
          </h1>
          <p className="text-neutral-700 text-center">
            Log in to complete the data analysis form
          </p>
        </div>
        <form
          className="flex flex-col gap-4 mb-4"
          onSubmit={handleSubmit(onSubmit)}
        >
          <div className="group label-input-group">
            <label htmlFor="email" className="label-text">
              Email
            </label>
            <input
              {...register("email")}
              id="email"
              type="email"
              placeholder="Enter your email address"
              className="input-field"
            />
            {errors.email && (
              <p className="text-red-500 text-sm">{`${errors.email.message}`}</p>
            )}
          </div>
          <div className="group label-input-group">
            <label htmlFor="password" className="label-text">
              Password
            </label>
            <div className="relative">
              <input
                {...register("password")}
                id="password"
                type={showPassword ? "text" : "password"}
                placeholder="Enter your password"
                className="input-field w-full pr-10"
              />
              {passwordValue && passwordValue.length > 0 && (
                <button
                  type="button"
                  tabIndex={-1}
                  className="absolute right-2 top-1/2 -translate-y-1/2 text-gray-500"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                  <span className="sr-only">
                    {showPassword ? "Hide" : "Show"} password
                  </span>
                </button>
              )}
            </div>
            {errors.password && (
              <p className="text-red-500 text-sm">{`${errors.password.message}`}</p>
            )}
          </div>
          <button type="submit" className="btn-primary" disabled={isSubmitting}>
            {isSubmitting ? (
              <span className="flex items-center gap-2">
                Signing in{" "}
                <div className="size-4 rounded-full border-x-2 animate-spin"></div>
              </span>
            ) : (
              "Submit"
            )}
          </button>
        </form>
      </div>
    </div>
  );
};

export default FormSignInPage;

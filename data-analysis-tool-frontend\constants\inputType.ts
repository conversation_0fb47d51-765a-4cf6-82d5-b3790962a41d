const inputTypeMapLiteral = {
  text: "Text",
  number: "Number",
  decimal: "Decimal",
  selectone: "Select one",
  selectmany: "Select many",
  date: "Date",
  dateandtime: "Date and time",
  table: "Table",
} as const;

export const InputTypeMap: Record<string, string> = inputTypeMapLiteral;

export const InputTypeKeys = Object.keys(inputTypeMapLiteral) as Array<
  keyof typeof inputTypeMapLiteral
>;

export type InputType = (typeof InputTypeKeys)[number];

/*
  Warnings:

  - You are about to drop the `Library` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropFore<PERSON>Key
ALTER TABLE "Library" DROP CONSTRAINT "Library_userId_fkey";

-- DropTable
DROP TABLE "Library";

-- CreateTable
CREATE TABLE "LibraryTemplate" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "sector" "Sector" NOT NULL,
    "country" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "userId" INTEGER NOT NULL,

    CONSTRAINT "LibraryTemplate_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "LibraryTemplate_name_userId_key" ON "LibraryTemplate"("name", "userId");

-- AddForeignKey
ALTER TABLE "LibraryTemplate" ADD CONSTRAINT "LibraryTemplate_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

// There is no try catch error handling because these functions are used by react query that does the error handling

import axios from "@/lib/axios";

const fetchUserProfile = async () => {
  const { data } = await axios.get(`/users/profile`);
  return data.profile;
};

const changeEmail = async ({ email }: { email: string }) => {
  const { data } = await axios.patch(`/users/change-email`, { email });
  return data;
};

const updateUserProfile = async ({
  dataToSend,
}: {
  dataToSend: {
    name: string;
    country: string;
    city?: string;
    sector: string;
    organizationType: string;
    bio: string;
  };
}) => {
  const { data } = await axios.patch(`/users/update`, dataToSend);
  return data;
};

const fetchSessionInformations = async () => {
  const { data } = await axios.get(`/users/sessions`);
  return data.sessions;
};

const sendVerificationEmail = async (email: string) => {
  const { data } = await axios.post(`/users/sendverificationemail`, {
    email: email,
  });
  return data;
};

export {
  fetchUserProfile,
  updateUserProfile,
  changeEmail,
  fetchSessionInformations,
  sendVerificationEmail,
};

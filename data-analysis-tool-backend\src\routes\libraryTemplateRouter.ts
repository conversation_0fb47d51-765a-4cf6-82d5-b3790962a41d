import express from "express";
import {
  createLibraryTemplate,
  updateLibraryTemplate,
  getAllLibraryTemplates,
  getLibraryTemplateById,
  deleteLibraryTemplate,
  fetchQuestionForLibraryTemplate,
  DeleteMultipleLibraryTemplate
} from "../controllers/libraryTemplateController";
import { authenticate } from "../middleware/auth";

const router = express.Router();

router.post("/", authenticate, createLibraryTemplate);
router.get("/", authenticate, getAllLibraryTemplates);
router.delete(
  "/delete-multiple",
  authenticate,
  DeleteMultipleLibraryTemplate as unknown as express.RequestHandler
);
router.patch("/:id", authenticate, updateLibraryTemplate);
router.get("/:id", authenticate, getLibraryTemplateById);
router.delete("/:id", authenticate, deleteLibraryTemplate);
router.get(
  "/form/:id",
  authenticate,
  fetchQuestionForLibraryTemplate as unknown as express.RequestHandler
);


export default router;

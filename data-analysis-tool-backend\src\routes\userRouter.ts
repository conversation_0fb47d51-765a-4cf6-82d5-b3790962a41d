import express, { RequestHand<PERSON> } from "express";

import {
  signup,
  login,
  updateProfile,
  changePassword,
  forgetPassword,
  resetPassword,
  verifyEmail,
  findAllUsers,
  findUserById,
  logout,
  userProfile,
  sendVerificationEmail,
  getUserSession,
  checkPasswordResetToken,
  logoutFromAllDevice,
  changeEmail,
  getUserSessions,
  checkUserByEmail,
} from "../controllers/userController";
import { authenticate } from "../middleware/auth";

const router = express.Router();

// User registration
router.post("/signup", signup as unknown as RequestHandler);
// User login
router.post("/login", login as unknown as RequestHandler);
router.patch("/update", authenticate, updateProfile);
router.get("/resetpasswordtoken/:token", checkPasswordResetToken);
router.post("/changepassword", authenticate, changePassword);
router.post("/forgetpassword", forgetPassword);
router.post("/resetPassword", resetPassword);
router.get("/verifyemail/:token", verifyEmail);
router.get("/", authenticate, findAllUsers);
router.get("/profile", authenticate, userProfile);
router.post("/logout", authenticate, logout);
router.post("/logoutall", authenticate, logoutFromAllDevice);
router.post("/check-email", checkUserByEmail as unknown as RequestHandler);
router.post(
  "/sendverificationemail",
  sendVerificationEmail as unknown as RequestHandler
);
router.get("/me", authenticate, getUserSession as unknown as RequestHandler);
router.patch(
  "/change-email",
  authenticate,
  changeEmail as unknown as RequestHandler
);
router.get(
  "/sessions",
  authenticate,
  getUserSessions as unknown as RequestHandler
);
router.get("/:id", findUserById);

export default router;

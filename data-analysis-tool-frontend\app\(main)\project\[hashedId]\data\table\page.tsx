"use client";

import { generateColumns, Submission } from "./columns";
import { Input } from "@/components/ui/input";
import React, { JSX, useState, useRef, useEffect } from "react";
import { BiSolidEdit } from "react-icons/bi";
import { RiDeleteBin6Fill } from "react-icons/ri";
import { FaChevronDown, FaChevronUp } from "react-icons/fa";
import { LuFullscreen } from "react-icons/lu";
import { EditSubmissionModal } from "@/components/modals/EditSubmissionModal";
import { GeneralTable } from "@/components/tables/GeneralTable";
import ViewSubmissionDetail from "@/components/modals/ViewSubmissionDetail";
import { fetchQuestions } from "@/lib/api/form-builder";
import { Question } from "@/types/formBuilder";

import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

import {
  Table as ReactTable,
  RowSelectionState,
  VisibilityState,
} from "@tanstack/react-table";
import { Button } from "@/components/ui/button";
import { ConfirmationModal } from "@/components/modals/ConfirmationModal";
import axios from "@/lib/axios";
import { useParams } from "next/navigation";
import { decode } from "@/lib/encodeDecode";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import {
  deleteFormSubmission,
  deleteMultipleFormSubmissions,
} from "@/lib/api/submission";
import { useDispatch } from "react-redux";
import { showNotification } from "@/redux/slices/notificationSlice";

type ConfirmationModalProps = {
  title: string;
  description: string | JSX.Element;
  confirmButtonText: string;
  confirmButtonClass: string;
  onConfirm: () => void;
} | null;

const fetchSubmissions = async (projectId: number) => {
  const { data } = await axios.get(`/form-submissions/${projectId}`);
  return data.data.formSubmissions;
};

const STORAGE_KEY = "data-table-column-visibility";

const Page = () => {
  const { hashedId } = useParams();
  const hashedIdString = hashedId as string;

  const projectId = Number(decode(hashedIdString));
  const dispatch = useDispatch();
  const queryClient = useQueryClient();

  const {
    data: submissions = [],
    isLoading,
    refetch,
  } = useQuery<Submission[]>({
    queryKey: ["formSubmissions", projectId],
    queryFn: () => fetchSubmissions(projectId!),
    enabled: projectId !== null,
    refetchInterval: 1000,
    staleTime: 0, // Always consider data stale to ensure fresh fetches
    gcTime: 0, // Don't cache the data (updated from cacheTime)
  });

  // Fetch ALL questions for the project (including conditional questions)
  const { data: allQuestions = [], isLoading: isLoadingQuestions } = useQuery<
    Question[]
  >({
    queryKey: ["allQuestions", projectId],
    queryFn: () => fetchQuestions({ projectId: projectId! }),
    enabled: projectId !== null,
    staleTime: 5 * 60 * 1000, // Cache for 5 minutes since questions don't change often
  });

  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showConfirmationModal, setShowConfirmationModal] = useState(false);
  const [globalFilter, setGlobalFilter] = useState("");
  const [isAnyRowSelected, setIsAnyRowSelected] = useState(false);

  const [confirmationModalContent, setConfirmationModalContent] =
    React.useState<ConfirmationModalProps>(null);
  const [showEditModal, setShowEditModal] = useState(false);
  const [columnVisibility, setColumnVisibility] =
    React.useState<VisibilityState>({});
  const [tableInstance, setTableInstance] =
    React.useState<ReactTable<Submission> | null>(null);

  const [selectedRows, setSelectedRows] = React.useState<RowSelectionState>({});

  const [showViewModal, setShowViewModal] = useState(false);
  const [selectedSubmission, setSelectedSubmission] =
    useState<Submission | null>(null);

  const handleViewSubmission = (submission: Submission) => {
    setSelectedSubmission(submission);
    setShowViewModal(true);
  };

  const columns =
    submissions.length > 0 && allQuestions.length > 0
      ? generateColumns(
          handleViewSubmission,
          submissions[0],
          hashedIdString,
          allQuestions
        )
      : [];

  const [isDropdownOpen, setIsDropdownOpen] = React.useState(false);
  const [statusDropdownOpen, setStatusDropdownOpen] = useState(false);

  const statusDropdownRef = useRef<HTMLDivElement>(null);

  // Ref to the container that will go fullscreen
  const contentRef = useRef<HTMLDivElement>(null);

  // Callback to monitor row selection changes
  const handleRowSelectionChange = (rowSelection: Record<string, boolean>) => {
    setIsAnyRowSelected(Object.keys(rowSelection).length > 0);
    setSelectedRows(rowSelection);

    // Get the selected submission when row selection changes
    if (Object.keys(rowSelection).length === 1) {
      // If exactly one row is selected
      const selectedIndex = Number(Object.keys(rowSelection)[0]);
      setSelectedSubmission(submissions[selectedIndex]);
    } else if (Object.keys(rowSelection).length === 0) {
      // If no rows are selected
      setSelectedSubmission(null);
    }
  };

  const handleFullscreenToggle = () => {
    setIsFullscreen((prev) => !prev);
  };

  // Handle status dropdown toggling
  const handleStatusDropdownToggle = () => {
    setStatusDropdownOpen((prev) => !prev);
  };

  const handleEditConfirm = () => {
    // You would typically save the updated submission to the backend here
    console.log("Edit confirmed");

    // Refresh the data using invalidateQueries
    queryClient.invalidateQueries({ queryKey: ["formSubmissions", projectId] });

    // Clear row selections
    setSelectedRows({});
    setIsAnyRowSelected(false);

    // Close the modal
    setShowEditModal(false);
    setSelectedSubmission(null);
  };

  // Mutation for deleting a single submission
  const deleteMutation = useMutation({
    mutationFn: (submissionId: number) =>
      deleteFormSubmission(submissionId, projectId),
    onSuccess: () => {
      dispatch(
        showNotification({
          message: "Submission deleted successfully",
          type: "success",
        })
      );

      queryClient.invalidateQueries({
        queryKey: ["formSubmissions", projectId],
      });

      // Clear selections
      setSelectedRows({});
      setIsAnyRowSelected(false);
      setSelectedSubmission(null);
      setShowConfirmationModal(false);
    },
    onError: (error) => {
      console.error("Error deleting submission:", error);
      dispatch(
        showNotification({
          message: "Failed to delete submission",
          type: "error",
        })
      );
      setShowConfirmationModal(false);
    },
  });

  // Mutation for deleting multiple submissions
  const deleteMultipleMutation = useMutation({
    mutationFn: (submissionIds: number[]) =>
      deleteMultipleFormSubmissions(submissionIds, projectId),
    onSuccess: (_, variables) => {
      dispatch(
        showNotification({
          message: `${variables.length} submissions deleted successfully`,
          type: "success",
        })
      );

      // Refresh the data
      queryClient.invalidateQueries({
        queryKey: ["formSubmissions", projectId],
      });

      // Clear selections
      setSelectedRows({});
      setIsAnyRowSelected(false);
      setSelectedSubmission(null);
      setShowConfirmationModal(false);
    },
    onError: (error) => {
      console.error("Error deleting multiple submissions:", error);
      dispatch(
        showNotification({
          message: "Failed to delete submissions",
          type: "error",
        })
      );
      setShowConfirmationModal(false);
    },
  });

  const handleDeleteClick = () => {
    // Get the IDs of the selected submissions
    const selectedSubmissionIds = Object.keys(selectedRows)
      .map((key) => {
        const index = parseInt(key);
        return submissions[index]?.id || 0;
      })
      .filter((id) => id > 0);

    setConfirmationModalContent({
      title: "Confirm Deletion",
      description: (
        <>
          <p>
            Are you sure you want to delete{" "}
            {selectedSubmissionIds.length > 1
              ? `these ${selectedSubmissionIds.length} submissions`
              : "this submission"}
            ? It is not possible to recover deleted submissions.
          </p>
        </>
      ),
      confirmButtonText: "Delete",
      confirmButtonClass: "bg-red-500 hover:bg-red-600 cursor-pointer",
      onConfirm: () => {
        if (selectedSubmissionIds.length === 1) {
          // Delete a single submission using the mutation
          deleteMutation.mutate(selectedSubmissionIds[0]);
        } else if (selectedSubmissionIds.length > 1) {
          // Delete multiple submissions using the mutation
          deleteMultipleMutation.mutate(selectedSubmissionIds);
        }
      },
    });
    setShowConfirmationModal(true);
  };

  const handleEditClick = () => {
    const selectedIds = Object.keys(selectedRows);

    if (selectedIds.length === 0) {
      dispatch(
        showNotification({
          message: "No submission selected for editing",
          type: "error",
        })
      );
      return;
    }

    // Collect all selected submissions
    const selectedSubmissionsArray = selectedIds
      .map((id) => {
        const index = Number(id);
        return submissions[index];
      })
      .filter(Boolean);

    // If exactly one row is selected, use that submission
    if (selectedIds.length === 1) {
      setSelectedSubmission(selectedSubmissionsArray[0]);
      setShowEditModal(true);
      return;
    }

    // For multiple selections, we'll set the first one as the default
    if (selectedIds.length > 1) {
      setSelectedSubmission(selectedSubmissionsArray[0]);
      setShowEditModal(true);
    }
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        statusDropdownRef.current &&
        !statusDropdownRef.current.contains(event.target as Node)
      ) {
        setStatusDropdownOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  useEffect(() => {
    try {
      const saved = localStorage.getItem(STORAGE_KEY);
      if (saved) {
        const parsed = JSON.parse(saved);
        if (parsed && typeof parsed === "object" && !Array.isArray(parsed)) {
          setColumnVisibility(parsed);
        } else {
          console.warn("Invalid format in localstorage for column visibility");
        }
      }
    } catch (error) {
      console.error("Error loading column visibility:", error);
    }
  }, []);

  useEffect(() => {
    if (Object.keys(columnVisibility).length > 0) {
      try {
        localStorage.setItem(STORAGE_KEY, JSON.stringify(columnVisibility));
      } catch (error) {
        console.error("Error saving column visibility:", error);
      }
    }
  }, [columnVisibility]);

  // Effect to properly update the table when selectedRows changes
  useEffect(() => {
    if (tableInstance && Object.keys(selectedRows).length === 0) {
      // If selectedRows is empty and we have a table instance,
      // explicitly reset the table's row selection
      tableInstance.resetRowSelection();
    }
  }, [selectedRows, tableInstance]);

  const handleColumnVisibilityChange = (newState: VisibilityState) => {
    setColumnVisibility(newState);
  };

  const handleTableInit = (table: ReactTable<Submission>) => {
    setTableInstance(table);

    if (Object.keys(columnVisibility).length > 0) {
      table.setColumnVisibility(columnVisibility);
    }
  };

  return (
    <div
      ref={contentRef}
      className={`flex flex-col gap-4 transition-all duration-300 ${
        isFullscreen
          ? "fixed inset-0 bg-neutral-100 w-screen h-screen z-50 p-6 overflow-auto"
          : ""
      }`}
    >
      <div className="flex flex-col desktop:flex-row justify-between gap-8 items-center py-4">
        <div className="flex items-center gap-4">
          <Input
            placeholder="Search all columns..."
            value={globalFilter}
            onChange={(e) => setGlobalFilter(e.target.value)}
          />

          {tableInstance && (
            <DropdownMenu
              open={isDropdownOpen}
              onOpenChange={(open) => setIsDropdownOpen(open)}
            >
              <DropdownMenuTrigger asChild>
                <Button
                  variant="outline"
                  className="flex items-center gap-2 cursor-pointer"
                >
                  Show/Hide Columns
                  {isDropdownOpen ? (
                    <FaChevronUp className="w-3 h-3" />
                  ) : (
                    <FaChevronDown className="w-3 h-3" />
                  )}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                align="start"
                className="bg-neutral-100 border border-neutral-200 shadow-md"
              >
                {tableInstance
                  .getAllColumns()
                  .filter((column) => column.getCanHide())
                  .map((column) => (
                    <DropdownMenuCheckboxItem
                      key={column.id}
                      className="capitalize cursor-pointer hover:bg-neutral-200"
                      checked={columnVisibility[column.id] ?? true}
                      onCheckedChange={(value) =>
                        setColumnVisibility((prev) => ({
                          ...prev,
                          [column.id]: value,
                        }))
                      }
                    >
                      {column.id}
                    </DropdownMenuCheckboxItem>
                  ))}
              </DropdownMenuContent>
            </DropdownMenu>
          )}
        </div>
        <div
          ref={statusDropdownRef}
          className="flex relative items-center gap-4 text-neutral-800"
        >
          <button onClick={handleFullscreenToggle} className="btn-primary">
            <LuFullscreen className="w-5 h-5" />
            {isFullscreen ? "Exit Fullscreen" : "Fullscreen"}
          </button>

          <button
            className={` bg-primary-500 font-medium rounded-lg shadow-md text-neutral-100 flex items-center justify-center gap-2 px-4 py-2  active:scale-95 transition-all duration-300 ${
              isAnyRowSelected
                ? "hover:bg-primary-600  cursor-pointer"
                : "opacity-50"
            }`}
            onClick={isAnyRowSelected ? handleStatusDropdownToggle : undefined}
          >
            Status
            {statusDropdownOpen ? (
              <FaChevronUp className="w-3 h-3" />
            ) : (
              <FaChevronDown className="w-3 h-3" />
            )}
          </button>
          {statusDropdownOpen && (
            <div className="absolute left-30 top-10 mt-2 w-64 bg-neutral-100 border border-gray-200 shadow-md rounded-md p-2 z-40">
              <div className="flex flex-col  gap-2">
                <div className="hover:bg-neutral-200 cursor-pointer p-2 text-neutral-800 rounded-sm">
                  Set on: Approved
                </div>
                <div className="hover:bg-neutral-200 cursor-pointer p-2 text-neutral-800 rounded-sm">
                  Set on: Not Approved
                </div>
                <div className="hover:bg-neutral-200 cursor-pointer p-2 text-neutral-800 rounded-sm">
                  Set on: On Hold
                </div>
              </div>
            </div>
          )}
          <button
            className={` bg-primary-500 font-medium rounded-lg shadow-md text-neutral-100 flex items-center justify-center gap-2 px-4 py-2  active:scale-95 transition-all duration-300 ${
              isAnyRowSelected
                ? "hover:bg-primary-600  cursor-pointer"
                : "opacity-50"
            }`}
            onClick={isAnyRowSelected ? handleEditClick : undefined}
          >
            <BiSolidEdit className="h-4 w-4" />
            Edit
          </button>
          <button
            className={` bg-primary-500 font-medium rounded-lg shadow-md text-neutral-100 flex items-center justify-center gap-2 px-4 py-2  active:scale-95 transition-all duration-300 ${
              isAnyRowSelected
                ? "hover:bg-primary-600  cursor-pointer"
                : "opacity-50"
            }`}
            onClick={isAnyRowSelected ? handleDeleteClick : undefined}
          >
            <RiDeleteBin6Fill className="h-4 w-4" />
            Delete
          </button>
        </div>
      </div>

      {isLoading || isLoadingQuestions ? (
        <div className="flex justify-center items-center py-12">
          <div className="text-muted-foreground">Loading data...</div>
        </div>
      ) : (
        <GeneralTable
          columns={columns}
          data={submissions}
          globalFilter={globalFilter}
          setGlobalFilter={setGlobalFilter}
          onTableInit={handleTableInit}
          columnVisibility={columnVisibility}
          setColumnVisibility={handleColumnVisibilityChange}
          onRowSelectionChange={handleRowSelectionChange}
          rowSelection={selectedRows}
        />
      )}

      {/* Conditional rendering of the EditSubmissionModal */}
      {showEditModal && selectedSubmission && (
        <EditSubmissionModal
          showModal={showEditModal}
          projectId={projectId}
          onClose={() => {
            setShowEditModal(false);
            setSelectedSubmission(null);
            // Clear row selections when closing the modal
            setSelectedRows({});
            setIsAnyRowSelected(false);
          }}
          onConfirm={handleEditConfirm}
          submission={selectedSubmission}
          isMultipleSelection={Object.keys(selectedRows).length > 1}
          selectedSubmissions={
            Object.keys(selectedRows).length > 1
              ? Object.keys(selectedRows)
                  .map((id) => submissions[Number(id)])
                  .filter(Boolean)
              : []
          }
        />
      )}

      {confirmationModalContent && (
        <ConfirmationModal
          showModal={showConfirmationModal}
          onClose={() => setShowConfirmationModal(false)}
          onConfirm={confirmationModalContent.onConfirm}
          title={confirmationModalContent.title}
          description={confirmationModalContent.description}
          confirmButtonText={confirmationModalContent.confirmButtonText}
          confirmButtonClass={confirmationModalContent.confirmButtonClass}
        />
      )}

      {selectedSubmission && (
        <ViewSubmissionDetail
          isOpen={showViewModal}
          onClose={() => setShowViewModal(false)}
          submission={selectedSubmission}
        />
      )}
    </div>
  );
};

export default Page;

import { Request, Response } from "express";
import { ApiResponse } from "../utils/ApiResponse";
import {
  questionSchema,
  questionPositionsSchema,
} from "../validators/questionValidators";
import { Status } from "@prisma/client";
import QuestionRepository from "../repositories/questionRepository";
import questionRepository from "../repositories/questionRepository";
import projectRepository from "../repositories/projectRepository";
import { prisma } from "../utils/prisma";
import { getProjectById } from "./projectController";
import { ZodUnknownDef } from "zod";

// export const createProject

interface userRequest extends Request {
  user?: {
    id: number;
  };
}

export const getAllQuestion = async (req: userRequest, res: Response) => {
  try {
    if (!req.user || !req.user.id) {
      return res.status(404).json({
        success: false,
        message: "user not found",
      });
    }
    const userId = Number(req.user.id);

    // Get projectId from either path parameter or query parameter
    const projectId = Number(req.params.projectId || req.query.projectId);

    if (!projectId) {
      return res.status(400).json({
        success: false,
        message: "Project ID is required",
      });
    }

    // const projectOwner = await questionRepository.isPorjectOwner(
    //   userId,
    //   projectId
    // );

    // if (!projectOwner) {
    //   return res.status(400).json({
    //     success: false,
    //     message: "only project owner can view the questions",
    //   });
    // }

    const questions = await questionRepository.findAll(projectId);

    return res
      .status(200)
      .json({ message: "Successfully fetched questions.", questions });
  } catch (error: unknown) {
    return res.status(500).json({
      success: false,
      message: "error fetching questions",
      error: error instanceof Error ? error.message : "unexpected error",
    });
  }
};

export const createQuestion = async (
  req: userRequest,
  res: Response
): Promise<void> => {
  try {
    const userId = req.user!.id;
    const projectId = Number(req.params.projectId);

    const result = questionSchema.safeParse(req.body);
    if (!result.success) {
      res.status(400).json({
        success: false,
        errors: result.error.flatten().fieldErrors,
      });
      return;
    }

    // Extract options and conditions
    const { questionOptions, conditions, ...questionData } = result.data;

    // Create the question with options and conditions
    const question = await questionRepository.create({
      ...questionData,
      projectId,
      questionOptions: questionOptions
        ? questionOptions.map((option) => ({
            label: option.label,
            sublabel: option.sublabel ?? "",
            code: option.code,
            nextQuestionId: option.nextQuestionId,
          }))
        : undefined,
      conditions: conditions
        ? conditions.map((cond) => ({
            operator: cond.operator,
            value: cond.value,
          }))
        : undefined,
    });

    res
      .status(200)
      .json(new ApiResponse(200, { question }, "question created success"));
    return;
  } catch (error: unknown) {
    res.status(500).json({
      success: false,
      message: "error creating qustion",
      error: error instanceof Error ? error.message : "unexpected error",
    });
    return;
  }
};

export const updateQuestion = async (
  req: userRequest,
  res: Response
): Promise<void> => {
  try {
    const id = Number(req.params.id);
    const userId = req.user?.id;

    if (!userId || isNaN(id)) {
      res.status(400).json({
        success: false,
        message: "Invalid request",
      });
      return;
    }
    const existingQuestion = await QuestionRepository.findById(id);

    if (!existingQuestion) {
      res.status(404).json({
        success: false,
        message: "Question not found",
      });
      return;
    }

    if (existingQuestion.projectId === undefined) {
      res.status(500).json({
        success: false,
        message: "Question has no associated project",
      });
      return;
    }

    const isOwner = await QuestionRepository.isPorjectOwner(
      userId,
      existingQuestion.projectId
    );
    if (!isOwner) {
      res.status(403).json({
        success: false,
        message: "You are not the project owner",
      });
      return;
    }

    // Parse the request body without requiring all fields
    const parseResult = questionSchema.safeParse({
      ...req.body,
      // Add dummy values for any required fields that aren't in the update
      // These won't actually be used for the update, just to pass validation
      label: req.body.label || existingQuestion.label,
      inputType: req.body.inputType || existingQuestion.inputType,
      position: req.body.position || existingQuestion.position,
    });

    console.log("parsed request", parseResult);

    if (!parseResult.success) {
      res.status(400).json({
        success: false,
        message: "Validation error",
        errors: parseResult.error.errors,
      });
      return;
    }

    // Only include fields that were actually in the request body
    // Only include fields that were actually in the request body
    const validatedData: Record<string, any> = {};
    const fields = [
      "label",
      "inputType",
      "isRequired",
      "hint",
      "placeholder",
      "position",
      "questionOptions",
      "conditions",
    ] as const;

    for (const field of fields) {
      if (req.body[field] !== undefined) {
        // Rename questionOptions to options
        if (field === "questionOptions") {
          validatedData.options = (parseResult.data as any)[field];
        } else {
          validatedData[field] = (parseResult.data as any)[field];
        }
      }
    }

    // Check if options are required for select input types
    if (
      validatedData.inputType === "selectone" ||
      validatedData.inputType === "selectmany"
    ) {
      if (
        !validatedData.options ||
        !Array.isArray(validatedData.options) ||
        validatedData.options.length === 0
      ) {
        res.status(400).json({
          success: false,
          message: "Options must be provided for select input types",
        });
        return;
      }
    }

    // Use the updated repository method to handle options and conditions
    const updatedQuestion = await QuestionRepository.updateById(
      id,
      validatedData
    );

    res
      .status(200)
      .json(
        new ApiResponse(
          200,
          { question: updatedQuestion },
          "question updated success"
        )
      );
  } catch (error: unknown) {
    res.status(500).json({
      success: false,
      message: "error updating question",
      error: error instanceof Error ? error.message : "unexpected error",
    });
    return;
  }
};

export const deleteQuestion = async (req: userRequest, res: Response) => {
  try {
    const userId = req.user?.id;
    const id = Number(req.params.id);

    if (!userId || isNaN(id)) {
      return res.status(400).json({
        message: "Invalid request: User ID or Question ID is missing",
        success: false,
      });
    }
    const currentQuestion = await questionRepository.findById(id);
    if (!currentQuestion) {
      return res.status(404).json({
        message: "Question not found",
        success: false,
      });
    }
    const isProjectOwner = await questionRepository.isPorjectOwner(
      userId,
      currentQuestion.projectId
    );

    if (!isProjectOwner) {
      return res.status(403).json({
        message: "Current user cannot delete question from this project",
        succcess: false,
      });
    }

    await questionRepository.deleteQuestion(id);

    return res.status(200).json({
      message: "Successfully deleted question",
      success: true,
    });
  } catch (error: unknown) {
    return res.status(500).json({
      message: error instanceof Error ? error.message : "unexpected error",
      success: false,
    });
  }
};

export const duplicateQuestion = async (req: userRequest, res: Response) => {
  try {
    const userId = req.user?.id;
    const id = Number(req.params.id);

    if (!userId || isNaN(id)) {
      return res.status(400).json({
        message: "Invalid request: User ID or Question ID is missing",
        success: false,
      });
    }
    const currentQuestion = await questionRepository.findById(id);
    if (!currentQuestion) {
      return res.status(404).json({
        message: "Question not found",
        success: false,
      });
    }
    const isProjectOwner = await questionRepository.isPorjectOwner(
      userId,
      currentQuestion.projectId
    );

    if (!isProjectOwner) {
      return res.status(403).json({
        message: "Current user cannot delete question from this project",
        succcess: false,
      });
    }

    const duplicatedQuestion = await questionRepository.duplicateQuestion(
      id,
      currentQuestion.projectId
    );

    return res.status(200).json({
      message: "Successfully duplicated the question.",
      success: true,
      duplicatedQuestion,
    });
  } catch (error: unknown) {
    return res.status(500).json({
      message: error instanceof Error ? error.message : "unexpected error",
      success: false,
    });
  }
};

export const updateQuestionPositions = async (
  req: userRequest,
  res: Response
): Promise<void> => {
  try {
    console.log("updateQuestionPositions called with:", {
      body: req.body,
      query: req.query,
      userId: req.user?.id,
    });

    const userId = req.user?.id;
    const projectId = Number(req.query.projectId);

    if (!userId || isNaN(projectId)) {
      console.log("Invalid request - missing userId or projectId:", {
        userId,
        projectId,
      });
      res.status(400).json({
        success: false,
        message: "Invalid request - missing userId or projectId",
      });
      return;
    }

    // Validate request body structure using the schema
    const parseResult = questionPositionsSchema.safeParse(req.body);
    if (!parseResult.success) {
      res.status(400).json({
        success: false,
        message: "Validation error",
        errors: parseResult.error.flatten().fieldErrors,
      });
      return;
    }

    const { questionPositions } = parseResult.data;

    // Check if user owns the project
    const isOwner = await questionRepository.isPorjectOwner(userId, projectId);
    if (!isOwner) {
      res.status(403).json({
        success: false,
        message: "You are not the project owner",
      });
      return;
    }

    // Update positions in bulk
    const updatedQuestions = await questionRepository.updateMultiplePositions(
      questionPositions
    );

    res.status(200).json({
      success: true,
      message: "Question positions updated successfully",
      data: { questions: updatedQuestions },
    });
  } catch (error: unknown) {
    console.error("Error in updateQuestionPositions:", error);
    res.status(500).json({
      success: false,
      message: "Error updating question positions",
      error: error instanceof Error ? error.message : "Unexpected error",
    });
  }
};

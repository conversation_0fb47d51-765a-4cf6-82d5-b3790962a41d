{"name": "data-analysis-tool-backend", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"build": "tsc", "dev": "nodemon", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"@fast-csv/format": "^5.0.2", "@prisma/client": "^6.6.0", "@types/dotenv": "^6.1.1", "@types/hashids": "^2.0.1", "axios": "^1.9.0", "bcryptjs": "^3.0.2", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "crypto-js": "^4.2.0", "dotenv": "^16.5.0", "exceljs": "^4.4.0", "express": "^5.1.0", "hashids": "^2.3.0", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "nodemailer": "^6.10.1", "recharts": "^2.15.3", "useragent": "^2.3.0", "uuid": "^11.1.0", "xlsx": "^0.18.5", "zod": "^3.24.2"}, "devDependencies": {"@types/axios": "^0.9.36", "@types/cookie-parser": "^1.4.8", "@types/cors": "^2.8.17", "@types/crypto-js": "^4.2.2", "@types/dotenv": "^6.1.1", "@types/exceljs": "^0.5.3", "@types/express": "^5.0.1", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^22.14.1", "@types/nodemailer": "^6.4.17", "@types/useragent": "^2.3.4", "jest": "^29.7.0", "nodemon": "^3.1.10", "prisma": "^6.6.0", "ts-jest": "^29.3.2", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}
"use client";

import React from "react";
import Modal from "./Modal";
import { LuCircleDot } from "react-icons/lu";

const DeleteModal = ({
  showModal,
  onClose,
  onConfirm,
}: {
  showModal: boolean;
  onClose: () => void;
  onConfirm: () => void;
}) => {
  return (
    <>
      <Modal isOpen={showModal} onClose={onClose} className="p-6  rounded-md">
        <h2 className="text-lg font-semibold text-neutral-700">
          Confirm Deletion
        </h2>
        <p className="text-sm text-neutral-700 mt-2">
          Are you sure you want to delete this item? This action cannot be
          undone.
        </p>
        <div className="mt-6 space-y-4">
          <div className="flex items-center gap-3">
            <LuCircleDot size={6} className="text-red-500 flex-shrink-0" />
            <span className="text-sm text-neutral-700">
              All data gathered for these projects will be deleted
            </span>
          </div>

          <div className="flex items-center gap-3">
            <LuCircleDot size={6} className="text-red-600 flex-shrink-0" />
            <span className="text-sm text-neutral-700">
              Forms associated with these projects will be deleted
            </span>
          </div>

          <div className="flex items-center gap-3">
            <LuCircleDot size={6} className="text-red-600 flex-shrink-0" />
            <span className="text-sm text-neutral-700">
              I understand that if I delete these projects I will not be able to
              recover them
            </span>
          </div>
        </div>
        <div className="flex justify-end gap-4 mt-6">
          <button className="btn-primary" onClick={onClose}>
            Cancel
          </button>
          <button
            className="bg-red-500 font-medium rounded-md shadow-md text-neutral-100 flex items-center justify-center gap-2 px-4 py-2 hover:bg-red-600 cursor-pointer active:scale-95 transition-all duration-300 disabled:pointer-events-none disabled:bg-red-400"
            onClick={onConfirm}
          >
            Delete
          </button>
        </div>
      </Modal>
    </>
  );
};

export default DeleteModal;

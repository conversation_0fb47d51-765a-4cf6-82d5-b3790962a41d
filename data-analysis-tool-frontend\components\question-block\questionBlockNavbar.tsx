"use client";

import { FilePen } from "lucide-react";
import { Navbar } from "@/components/general/SecondaryNavbar";

const QuestionBlockNavbar = () => {

  const projectNavbarItems = [
    {
      label: "Form Builder",
      icon: <FilePen size={16} />,
      route: `/library/question-block/form-builder`,
    },
  ];

  return <Navbar items={projectNavbarItems} />;
};

export { QuestionBlockNavbar };

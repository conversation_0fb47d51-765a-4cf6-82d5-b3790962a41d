import { ProjectUser } from "@prisma/client";
import { prisma } from "../utils/prisma";
import projectRepository from "./projectRepository";

class ProjectUserRepository {
  async create(
    userId: number,
    projectId: number,
    permission: Record<string, boolean>
  ): Promise<ProjectUser> {
    return await prisma.projectUser.create({
      data: {
        userId,
        projectId,
        permission,
      },
    });
  }

  async deleteUserFromProject(
    userId: number,
    projectId: number
  ): Promise<ProjectUser> {
    const projectUser = await prisma.projectUser.findFirst({
      where: {
        userId: userId,
        projectId: projectId,
      },
    });

    if (projectUser) {
      return await prisma.projectUser.delete({
        where: {
          id: projectUser.id, // Use 'id' for deletion
        },
      });
    }

    throw new Error("project not found");
  }

  async updateUserPermission(
    userId: number,
    projectId: number,
    permission: Record<string, boolean>
  ): Promise<ProjectUser> {
    const existing = await prisma.projectUser.findFirst({
      where: { userId, projectId },
    });

    if (!existing) throw new Error("ProjectUser not found");

    return await prisma.projectUser.update({
      where: { id: existing.id },
      data: { permission },
    });
  }

  async findUserProject(
    userId: number,
    projectId: number
  ): Promise<ProjectUser | null> {
    return await prisma.projectUser.findFirst({
      where: {
        userId,
        projectId,
      },
    });
  }

  async allUsers(projectId: number): Promise<ProjectUser[]> {
    return await prisma.projectUser.findMany({
      where: {
        projectId,
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    });
  }
}

export default new ProjectUserRepository();

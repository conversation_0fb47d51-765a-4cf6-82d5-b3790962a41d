import {
  PrismaClient,
  LibraryQuestion,
  LibraryQuestionOption,
  LibraryQuestionCondition,
  InputType,
  Operator,
  Prisma,
} from "@prisma/client";
import { prisma } from "../utils/prisma";

class LibraryQuestionRepository {
  async findById(id: number): Promise<
    | (LibraryQuestion & {
        questionOptions: LibraryQuestionOption[];
        questionConditions: LibraryQuestionCondition[];
      })
    | null
  > {
    return await prisma.libraryQuestion.findUnique({
      where: { id },
      include: {
        questionOptions: true,
        questionConditions: true,
      },
    });
  }

  async findByLibraryTemplateId(libraryTemplateId: number) {
    return await prisma.libraryQuestion.findMany({
      where: { libraryTemplateId },
      include: {
        questionOptions: true,
        questionConditions: true,
      },
      orderBy: {
        position: "asc",
      },
    });
  }

  async isLibraryTemplateOwner(
    userId: number,
    libraryTemplateId: number
  ): Promise<boolean> {
    const count = await prisma.libraryTemplate.count({
      where: {
        id: libraryTemplateId,
        userId,
      },
    });
    return count > 0;
  }

  async create(data: {
    label: string;
    inputType: InputType;
    hint?: string;
    placeholder?: string;
    isRequired: boolean;
    position: number;
    libraryTemplateId: number;
    questionOptions?: {
      label: string;
      code: string;
      nextLibraryQuestionId?: number | null;
    }[];
    conditions?: { operator: Operator; value: string }[];
  }): Promise<
    LibraryQuestion & {
      questionOptions: LibraryQuestionOption[];
      questionConditions: LibraryQuestionCondition[];
    }
  > {
    const { questionOptions, conditions, ...questionData } = data;

    return await prisma.$transaction(async (tx) => {
      // Create the question
      const question = await tx.libraryQuestion.create({
        // data: questionData,
        data: {
          ...questionData,
          hint: questionData.hint ?? "", // fallback to empty string
          placeholder: questionData.placeholder ?? "", // fallback to empty string
        },
      });

      // Create options if provided (for select type questions)
      if (questionOptions && questionOptions.length > 0) {
        await tx.libraryQuestionOption.createMany({
          data: questionOptions.map((option) => ({
            label: option.label,
            code: option.code,
            libraryQuestionId: question.id,
            nextLibraryQuestionId: option.nextLibraryQuestionId || null,
          })),
        });
      }

      // Create conditions if provided
      if (conditions && conditions.length > 0) {
        await tx.libraryQuestionCondition.createMany({
          data: conditions.map((condition) => ({
            operator: condition.operator,
            value: condition.value,
            libraryQuestionId: question.id,
          })),
        });
      }

      // Return the created question with options and conditions
      return (await tx.libraryQuestion.findUnique({
        where: { id: question.id },
        include: {
          questionOptions: true,
          questionConditions: true,
        },
      })) as LibraryQuestion & {
        questionOptions: LibraryQuestionOption[];
        questionConditions: LibraryQuestionCondition[];
      };
    });
  }

  async duplicateQuestion(id: number, templateId: number) {
    const question = await prisma.libraryQuestion.findUnique({
      where: { id },
    });
    if (!question) {
      return null;
    }

    const duplicatedQuestion = await prisma.libraryQuestion.create({
      data: {
        ...question,
        id: undefined,
        libraryTemplateId: templateId,
      },
      include: {
        questionOptions: true,
        questionConditions: true,
      },
    });
    return duplicatedQuestion;
  }

  async update(
    id: number,
    data: {
      label?: string;
      inputType?: InputType;
      hint?: string;
      placeholder?: string;
      isRequired?: boolean;
      position?: number;
      questionOptions?: {
        id?: number;
        label: string;
        code: string;
        nextLibraryQuestionId?: number | null;
      }[];
      conditions?: {
        id?: number;
        operator: Operator;
        value: string;
      }[];
    }
  ): Promise<
    LibraryQuestion & {
      questionOptions: LibraryQuestionOption[];
      questionConditions: LibraryQuestionCondition[];
    }
  > {
    const { questionOptions, conditions, ...questionData } = data;

    return await prisma.$transaction(async (tx) => {
      // Update the question
      const updatedQuestion = await tx.libraryQuestion.update({
        where: { id },
        data: questionData,
      });

      // Handle options if provided
      if (questionOptions) {
        const existingOptions = await tx.libraryQuestionOption.findMany({
          where: { libraryQuestionId: id },
        });

        // Identify options to add/update/delete
        const existingIds = existingOptions.map((o) => o.id);
        const updatedIds = questionOptions
          .filter((o) => o.id)
          .map((o) => o.id as number);
        const idsToDelete = existingIds.filter(
          (id) => !updatedIds.includes(id)
        );

        // Delete removed options
        if (idsToDelete.length > 0) {
          await tx.libraryQuestionOption.deleteMany({
            where: { id: { in: idsToDelete } },
          });
        }

        // Add new options and update existing ones
        for (const option of questionOptions) {
          if (option.id) {
            // Update existing option
            await tx.libraryQuestionOption.update({
              where: { id: option.id },
              data: {
                label: option.label,
                nextLibraryQuestionId: option.nextLibraryQuestionId || null,
              },
            });
          } else {
            // Create new option
            await tx.libraryQuestionOption.create({
              data: {
                label: option.label,
                code: option.code,
                libraryQuestionId: id,
                nextLibraryQuestionId: option.nextLibraryQuestionId || null,
              },
            });
          }
        }
      }

      // Handle conditions if provided
      if (conditions) {
        const existingConditions = await tx.libraryQuestionCondition.findMany({
          where: { libraryQuestionId: id },
        });

        // Identify conditions to add/update/delete
        const existingIds = existingConditions.map((c) => c.id);
        const updatedIds = conditions
          .filter((c) => c.id)
          .map((c) => c.id as number);
        const idsToDelete = existingIds.filter(
          (id) => !updatedIds.includes(id)
        );

        // Delete removed conditions
        if (idsToDelete.length > 0) {
          await tx.libraryQuestionCondition.deleteMany({
            where: { id: { in: idsToDelete } },
          });
        }

        // Add new conditions and update existing ones
        for (const condition of conditions) {
          if (condition.id) {
            // Check if condition exists and belongs to this question
            const existingCondition =
              await tx.libraryQuestionCondition.findUnique({
                where: {
                  id: condition.id,
                },
              });

            // Only update if condition exists and belongs to this question
            if (
              existingCondition &&
              existingCondition.libraryQuestionId === id
            ) {
              // Update existing condition
              await tx.libraryQuestionCondition.update({
                where: { id: condition.id },
                data: {
                  operator: condition.operator,
                  value: condition.value,
                },
              });
            } else {
              // Create new condition if ID doesn't exist
              await tx.libraryQuestionCondition.create({
                data: {
                  operator: condition.operator,
                  value: condition.value,
                  libraryQuestionId: id,
                },
              });
            }
          } else {
            // Create new condition
            await tx.libraryQuestionCondition.create({
              data: {
                operator: condition.operator,
                value: condition.value,
                libraryQuestionId: id,
              },
            });
          }
        }
      }

      // Return the updated question with options and conditions
      return (await tx.libraryQuestion.findUnique({
        where: { id },
        include: {
          questionOptions: true,
          questionConditions: true,
        },
      })) as LibraryQuestion & {
        questionOptions: LibraryQuestionOption[];
        questionConditions: LibraryQuestionCondition[];
      };
    });
  }

  async delete(id: number): Promise<void> {
    await prisma.libraryQuestion.delete({
      where: { id },
    });
  }
}

export default new LibraryQuestionRepository();

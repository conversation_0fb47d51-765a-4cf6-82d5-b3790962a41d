// src/app.ts
import express, { Request, Response, NextFunction } from "express";
import cors from "cors";
import userRoutes from "./routes/userRouter";
import projectRoutes from "./routes/projectRouter";
import questionRoutes from "./routes/questionRouter";
import libraryTemplateRoutes from "./routes/libraryTemplateRouter";
import libraryQuestionRoutes from "./routes/libraryQuestionRouter";
import ProjectUserRouter from "./routes/projectUserRouter";
import questionGroupRouter from "./routes/questionGroupRouter";
import answerRouter from "./routes/answerRouter";
import formSubmissionRouter from "./routes/formSubmissionRouter";
import cookieParser from "cookie-parser";
import reportRouter from "./routes/reportRouter";
import exportRouter from "./routes/exportFileRouter";
import tableQuestionRouter from "./routes/tableQuestionRouter";
import helmet from "helmet";
import libraryQuestionBlockRouter from "./routes/libraryQuestionBlockQuestionRouter";
import libraryQuestionBlockGroupRouter from "./routes/libraryQuestionBlockQuestionGroupRouter";
const app = express();

// Middleware
app.use(
  cors({
    origin: process.env.CLIENT_URL,
    credentials: true,
  })
);
app.use(helmet());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(cookieParser());

// Routes
app.use("/api/users", userRoutes);
app.use("/api/projects", projectRoutes);
app.use("/api/questions", questionRoutes);
app.use("/api/libraries", libraryTemplateRoutes);
app.use("/api/template-questions", libraryQuestionRoutes);
app.use("/api/project-users", ProjectUserRouter);
app.use("/api/question-groups", questionGroupRouter);
app.use("/api/answers", answerRouter);
app.use("/api/form-submissions", formSubmissionRouter);
app.use("/api", reportRouter);
app.use("/api/export", exportRouter);
app.use("/api/question-blocks", libraryQuestionBlockRouter);
app.use("/api/question-block-group", libraryQuestionBlockGroupRouter);
app.use("/api/table-questions", tableQuestionRouter);

// Error handler
app.use((err: Error, req: Request, res: Response, next: NextFunction) => {
  res.status(500).json({
    success: false,
    message: "Something went wrong!",
    error: process.env.NODE_ENV === "development" ? err.message : undefined,
  });
});

export default app;

import express from "express";
import {
  getAllProjectUser,
  createProjectUser,
  updateProjectUser,
  deletProjectUser,
  copyProjectUsers,
} from "../controllers/projectUserController";
import { authenticate } from "../middleware/auth";
import { checkPermission } from "../middleware/checkPermission";

const router = express.Router();

router.post(
  "/",
  authenticate,
  // checkPermission("manageProject") as unknown as express.RequestHandler,
  createProjectUser as express.RequestHandler
);
router.get(
  "/:projectId",
  authenticate,
  getAllProjectUser as unknown as express.RequestHandler
);
router.patch("/", authenticate, updateProjectUser as express.RequestHandler);
router.delete(
  "/",
  authenticate,
  checkPermission("manageProject") as unknown as express.RequestHandler,

  deletProjectUser as unknown as express.RequestHandler
);
router.post(
  "/copy-users",
  authenticate,
  copyProjectUsers as unknown as express.RequestHandler
);
// router.post(
//   "/get-users",
//   authenticate,
//   getAllProjectUser as unknown as express.RequestHandler
// );

export default router;

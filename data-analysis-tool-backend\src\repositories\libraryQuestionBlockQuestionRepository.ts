import {
  PrismaClient,
  LibraryQuestionBlockQuestion,
  LibraryQuestionBlockQuestionOption,
  LibraryQuestionBlockQuestionCondition,
  InputType,
  Operator,
} from "@prisma/client";
import { prisma } from "../utils/prisma";

class LibraryQuestionRepository {
  async findById(id: number): Promise<
    | (LibraryQuestionBlockQuestion & {
        questionOptions: LibraryQuestionBlockQuestionOption[];
        questionConditions: LibraryQuestionBlockQuestionCondition[];
      })
    | null
  > {
    return await prisma.libraryQuestionBlockQuestion.findUnique({
      where: { id },
      include: {
        questionOptions: true,
        questionConditions: true,
      },
    });
  }

  async create(
    data: {
      label: string;
      inputType: InputType;
      hint?: string;
      placeholder?: string;
      isRequired?: boolean;
      position: number;
      libraryQuestionBlockQuestionGroupId: number;
      questionOptions?: {
        label: string;
        code: string;
        nextQuestionId?: number | null;
      }[];
      conditions?: {
        operator: Operator;
        value: string;
      }[];
    },
    userId: number
  ): Promise<LibraryQuestionBlockQuestion> {
    const {
      label,
      inputType,
      hint,
      placeholder,
      isRequired,
      position,
      libraryQuestionBlockQuestionGroupId,
      questionOptions,
      conditions,
    } = data;

    return await prisma.$transaction(async (tx) => {
      // Create the question
      const question = await tx.libraryQuestionBlockQuestion.create({
        data: {
          label,
          inputType,
          hint: hint ?? "", // fallback to empty string
          placeholder: placeholder ?? "",
          isRequired,
          position,
          userId,
          libraryQuestionBlockQuestionGroupId,
        },
        include: {
          questionOptions: true,
          questionConditions: true,
        },
      });

      // Create options if provided
      if (questionOptions && questionOptions.length > 0) {
        await tx.libraryQuestionBlockQuestionOption.createMany({
          data: questionOptions.map((option) => ({
            label: option.label,
            code: option.code,
            libraryQuestionBlockQuestionId: question.id,
            // nextQuestionId: option.nextQuestionId || null,
          })),
        });
      }

      // Create conditions if provided
      if (conditions && conditions.length > 0) {
        await tx.libraryQuestionBlockQuestionCondition.createMany({
          data: conditions.map((condition) => ({
            operator: condition.operator,
            value: condition.value,
            libraryQuestionBlockQuestionId: question.id,
          })),
        });
      }

      // Return the created question with options and conditions
      return question;
    });
  }

  async updateById(
    id: number,
    updateData: {
      label?: string;
      inputType?: InputType;
      hint?: string;
      placeholder?: string;
      isRequired?: boolean;
      position?: number;
      options?: {
        id?: number;
        label: string;
        code: string;
        nextQuestionId?: number | null;
      }[];
      conditions?: {
        id?: number;
        operator: Operator;
        value: string;
      }[];
    }
  ) {
    const { options, conditions, ...rest } = updateData;

    const questionData = {
      label: rest.label,
      inputType: rest.inputType,
      hint: rest.hint,
      placeholder: rest.placeholder,
      isRequired: rest.isRequired,
      position: rest.position,
    };

    return await prisma.$transaction(async (tx) => {
      // Update the question basic data
      const updatedQuestion = await tx.libraryQuestionBlockQuestion.update({
        where: { id },
        data: questionData,
      });

      // Handle options if provided
      if (options) {
        console.log("handleing option ");
        const existingOptions =
          await tx.libraryQuestionBlockQuestionOption.findMany({
            where: { libraryQuestionBlockQuestionId: id },
          });

        // Identify options to add/update/delete
        const existingIds = existingOptions.map((o) => o.id);

        const updatedIds = options
          .filter((o) => o.id)
          .map((o) => o.id as number);
        const idsToDelete = existingIds.filter(
          (id) => !updatedIds.includes(id)
        );

        // Delete removed options
        if (idsToDelete.length > 0) {
          await tx.libraryQuestionBlockQuestionOption.deleteMany({
            where: { id: { in: idsToDelete } },
          });
        }

        // Add new options and update existing ones
        for (const option of options) {
          if (option.id) {
            // Update existing option
            await tx.libraryQuestionBlockQuestionOption.update({
              where: { id: option.id },
              data: {
                label: option.label,
                code: option.code,
                nextLibraryQuestionId: option.nextQuestionId || null,
              },
            });
          } else {
            // Create new option
            await tx.libraryQuestionBlockQuestionOption.create({
              data: {
                label: option.label,
                code: option.code,
                libraryQuestionBlockQuestionId: id,
                nextLibraryQuestionId: option.nextQuestionId || null,
              },
            });
          }
        }
      }

      // Handle conditions if provided
      if (conditions) {
        const existingConditions =
          await tx.libraryQuestionBlockQuestionCondition.findMany({
            where: { libraryQuestionBlockQuestionId: id },
          });

        // Identify conditions to add/update/delete
        const existingIds = existingConditions.map((c) => c.id);
        const updatedIds = conditions
          .filter((c) => c.id)
          .map((c) => c.id as number);
        const idsToDelete = existingIds.filter(
          (id) => !updatedIds.includes(id)
        );

        // Delete removed conditions
        if (idsToDelete.length > 0) {
          await tx.libraryQuestionBlockQuestionCondition.deleteMany({
            where: { id: { in: idsToDelete } },
          });
        }

        // Add new conditions and update existing ones
        for (const condition of conditions) {
          if (condition.id) {
            // Check if condition exists and belongs to this question
            const existingCondition =
              await tx.libraryQuestionBlockQuestionCondition.findUnique({
                where: {
                  id: condition.id,
                },
              });

            // Only update if condition exists and belongs to this question
            if (
              existingCondition &&
              existingCondition.libraryQuestionBlockQuestionId === id
            ) {
              // Update existing condition
              await tx.libraryQuestionBlockQuestionCondition.update({
                where: { id: condition.id },
                data: {
                  operator: condition.operator,
                  value: condition.value,
                },
              });
            } else {
              // Create new condition if ID doesn't exist
              await tx.libraryQuestionBlockQuestionCondition.create({
                data: {
                  operator: condition.operator,
                  value: condition.value,
                  libraryQuestionBlockQuestionId: id,
                },
              });
            }
          } else {
            // Create new condition
            await tx.libraryQuestionBlockQuestionCondition.create({
              data: {
                operator: condition.operator,
                value: condition.value,
                libraryQuestionBlockQuestionId: id,
              },
            });
          }
        }
      }

      // Return the updated question with options and conditions
      return await tx.libraryQuestionBlockQuestion.findUnique({
        where: { id },
        include: {
          questionOptions: true,
          questionConditions: true,
        },
      });
    });
  }

  async deleteQuestion(id: number): Promise<LibraryQuestionBlockQuestion> {
    return await prisma.libraryQuestionBlockQuestion.delete({
      where: { id },
    });
  }

  async findAll(userId?: number): Promise<LibraryQuestionBlockQuestion[]> {
    return await prisma.libraryQuestionBlockQuestion.findMany({
      where: { userId },
      include: {
        questionOptions: true,
        questionConditions: true,
      },
    });
  }
}

export default new LibraryQuestionRepository();

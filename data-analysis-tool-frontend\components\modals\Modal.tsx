"use client";

import { AnimatePresence, motion, easeInOut } from "framer-motion";
import { X } from "lucide-react";
import React from "react";

const Modal = ({
  children,
  className,
  isOpen,
  onClose,
  preventOutsideClick = false,
}: {
  children: React.ReactNode;
  className?: string;
  isOpen: boolean;
  onClose: () => void;
  preventOutsideClick?: boolean;
}) => {
  // Handle backdrop click with confirmation if needed
  const handleBackdropClick = (e: React.MouseEvent) => {
    if (preventOutsideClick) {
      // Do nothing, prevent closing
      return;
    } else {
      onClose();
    }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-neutral-900/50 flex items-center justify-center z-40 p-4 overflow-y-auto"
          onClick={handleBackdropClick} // Handle backdrop click
        >
          <motion.div
            initial={{ scale: 0.6, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.6, opacity: 0 }}
            transition={{ duration: 0.3, ease: easeInOut }}
            className={`relative section flex flex-col max-w-4xl max-h-[90vh] overflow-y-auto ${className}`}
            onClick={(e) => e.stopPropagation()} // Prevent clicks inside the modal from closing it
          >
            <X
              onClick={onClose}
              className="absolute self-end cursor-pointer text-neutral-700 hover:text-neutral-900 active:scale-90 transition-all duration-300"
            />
            {children}
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default Modal;

import Hashids from "hashids";

const salt = process.env.SALT || "rushan-salt";

const hashids = new Hashids(salt, 12);

const encode = (id: number) => {
  return hashids.encode(id);
};

const decode = (hash: string) => {
  const decodedNumberLike = hashids.decode(hash)[0];
  const decoded =
    typeof decodedNumberLike === "bigint"
      ? decodedNumberLike < Number.MAX_SAFE_INTEGER
        ? Number(decodedNumberLike)
        : null
      : typeof decodedNumberLike === "number"
      ? decodedNumberLike
      : null;
  return decoded;
};

export { encode, decode };

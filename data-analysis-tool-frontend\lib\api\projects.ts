import axios from "@/lib/axios";
import { Project } from "@/types";

const fetchProjectById = async ({ projectId }: { projectId: number }) => {
  const { data } = await axios.get(`/projects/${projectId}`);
  return data.project;
};

const createProjectFromTemplate = async (dataToSend: {
  templateId: number;
  name: string;
  description: string;
  sector: string;
  country: string;
}) => {
  const { data } = await axios.post(`/projects/from-template`, dataToSend);
  return data;
};

//Fetch all projects for the current user
const fetchProjects = async (): Promise<Project[]> => {
  try {
    const { data } = await axios.get(`/projects`);
    return data.projects;
  } catch (error) {
    console.error("Error fetching projects:", error);
    throw error;
  }
};

// Delete project
const deleteProject = async (projectId: number) => {
  const { data } = await axios.delete(`/projects/delete/${projectId}`);
  return data;
};

// Delete multiple projects
const deleteMultipleProjects = async (projectIds: number[]) => {
  try {
    const { data } = await axios.delete(`/projects/delete-multiple`, {
      data: { projectIds },
    });
    return data;
  } catch (error) {
    console.error("Error deleting multiple projects:", error);
    throw error;
  }
};

//Archive project
const archiveProject = async (projectId: number) => {
  try {
    const { data } = await axios.patch(`/projects/change-status/${projectId}`, {
      status: "archived",
    });
    return data;
  } catch (error) {
    console.error("Error archiving project:", error);
    throw error;
  }
};

//Deploy project
const deployProject = async (
  projectId: number,
  isUnarchive: boolean = false
) => {
  try {
    const { data } = await axios.patch(`/projects/change-status/${projectId}`, {
      status: "deployed",
    });
    return data;
  } catch (error) {
    console.error("Error deploying project:", error);
    throw error;
  }
};

// Archive multiple projects
const archiveMultipleProjects = async (projectIds: number[]) => {
  try {
    const { data } = await axios.patch(`/projects/update-many-status`, {
      projectIds,
      status: "archived",
    });
    return data;
  } catch (error) {
    console.error("Error archiving multiple projects:", error);
    throw error;
  }
};

// Check if user exists by email
const checkUserExists = async (email: string) => {
  try {
    const { data } = await axios.post(`/users/check-email`, { email });
    return data;
  } catch (error: any) {
    // Format error message consistently
    const errorMessage =
      typeof error.response?.data?.message === "object"
        ? JSON.stringify(error.response?.data?.message)
        : error.response?.data?.message ||
          error.message ||
          "Failed to check user";

    throw new Error(errorMessage);
  }
};

// Add user to project by email
const addProjectUser = async ({
  projectId,
  email,
  permissions,
}: {
  projectId: number;
  email: string;
  permissions: Record<string, boolean>;
}) => {
  try {
    // First check if the user exists
    const userData = await checkUserExists(email);

    if (!userData || !userData.success) {
      throw new Error(userData?.message || "User not found");
    }

    // Now use the user ID to add them to the project
    const { data } = await axios.post(`/project-users`, {
      userId: userData.user.id,
      projectId,
      permission: permissions,
    });

    return data;
  } catch (error: any) {
    console.error("Error adding user to project:", error);
    // Format error message as a string
    const errorMessage =
      typeof error.response?.data?.message === "object"
        ? JSON.stringify(error.response?.data?.message)
        : error.response?.data?.message ||
          error.message ||
          "Failed to add user";

    throw new Error(errorMessage);
  }
};

// Fetch all users for a specific project
const fetchProjectUsers = async (projectId: number) => {
  try {
    const { data } = await axios.get(`/project-users/${projectId}`);
    return data.data.AllUser;
  } catch (error: any) {
    console.error("Error fetching project users:", error);
    const errorMessage =
      typeof error.response?.data?.message === "object"
        ? JSON.stringify(error.response?.data?.message)
        : error.response?.data?.message ||
          error.message ||
          "Failed to fetch project users";

    throw new Error(errorMessage);
  }
};

// Create answer submission
const createAnswerSubmission = async (
  answers: {
    projectId: number;
    questionId: number;
    answerType: string;
    value?: string | number | boolean;
    imageUrl?: string;
    questionOptionId?: number | number[];
    isOtherOption?: boolean;
  }[]
) => {
  try {
    const { data } = await axios.post(`/answers/multiple`, answers);
    return data;
  } catch (error) {
    console.error("Error creating answer submission:", error);
    throw error;
  }
};

// Update answer submission
const updateAnswerSubmission = async (
  projectId: number,
  submissionId: number,
  answers: {
    projectId: number;
    questionId: number;
    answerType: string;
    value?: string | number | boolean;
    imageUrl?: string;
    questionOptionId?: number | number[];
    isOtherOption?: boolean;
  }[]
) => {
  try {
    const { data } = await axios.patch(`/form-submissions/${projectId}/${submissionId}`, { answers });
    return data;
  } catch (error) {
    console.error("Error updating answer submission:", error);
    throw error;
  }
};




export {
  fetchProjectById,
  fetchProjects,
  deleteProject,
  deleteMultipleProjects,
  archiveMultipleProjects,
  createProjectFromTemplate,
  archiveProject,
  deployProject,
  addProjectUser,
  checkUserExists,
  fetchProjectUsers,
  createAnswerSubmission,
  updateAnswerSubmission,
};

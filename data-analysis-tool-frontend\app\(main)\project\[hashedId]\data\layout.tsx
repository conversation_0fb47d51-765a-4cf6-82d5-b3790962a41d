"use client";

import React from "react";
import { useParams, usePathname, useRouter } from "next/navigation";
import { Table2, BarChart2, Download } from "lucide-react";

export default function DataLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { hashedId } = useParams();
  const hashedIdString = hashedId as string;

  const navItems = [
    { label: "Data", href: `/project/${hashedIdString}/data`, icon: Table2 },
    {
      label: "Reports",
      href: `/project/${hashedIdString}/data/reports`,
      icon: BarChart2,
    },
    {
      label: "Downloads",
      href: `/project/${hashedIdString}/data/downloads`,
      icon: Download,
    },
  ];

  const pathname = usePathname();
  const router = useRouter();

  const handleNavigation = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const selectedHref = e.target.value;
    if (selectedHref !== pathname) {
      router.push(selectedHref);
    }
  };

  return (
    <div className="flex flex-col min-h-screen bg-neutral-100 p-4">
      {/* Dropdown Navigation */}
      <div className="flex justify-between mb-4">
        <h2 className="heading-text">Survey Results</h2>

        <div className="">
          <h2 className="flex flex-col text-sm font-medium text-neutral-700 mb-1">
            Navigate
          </h2>
          <select
            value={
              navItems.some((item) => item.href === pathname) ? pathname : ""
            }
            onChange={handleNavigation}
            className=" p-2 border border-neutral-300 rounded-md shadow-sm cursor-pointer"
          >
            <option value="">
              {pathname === `/project/${hashedIdString}/data`
                ? "DataTable Overview"
                : "Select"}
            </option>
            {navItems.map(({ label, href }) => (
              <option key={label} value={href}>
                {label}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Main Content */}
      <main className="p-4 bg-neutral-100 rounded-md border border-neutral-300 shadow-sm">
        <div>{children}</div>
      </main>
    </div>
  );
}

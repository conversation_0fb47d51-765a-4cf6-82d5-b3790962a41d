"use client";

import { ChevronDown } from "lucide-react";
import React, { useEffect, useRef, useState } from "react";

const Select = ({
  id,
  options,
  value,
  onChange,
}: {
  id: string;
  options: string[];
  value: string | null;
  onChange: (value: string) => void;
}) => {
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const listRef = useRef<HTMLUListElement>(null);
  const optionRefs = useRef<(HTMLLIElement | null)[]>([]);
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        containerRef.current &&
        !containerRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const handleKeyPress = (event: KeyboardEvent) => {
    if (!isOpen) return;

    const pressedKey = event.key.toLowerCase();

    if (pressedKey.match(/[a-z]/)) {
      // Find the first option that starts with pressed key
      const index = options.findIndex((option) =>
        option.toLowerCase().startsWith(pressedKey)
      );

      if (index !== -1 && optionRefs.current[index]) {
        optionRefs.current[index]?.scrollIntoView({
          behavior: "auto",
          block: "nearest",
        });
      }
    }
  };

  useEffect(() => {
    document.addEventListener("keydown", handleKeyPress);
    return () => {
      document.removeEventListener("keydown", handleKeyPress);
    };
  }, [isOpen, options]);

  return (
    <div className="relative" ref={containerRef}>
      <button
        id={id}
        type="button"
        className="px-4 py-2 flex items-center justify-between rounded-md border border-neutral-400 focus:border-primary-500 duration-300 w-full text-left cursor-pointer"
        onClick={() => {
          setIsOpen(!isOpen);
        }}
      >
        <span>{value || "Select an option"}</span>
        <ChevronDown />
      </button>
      {isOpen && (
        <ul
          className="absolute z-10 max-h-[180px] overflow-auto border border-neutral-400 rounded-md bg-neutral-100 w-full mt-1 flex flex-col"
          ref={listRef}
        >
          {options.map((option, index) => (
            <li
              key={index}
              ref={(el) => {
                optionRefs.current[index] = el;
              }}
              className="cursor-pointer bg-neutral-100 hover:bg-neutral-200 px-4 py-2"
              onClick={() => {
                onChange(option);
                setIsOpen(false);
              }}
            >
              {option}
            </li>
          ))}
        </ul>
      )}
    </div>
  );
};

export { Select };

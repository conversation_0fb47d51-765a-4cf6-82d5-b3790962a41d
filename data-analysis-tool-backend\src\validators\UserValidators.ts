import { OrganizationType, Sector } from "@prisma/client";
import { z } from "zod";

export const userSchema = z.object({
  name: z.string().min(1, "Full name not provided"),
  email: z.string().min(1, "Email not provided").email("Invalid email address"),
  password: z
    .string()
    .min(1, "Password not provided")
    .min(8, "Password must be at least 8 characters")
    .max(32, "Password must be less than 32 characters")
    .regex(/[A-Z]/, "Must contain at least one uppercase letter")
    .regex(/[a-z]/, "Must contain at least one lowercase letter")
    .regex(/[0-9]/, "Must contain at least one number")
    .regex(
      /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/,
      "Must contain at least one special character"
    ),
  country: z.string().min(1, "Country not selected"),
  sector: z.nativeEnum(Sector, {
    errorMap: () => ({ message: "Invalid sector selected" }),
  }),
  organizationType: z.nativeEnum(OrganizationType, {
    errorMap: () => ({ message: "Invalid organization type selected" }),
  }),
});

export const updateProfileSchema = z.object({
  name: z.string().min(1, "Name is required"),
  country: z.string().min(1, "Country not selected"),
  city: z.string().optional(),
  bio: z.string().optional(),
  sector: z.nativeEnum(Sector, {
    errorMap: () => ({ message: "Invalid sector selected" }),
  }),
  organizationType: z.nativeEnum(OrganizationType, {
    errorMap: () => ({ message: "Invalid organization type selected" }),
  }),
});

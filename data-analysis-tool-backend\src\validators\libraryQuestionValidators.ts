import { z } from "zod";
import { InputType, Operator } from "@prisma/client";

export const libraryQuestionOptionSchema = z.object({
  id: z.number().optional(),
  label: z.string().min(1, "Option label is required"),
  code: z.string().min(1, "Option code is required"),
  nextLibraryQuestionId: z.number().optional().nullable(),
});

export const libraryQuestionConditionSchema = z.object({
  id: z.number().optional(),
  operator: z.nativeEnum(Operator, {
    errorMap: () => ({ message: "Invalid operator selected" }),
  }),
  value: z.string().min(1, "Condition value is required"),
});

export const libraryQuestionSchema = z.object({
  label: z.string().min(1, "Question label is required"),
  inputType: z.nativeEnum(InputType, {
    errorMap: () => ({ message: "Invalid input type selected" }),
  }),
  hint: z.string().optional(),
  placeholder: z.string().optional(),
  isRequired: z.boolean(),
  position: z.number().int().positive("Position must be a positive number"),
  questionOptions: z.array(libraryQuestionOptionSchema).optional(),
  conditions: z.array(libraryQuestionConditionSchema).optional(),
});

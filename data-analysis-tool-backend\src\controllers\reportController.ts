import { Request, Response } from "express";
import { ApiResponse } from "../utils/ApiResponse";
import reportRepository from "../repositories/reportRepository";
import formSubmissionRepository from "../repositories/formSubmissionRepository";
import Hashids from "hashids";

// For easier testing, export these
export const salt = process.env.SALT || "rushan-salt";
export const hashids = new Hashids(salt, 12);

interface UserRequest extends Request {
  user?: {
    id: number;
  };
}

// Generate report from form submissions for a project
export const getProjectReport = async (
  req: UserRequest,
  res: Response
): Promise<void> => {
  try {
    const userId = req.user?.id;
    const hashedProjectId = req.params.projectId;
    const reportType = (req.query.type as string) || "default";
    const startDate = req.query.startDate
      ? new Date(req.query.startDate as string)
      : undefined;
    const endDate = req.query.endDate
      ? new Date(req.query.endDate as string)
      : undefined;

    if (!userId) {
      res.status(401).json(new ApiResponse(401, null, "Unauthorized"));
      return;
    }

    // Decode the hashed project ID
    const decoded = hashids.decode(hashedProjectId);
    const decodedProjectId =
      decoded && decoded.length > 0 ? decoded[0] : undefined;
    if (!decodedProjectId || typeof decodedProjectId !== "number") {
      res.status(400).json(new ApiResponse(400, null, "Invalid project ID"));
      return;
    }

    try {
      // Check if user has access to project
      const hasAccess = await formSubmissionRepository.isProjectAccessible(
        userId,
        decodedProjectId
      );

      if (!hasAccess) {
        res
          .status(403)
          .json(
            new ApiResponse(403, null, "You don't have access to this project")
          );
        return;
      }

      // Generate report using repository
      const report = await reportRepository.generateReport(decodedProjectId, {
        startDate,
        endDate,
        type: reportType as any,
      });

      res
        .status(200)
        .json(new ApiResponse(200, report, "Report generated successfully"));
    } catch (innerError) {
      console.error("Error in report generation:", innerError);
      res
        .status(500)
        .json(
          new ApiResponse(
            500,
            null,
            `Error generating report: ${
              innerError instanceof Error ? innerError.message : "Unknown error"
            }`
          )
        );
    }
  } catch (error) {
    console.error("Error generating report:", error);
    res
      .status(500)
      .json(
        new ApiResponse(
          500,
          null,
          `Error generating report: ${
            error instanceof Error ? error.message : "Unknown error"
          }`
        )
      );
  }
};

import { createSlice, PayloadAction } from "@reduxjs/toolkit";

const createLibraryItemSlice = createSlice({
  initialState: {
    visible: false,
    option: "",
  },
  name: "createLibraryItem",
  reducers: {
    showCreateLibraryItemModal: (state, action: PayloadAction<string>) => {
      state.visible = true;
      state.option = action.payload;
    },
    hideCreateLibraryItemModal: (state) => {
      state.visible = false;
      state.option = "";
    },
  },
});

export const { showCreateLibraryItemModal, hideCreateLibraryItemModal } =
  createLibraryItemSlice.actions;
export default createLibraryItemSlice.reducer;

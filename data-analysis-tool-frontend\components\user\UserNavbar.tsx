import { ShieldAlert, User, Database } from "lucide-react";
import React from "react";
import { Navbar } from "../general/SecondaryNavbar";

const UserNavbar = () => {
  const userNavbarItems = [
    {
      label: "Profile",
      icon: <User size={16} />,
      route: "/account/profile",
    },
    {
      label: "Security",
      icon: <ShieldAlert size={16} />,
      route: "/account/security",
    },
  ];

  return <Navbar items={userNavbarItems} />;
};

export { UserNavbar };

import express, { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import {
  createLibraryQuestion,
  getAllLibraryQuestions,
  getLibraryQuestionById,
  updateLibraryQuestion,
  deleteLibraryQuestion,
  duplicateTemplateQuestion,
} from "../controllers/libraryQuestionController";
import { authenticate } from "../middleware/auth";

const router = express.Router();

// Routes for library questions
// All routes need authentication
// Base path: /api/library/:libraryTemplateId/questions

// Create a new question for a library template
router.post("/:libraryTemplateId", authenticate, createLibraryQuestion);

router.post(
  "/duplicate/:id",
  authenticate,
  duplicateTemplateQuestion as unknown as <PERSON><PERSON><PERSON><PERSON><PERSON>
);

// Get all questions for a library template
router.get("/:libraryTemplateId", authenticate, getAllLibraryQuestions);

// Get a specific question
router.get("/:id", authenticate, getLibraryQuestionById);

// Update a specific question
router.patch("/:id", authenticate, updateLibraryQuestion);

// Delete a specific question
router.delete("/:id", authenticate, deleteLibraryQuestion);

export default router;

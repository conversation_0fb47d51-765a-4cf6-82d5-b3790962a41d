import { Request, Response } from "express";
import {
  createLibraryTemplate,
  getAllLibraryTemplates,
  updateLibraryTemplate,
  deleteLibraryTemplate,
  getLibraryTemplateById,
} from "../../controllers/libraryTemplateController";
import libraryTemplateRepository from "../../repositories/libraryTemplateRepository";

// Mock the dependencies
jest.mock("../../repositories/libraryTemplateRepository");

describe("Library Template Controller", () => {
  let mockRequest: Partial<Request & { user?: { id: number } }>;
  let mockResponse: Partial<Response>;
  let responseObject: any = {};

  beforeEach(() => {
    // Reset mocks before each test
    jest.clearAllMocks();
    jest.resetAllMocks();

    // Setup mock response
    mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockImplementation((result) => {
        responseObject = result;
        return mockResponse;
      }),
    };

    // Reset response object
    responseObject = {};

    // Setup default authenticated user
    mockRequest = {
      user: {
        id: 1,
      },
      params: {},
      query: {},
      body: {},
    };
  });

  describe("createLibraryTemplate", () => {
    beforeEach(() => {
      mockRequest.body = {
        name: "Test Template",
        description: "This is a test template",
        sector: "information_media",
        country: "United States",
      };
    });

    it("should create a library template successfully", async () => {
      const mockTemplate = {
        id: 1,
        name: "Test Template",
        description: "This is a test template",
        sector: "information_media",
        country: "United States",
        userId: 1,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      (libraryTemplateRepository.findByName as jest.Mock).mockResolvedValue(
        null
      );
      (libraryTemplateRepository.create as jest.Mock).mockResolvedValue(
        mockTemplate
      );

      await createLibraryTemplate(mockRequest as any, mockResponse as Response);

      expect(libraryTemplateRepository.findByName).toHaveBeenCalledWith(
        "Test Template",
        1
      );
      expect(libraryTemplateRepository.create).toHaveBeenCalledWith({
        name: "Test Template",
        description: "This is a test template",
        sector: "information_media",
        userId: 1,
        country: "United States",
      });
      expect(mockResponse.status).toHaveBeenCalledWith(201);
      expect(responseObject).toHaveProperty(
        "message",
        "Library template created successfully"
      );
      expect(responseObject).toHaveProperty("libraryTemplate", mockTemplate);
    });

    it("should return 400 when library template already exists", async () => {
      const existingTemplate = {
        id: 1,
        name: "Test Template",
        userId: 1,
      };

      (libraryTemplateRepository.findByName as jest.Mock).mockResolvedValue(
        existingTemplate
      );

      await createLibraryTemplate(mockRequest as any, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(responseObject).toHaveProperty("success", false);
      expect(responseObject).toHaveProperty(
        "message",
        "library template already exists"
      );
      expect(libraryTemplateRepository.create).not.toHaveBeenCalled();
    });

    it("should return 400 for invalid input", async () => {
      // Missing required fields
      mockRequest.body = { name: "Test Template" };

      await createLibraryTemplate(mockRequest as any, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(responseObject).toHaveProperty("success", false);
      expect(libraryTemplateRepository.create).not.toHaveBeenCalled();
    });

    it("should handle server errors", async () => {
      (libraryTemplateRepository.findByName as jest.Mock).mockRejectedValue(
        new Error("Database error")
      );

      await createLibraryTemplate(mockRequest as any, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(responseObject).toHaveProperty("success", false);
      expect(responseObject).toHaveProperty(
        "message",
        "error creating library template"
      );
    });
  });

  describe("getAllLibraryTemplates", () => {
    it("should get all library templates successfully", async () => {
      const mockTemplates = [
        {
          id: 1,
          name: "Template 1",
          description: "Description 1",
          userId: 1,
        },
        {
          id: 2,
          name: "Template 2",
          description: "Description 2",
          userId: 1,
        },
      ];

      (libraryTemplateRepository.findAll as jest.Mock).mockResolvedValue(
        mockTemplates
      );

      await getAllLibraryTemplates(
        mockRequest as any,
        mockResponse as Response
      );

      expect(libraryTemplateRepository.findAll).toHaveBeenCalledWith(1);
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(responseObject).toHaveProperty(
        "message",
        "Successfully fetched all library templates"
      );
      expect(responseObject).toHaveProperty("templates", mockTemplates);
    });

    it("should handle server errors", async () => {
      (libraryTemplateRepository.findAll as jest.Mock).mockRejectedValue(
        new Error("Database error")
      );

      await getAllLibraryTemplates(
        mockRequest as any,
        mockResponse as Response
      );

      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(responseObject).toHaveProperty("success", false);
      expect(responseObject).toHaveProperty(
        "message",
        "error getting library templates"
      );
    });
  });

  describe("updateLibraryTemplate", () => {
    beforeEach(() => {
      mockRequest.params = { id: "1" };
      mockRequest.body = {
        name: "Updated Template",
        description: "Updated description",
        sector: "information_media",
        country: "Canada",
      };
    });

    it("should update a library template successfully", async () => {
      const existingTemplate = {
        id: 1,
        name: "Test Template",
        description: "This is a test template",
        userId: 1,
      };

      const updatedTemplate = {
        id: 1,
        name: "Updated Template",
        description: "Updated description",
        sector: "information_media",
        country: "Canada",
      };

      (
        libraryTemplateRepository.findLibraryTemplateByIdAndUser as jest.Mock
      ).mockResolvedValue(existingTemplate);
      (libraryTemplateRepository.updateById as jest.Mock).mockResolvedValue(
        updatedTemplate
      );

      await updateLibraryTemplate(mockRequest as any, mockResponse as Response);

      expect(
        libraryTemplateRepository.findLibraryTemplateByIdAndUser
      ).toHaveBeenCalledWith(1, 1);
      expect(libraryTemplateRepository.updateById).toHaveBeenCalledWith(
        1,
        expect.objectContaining({
          name: "Updated Template",
          description: "Updated description",
        })
      );
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(responseObject.data).toHaveProperty(
        "updatedLibraryTemplate",
        updatedTemplate
      );
    });

    it("should return 404 when library template not found", async () => {
      (
        libraryTemplateRepository.findLibraryTemplateByIdAndUser as jest.Mock
      ).mockResolvedValue(null);

      await updateLibraryTemplate(mockRequest as any, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(404);
      expect(responseObject).toHaveProperty("success", false);
      expect(responseObject).toHaveProperty(
        "message",
        "no library template found"
      );
      expect(libraryTemplateRepository.updateById).not.toHaveBeenCalled();
    });

    it("should return 400 for invalid input", async () => {
      const existingTemplate = {
        id: 1,
        name: "Test Template",
        userId: 1,
      };

      (
        libraryTemplateRepository.findLibraryTemplateByIdAndUser as jest.Mock
      ).mockResolvedValue(existingTemplate);

      // Invalid input - missing required fields
      mockRequest.body = {};

      await updateLibraryTemplate(mockRequest as any, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(responseObject).toHaveProperty("success", false);
      expect(libraryTemplateRepository.updateById).not.toHaveBeenCalled();
    });

    it("should handle server errors", async () => {
      (
        libraryTemplateRepository.findLibraryTemplateByIdAndUser as jest.Mock
      ).mockRejectedValue(new Error("Database error"));

      await updateLibraryTemplate(mockRequest as any, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(responseObject).toHaveProperty("success", false);
      expect(responseObject).toHaveProperty(
        "message",
        "error updating library template"
      );
    });
  });

  describe("deleteLibraryTemplate", () => {
    beforeEach(() => {
      mockRequest.params = { id: "1" };
    });

    it("should delete a library template successfully", async () => {
      const existingTemplate = {
        id: 1,
        name: "Test Template",
        userId: 1,
      };

      (
        libraryTemplateRepository.findLibraryTemplateByIdAndUser as jest.Mock
      ).mockResolvedValue(existingTemplate);
      (
        libraryTemplateRepository.deleteLibraryTemplate as jest.Mock
      ).mockResolvedValue({ id: 1 });

      await deleteLibraryTemplate(mockRequest as any, mockResponse as Response);

      expect(
        libraryTemplateRepository.findLibraryTemplateByIdAndUser
      ).toHaveBeenCalledWith(1, 1);
      expect(
        libraryTemplateRepository.deleteLibraryTemplate
      ).toHaveBeenCalledWith(1);
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(responseObject).toHaveProperty(
        "message",
        "library template deleted successfully"
      );
    });

    it("should return 404 when library template not found", async () => {
      (
        libraryTemplateRepository.findLibraryTemplateByIdAndUser as jest.Mock
      ).mockResolvedValue(null);

      await deleteLibraryTemplate(mockRequest as any, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(404);
      expect(responseObject).toHaveProperty("success", false);
      expect(responseObject).toHaveProperty(
        "message",
        "no library template found with given id"
      );
      expect(
        libraryTemplateRepository.deleteLibraryTemplate
      ).not.toHaveBeenCalled();
    });

    it("should handle server errors", async () => {
      (
        libraryTemplateRepository.findLibraryTemplateByIdAndUser as jest.Mock
      ).mockRejectedValue(new Error("Database error"));

      await deleteLibraryTemplate(mockRequest as any, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(responseObject).toHaveProperty("success", false);
      expect(responseObject).toHaveProperty(
        "message",
        "error deleting library template"
      );
    });
  });

  describe("getLibraryTemplateById", () => {
    beforeEach(() => {
      mockRequest.params = { id: "1" };
    });

    it("should get a library template by ID successfully", async () => {
      const mockTemplate = {
        id: 1,
        name: "Test Template",
        description: "This is a test template",
        userId: 1,
      };

      (
        libraryTemplateRepository.findLibraryTemplateByIdAndUser as jest.Mock
      ).mockResolvedValue(mockTemplate);

      await getLibraryTemplateById(
        mockRequest as any,
        mockResponse as Response
      );

      expect(
        libraryTemplateRepository.findLibraryTemplateByIdAndUser
      ).toHaveBeenCalledWith(1, 1);
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(responseObject).toHaveProperty(
        "message",
        "Successfully fetched library template"
      );
      expect(responseObject).toHaveProperty("template", mockTemplate);
    });

    it("should return 404 when library template not found", async () => {
      (
        libraryTemplateRepository.findLibraryTemplateByIdAndUser as jest.Mock
      ).mockResolvedValue(null);

      await getLibraryTemplateById(
        mockRequest as any,
        mockResponse as Response
      );

      expect(mockResponse.status).toHaveBeenCalledWith(404);
      expect(responseObject).toHaveProperty("success", false);
      expect(responseObject).toHaveProperty(
        "message",
        "library template not found"
      );
    });

    it("should handle server errors", async () => {
      (
        libraryTemplateRepository.findLibraryTemplateByIdAndUser as jest.Mock
      ).mockRejectedValue(new Error("Database error"));

      await getLibraryTemplateById(
        mockRequest as any,
        mockResponse as Response
      );

      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(responseObject).toHaveProperty("success", false);
      expect(responseObject).toHaveProperty(
        "message",
        "error getting library template by id"
      );
    });
  });
});

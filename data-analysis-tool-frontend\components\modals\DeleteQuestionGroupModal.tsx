"use client";

import React from "react";
import { X } from "lucide-react";

interface DeleteQuestionGroupModalProps {
  showModal: boolean;
  setShowModal: (show: boolean) => void;
  onConfirmDelete: () => void;
  onConfirmDeleteWithQuestions: () => void;
  isDeleting: boolean;
}

const DeleteQuestionGroupModal = ({
  showModal,
  setShowModal,
  onConfirmDelete,
  onConfirmDeleteWithQuestions,
  isDeleting,
}: DeleteQuestionGroupModalProps) => {
  if (!showModal) return null;

  return (
    <div className="fixed inset-0 bg-neutral-900/50 flex items-center justify-center z-40">
      <div className="bg-white rounded-lg shadow-lg w-full max-w-md">
        <div className="flex justify-between items-center p-4 border-b">
          <h2 className="text-xl font-semibold">Delete Question Group</h2>
          <button
            onClick={() => setShowModal(false)}
            className="text-gray-500 hover:text-gray-700"
            disabled={isDeleting}
          >
            <X size={20} />
          </button>
        </div>

        <div className="p-4">
          <p className="mb-4">
            How would you like to delete this question group?
          </p>

          <div className="space-y-4">
            <button
              onClick={onConfirmDelete}
              className="w-full px-4 py-2 bg-amber-500 text-white rounded-md hover:bg-amber-600"
              disabled={isDeleting}
            >
              {isDeleting ? "Deleting..." : "Delete Group Only (Keep Questions)"}
            </button>
            <p className="text-sm text-gray-600 mb-4">
              This will remove the group but keep all questions. Questions will be available to add to other groups.
            </p>

            <button
              onClick={onConfirmDeleteWithQuestions}
              className="w-full px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600"
              disabled={isDeleting}
            >
              {isDeleting ? "Deleting..." : "Delete Group and All Questions"}
            </button>
            <p className="text-sm text-gray-600 mb-4">
              This will permanently delete the group and all questions inside it.
            </p>

            <button
              onClick={() => setShowModal(false)}
              className="w-full px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
              disabled={isDeleting}
            >
              Cancel
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export { DeleteQuestionGroupModal };

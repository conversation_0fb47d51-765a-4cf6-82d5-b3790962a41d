import { Question } from "@/types/formBuilder";
import React, { useEffect } from "react";
import Modal from "./Modal";
import { ContextType } from "@/types";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { showNotification } from "@/redux/slices/notificationSlice";
import { useDispatch } from "react-redux";
import { LoadingOverlay } from "../general/LoadingOverlay";
import { TableQuestionBuilder } from "../form-builder/TableQuestionBuilder";
import { fetchTableStructure, TableQuestion } from "@/lib/api/table";
import axios from "@/lib/axios";

const EditTableQuestionModal = ({
  showModal,
  setShowModal,
  contextType,
  question,
  contextId,
}: {
  showModal: boolean;
  setShowModal: React.Dispatch<React.SetStateAction<boolean>>;
  contextType: ContextType;
  question: Question;
  contextId: number;
}) => {
  const queryClient = useQueryClient();
  const dispatch = useDispatch();

  // Fetch the table structure (columns and rows) and cell values
  const {
    data: rawTableResponse,
    isLoading,
    error,
  } = useQuery<{ question: TableQuestion; cellValues?: Record<string, any> }>({
    queryKey: ["tableQuestion", question.id],
    queryFn: async () => {
      try {
        console.log("Fetching table structure for question ID:", question.id);

        // Use the table-questions endpoint directly to get both structure and cell values
        const response = await axios.get(`/table-questions/${question.id}`);
        console.log("Full API response:", response.data);

        if (response.data && response.data.success && response.data.data) {
          const { question: tableQuestion, cellValues } = response.data.data;
          console.log("Extracted table question:", tableQuestion);
          console.log("Extracted cell values:", cellValues);

          return {
            question: tableQuestion,
            cellValues: cellValues || {},
          };
        } else {
          throw new Error("Invalid response structure");
        }
      } catch (err) {
        console.error("Error fetching table data:", err);
        throw err;
      }
    },
    enabled: showModal && question.id > 0 && question.inputType === "table",
  });

  // Process the table data to match the expected format for TableQuestionBuilder
  const tableData = React.useMemo(() => {
    if (!rawTableResponse?.question) return null;

    const rawTableData = rawTableResponse.question;
    const cellValues = rawTableResponse.cellValues || {};

    console.log("Processing raw table data:", rawTableData);
    console.log("Processing cell values:", cellValues);
    console.log("Cell values keys:", Object.keys(cellValues));
    console.log("Table rows:", rawTableData.tableRows);

    // Create a copy of the raw table data and ensure it matches the expected interface
    const processedData = {
      id: rawTableData.id,
      label: rawTableData.label,
      tableColumns: rawTableData.tableColumns.map((col) => ({
        id: col.id,
        columnName: col.columnName,
        parentColumnId: col.parentColumnId,
        childColumns:
          col.childColumns?.map((child) => ({
            id: child.id,
            columnName: child.columnName,
            parentColumnId: child.parentColumnId || col.id, // Ensure parentColumnId is set for child columns
          })) || [],
      })),
      tableRows: rawTableData.tableRows.map((row) => ({
        id: row.id,
        rowsName: row.rowsName,
        // Add default values from cell values if they exist
        defaultValues: Object.entries(cellValues)
          .filter(([key, value]) => {
            const [columnId, rowId] = key.split("_");
            return (
              parseInt(rowId) === row.id &&
              value.value &&
              value.value.trim() !== ""
            );
          })
          .map(([key, value]) => {
            const [columnId] = key.split("_");
            return {
              columnId: parseInt(columnId),
              value: value.value || "",
              code: value.code || value.value || "",
            };
          }),
      })),
      // Also include cell values for the TableQuestionBuilder
      cellValues: cellValues,
    };

    console.log("Processed table data:", processedData);
    return processedData;
  }, [rawTableResponse]);

  // Log when the modal is shown or hidden
  useEffect(() => {
    console.log("EditTableQuestionModal showModal changed:", showModal);
    if (showModal) {
      console.log("Modal opened for question:", question);
    }
  }, [showModal, question]);

  const queryKey =
    contextType === "project"
      ? ["questions"]
      : contextType === "template"
      ? ["templateQuestions"]
      : ["questionBlockQuestions"];

  const handleTableUpdated = async (tableId: number) => {
    console.log("Table updated with ID:", tableId);

    try {
      // Invalidate all relevant queries to ensure fresh data is loaded everywhere
      await queryClient.invalidateQueries({
        queryKey,
        exact: false,
      });

      await queryClient.invalidateQueries({
        queryKey: ["tableQuestion", question.id],
        exact: true,
      });

      // Also invalidate any cached table structure queries that TableInput might use
      await queryClient.invalidateQueries({
        queryKey: ["tableStructure"],
        exact: false,
      });

      // Invalidate any queries that might be caching table data
      await queryClient.invalidateQueries({
        predicate: (query) => {
          const queryKey = query.queryKey;
          return (
            Array.isArray(queryKey) &&
            (queryKey.includes("tableQuestion") ||
              queryKey.includes("tableStructure") ||
              queryKey.includes("formQuestions") ||
              (queryKey.includes(question.id) &&
                typeof question.id === "number"))
          );
        },
      });

      // Force refetch of the current table data
      await queryClient.refetchQueries({
        queryKey: ["tableQuestion", question.id],
      });

      console.log(
        "Successfully invalidated and refetched all table-related queries"
      );

      dispatch(
        showNotification({
          message: "Successfully updated table question",
          type: "success",
        })
      );

      // Add a small delay before closing the modal to ensure state is properly updated
      setTimeout(() => {
        console.log("Closing modal after table update");
        setShowModal(false);
      }, 200); // Increased delay to allow for async operations
    } catch (error) {
      console.error("Error updating queries after table update:", error);

      dispatch(
        showNotification({
          message:
            "Table updated but there was an issue refreshing the data. Please refresh the page.",
          type: "warning",
        })
      );

      // Still close the modal after a delay
      setTimeout(() => {
        setShowModal(false);
      }, 100);
    }
  };

  if (error) {
    console.error("Error fetching table data:", error);
    dispatch(
      showNotification({
        message: "Failed to load table data",
        type: "error",
      })
    );
  }

  // Reference to the TableQuestionBuilder component
  const tableBuilderRef = React.useRef<HTMLDivElement>(null);

  // Function to trigger the submit event on the TableQuestionBuilder
  const handleSaveClick = () => {
    console.log("Save button clicked");

    // Try multiple approaches to find the table builder element
    let tableBuilder = null;

    // Approach 1: Direct reference through ref
    if (tableBuilderRef.current) {
      console.log("Using tableBuilderRef.current:", tableBuilderRef.current);
      tableBuilder = tableBuilderRef.current;
    }

    // Approach 2: Find by class name in the document
    if (!tableBuilder) {
      const allTableBuilders = document.querySelectorAll(
        ".table-question-builder"
      );
      console.log("Found table builders by class:", allTableBuilders.length);

      if (allTableBuilders.length > 0) {
        tableBuilder = allTableBuilders[0];
        console.log("Using first table builder found by class:", tableBuilder);
      }
    }

    // Approach 3: Find by class name in the container
    if (!tableBuilder && tableBuilderRef.current) {
      const containerTableBuilder = tableBuilderRef.current.querySelector(
        ".table-question-builder"
      );
      if (containerTableBuilder) {
        tableBuilder = containerTableBuilder;
        console.log("Using table builder found in container:", tableBuilder);
      }
    }

    if (tableBuilder) {
      console.log("Dispatching submitTable event to:", tableBuilder);

      // Create and dispatch the event
      const submitEvent = new CustomEvent("submitTable", {
        bubbles: true, // Allow event to bubble up the DOM tree
        cancelable: true, // Allow event to be canceled
        detail: { timestamp: Date.now() }, // Add some detail to help with debugging
      });

      tableBuilder.dispatchEvent(submitEvent);
      console.log("Event dispatched successfully");
    } else {
      console.error(
        "Could not find any table builder element to dispatch event to"
      );

      // As a last resort, try to find any element with a similar class
      const anyElement = document.querySelector("[class*='table']");
      if (anyElement) {
        console.log("Found element with 'table' in class:", anyElement);
        console.log("Attempting to dispatch event to this element");

        const submitEvent = new CustomEvent("submitTable", {
          bubbles: true,
          cancelable: true,
          detail: { timestamp: Date.now(), isLastResort: true },
        });

        anyElement.dispatchEvent(submitEvent);
      }
    }
  };

  // Function to handle confirmation before closing
  const handleClose = () => {
    // Show a confirmation dialog if there are unsaved changes
    if (
      window.confirm(
        "Are you sure you want to close? Any unsaved changes will be lost."
      )
    ) {
      setShowModal(false);
    }
  };

  return (
    <Modal
      isOpen={showModal}
      onClose={handleClose}
      className="w-11/12 tablet:w-4/5 desktop:w-3/5"
      preventOutsideClick={true}
    >
      {isLoading && <LoadingOverlay />}

      <div className="space-y-4">
        <h1 className="heading-text capitalize mb-4">Edit Table Question</h1>

        {tableData ? (
          <div
            ref={tableBuilderRef}
            className="table-question-builder-container"
          >
            <TableQuestionBuilder
              projectId={contextId}
              isInModal={true}
              isEditMode={true}
              existingTableData={tableData as any}
              onTableCreated={handleTableUpdated}
            />

            {/* Add Save button outside the TableQuestionBuilder */}
            <div className="flex items-center justify-end space-x-4 mt-6">
              <button
                type="button"
                onClick={() => setShowModal(false)}
                className="btn-outline"
              >
                Cancel
              </button>
              <button
                type="button"
                onClick={handleSaveClick}
                className="btn-primary"
              >
                Save Changes
              </button>
            </div>
          </div>
        ) : !isLoading ? (
          <div className="p-4 text-center">
            <p className="text-red-500">
              Could not load table data. Please try again.
            </p>
          </div>
        ) : null}
      </div>
    </Modal>
  );
};

export { EditTableQuestionModal };

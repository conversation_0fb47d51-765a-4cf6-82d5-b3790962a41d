"use client";

import React, { useState } from "react";
import Modal from "./Modal";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/redux/store";
import { hideCreateLibraryItemModal } from "@/redux/slices/createLibraryItemSlice";
import { CreateLibraryTemplate } from "../library/CreateLibraryTemplate";

const CreateLibraryItemModal = () => {
  const { visible, option } = useSelector(
    (state: RootState) => state.createLibraryItem
  );

  const dispatch = useDispatch();
  // without this closing animation won't work
  const [isClosing, setIsClosing] = useState(false);
  const handleClose = () => {
    setIsClosing(true);
    setTimeout(() => {
      dispatch(hideCreateLibraryItemModal());
    }, 300);
    // match the time with animation duration
  };

  const content = () => {
    switch (option) {
      case "question-block":
        return <div>Question Block</div>;
      case "template":
        return <CreateLibraryTemplate handleClose={handleClose} />;
      case "upload":
        return <div>Upload</div>;
      case "collection":
        return <div>Collection</div>;
      default:
        return null;
    }
  };

  return (
    <Modal
      isOpen={visible && !isClosing}
      onClose={handleClose}
      className="w-3/5"
    >
      {content()}
    </Modal>
  );
};

export { CreateLibraryItemModal };

import { Request, Response } from "express";
import tableQuestionRepository from "../repositories/tableQuestionRepository";
import {
  createTableQuestionSchema,
  updateTableQuestionSchema,
  saveCellValuesSchema,
  addRowSchema,
  dynamicTableSubmissionSchema,
} from "../validators/tableQuestionValidator";

/**
 * Interface for authenticated requests
 * Extends the standard Request interface with a user property
 */
interface userRequest extends Request {
  user?: {
    id: number;
  };
}

/**
 * TableQuestionController
 *
 * Handles all HTTP requests related to table questions.
 * Responsible for:
 * - Validating input data
 * - Calling appropriate repository methods
 * - Formatting and returning responses
 * - Error handling
 */

/**
 * Create a new table question
 *
 * @param req - Request object containing table question data
 * @param res - Response object
 * @returns JSON response with created table question or error
 */
export const createTableQuestion = async (req: userRequest, res: Response) => {
  try {
    // User authentication is handled by the middleware
    const result = createTableQuestionSchema.safeParse(req.body);

    if (!result.success) {
      console.error("Validation error:", result.error.flatten().fieldErrors);
      return res.status(400).json({
        success: false,
        message: "Invalid input data",
        errors: result.error.flatten().fieldErrors,
      });
    }

    // Check if user has access to the project
    // This would typically use your existing project access check logic

    // Note: Most validation is now handled by the Zod schema in createTableQuestionSchema
    // The schema validates:
    // 1. Required fields (label, projectId)
    // 2. Column and row existence
    // 3. Parent-child relationships
    // 4. Maximum of 2 children per parent
    // 5. Maximum of 2 levels of nesting
    // 6. No circular references

    const { columns, rows } = result.data;

    const tableQuestion = await tableQuestionRepository.createTableQuestion(
      result.data
    );

    return res.status(201).json({
      success: true,
      message: "Table question created successfully",
      data: tableQuestion,
    });
  } catch (error: any) {
    // Determine appropriate status code based on error
    let statusCode = 500;
    if (
      error.message.includes("required") ||
      error.message.includes("valid") ||
      error.message.includes("must have")
    ) {
      statusCode = 400;
    }

    return res.status(statusCode).json({
      success: false,
      message: error.message || "Error creating table question",
      error: error.stack,
    });
  }
};

/**
 * Get a table question by ID
 *
 * @param req - Request object containing question ID
 * @param res - Response object
 * @returns JSON response with table question and cell values or error
 */
export const getTableQuestion = async (req: userRequest, res: Response) => {
  try {
    // User authentication is handled by the middleware
    const questionId = parseInt(req.params.id);

    if (isNaN(questionId)) {
      return res.status(400).json({
        success: false,
        message: "Invalid question ID",
      });
    }

    const tableQuestion = await tableQuestionRepository.getTableQuestionById(
      questionId
    );

    if (!tableQuestion) {
      return res.status(404).json({
        success: false,
        message: "Table question not found",
      });
    }

    // Check if user has access to the project
    // This would typically use your existing project access check logic

    // Get cell values - now returns a formatted map directly
    const cellValuesMap = await tableQuestionRepository.getCellValues(
      questionId
    );

    return res.status(200).json({
      success: true,
      data: {
        question: tableQuestion,
        cellValues: cellValuesMap,
      },
    });
  } catch (error: any) {
    return res.status(500).json({
      success: false,
      message: "Error retrieving table question",
      error: error.message,
    });
  }
};

/**
 * Save cell values for a table question
 *
 * @param req - Request object containing cell values data
 * @param res - Response object
 * @returns JSON response with success message or error
 */
export const saveCellValues = async (req: userRequest, res: Response) => {
  try {
    // User authentication is handled by the middleware
    const result = saveCellValuesSchema.safeParse(req.body);

    if (!result.success) {
      return res.status(400).json({
        success: false,
        message: "Invalid input data",
        errors: result.error.flatten().fieldErrors,
      });
    }

    const { questionId, cellValues } = result.data;

    // Verify the question exists
    const question = await tableQuestionRepository.getTableQuestionById(
      questionId
    );

    if (!question) {
      return res.status(404).json({
        success: false,
        message: "Table question not found",
      });
    }

    // Check if user has access to the project
    // This would typically use your existing project access check logic

    // Save cell values with the updated repository method signature
    await tableQuestionRepository.saveCellValues({
      questionId,
      cellValues,
    });

    return res.status(200).json({
      success: true,
      message: "Cell values saved successfully",
    });
  } catch (error: any) {
    return res.status(500).json({
      success: false,
      message: "Error saving cell values",
      error: error.message,
    });
  }
};

/**
 * Update an existing table question
 *
 * @param req - Request object containing updated table question data
 * @param res - Response object
 * @returns JSON response with updated table question or error
 */
export const updateTableQuestion = async (req: userRequest, res: Response) => {
  try {
    // User authentication is handled by the middleware
    const questionId = parseInt(req.params.id);

    if (isNaN(questionId)) {
      return res.status(400).json({
        success: false,
        message: "Invalid question ID",
      });
    }

    // Validate the request body
    const result = updateTableQuestionSchema.safeParse(req.body);

    if (!result.success) {
      console.error("Validation error:", result.error.flatten().fieldErrors);
      return res.status(400).json({
        success: false,
        message: "Invalid input data",
        errors: result.error.flatten().fieldErrors,
      });
    }

    // Get the existing table question
    const existingQuestion = await tableQuestionRepository.getTableQuestionById(
      questionId
    );

    if (!existingQuestion) {
      return res.status(404).json({
        success: false,
        message: "Table question not found",
      });
    }

    // Check if user has access to the project
    // This would typically use your existing project access check logic

    // Extract validated data
    const { label, columns, rows } = result.data;

    // Check for empty column names
    const emptyColumns = columns.filter((col) => !col.columnName.trim());
    if (emptyColumns.length > 0) {
      return res.status(400).json({
        success: false,
        message: "All columns must have valid names",
      });
    }

    // Check for empty row names
    const emptyRows = rows.filter((row) => !row.rowsName.trim());
    if (emptyRows.length > 0) {
      return res.status(400).json({
        success: false,
        message: "All rows must have valid names",
      });
    }

    // Note: We don't need to manually validate parent-child relationships here
    // because the Zod schema in updateTableQuestionSchema already does this validation
    // The schema checks for:
    // 1. Parent column existence
    // 2. Maximum of 2 children per parent
    // 3. Maximum of 2 levels of nesting
    // 4. No circular references

    // Update the table question
    try {
      const updatedTableQuestion =
        await tableQuestionRepository.updateTableQuestion(questionId, {
          label,
          columns,
          rows,
          projectId: existingQuestion.projectId, // Include the project ID from the existing question
        });

      return res.status(200).json({
        success: true,
        message: "Table question updated successfully",
        data: updatedTableQuestion,
      });
    } catch (updateError: any) {
      // Determine appropriate status code based on error
      let statusCode = 500;
      if (
        updateError.message.includes("required") ||
        updateError.message.includes("valid") ||
        updateError.message.includes("must have") ||
        updateError.message.includes("not found") ||
        updateError.message.includes("Invalid")
      ) {
        statusCode = 400;
      }

      return res.status(statusCode).json({
        success: false,
        message: updateError.message || "Error updating table question",
        error: updateError.stack,
      });
    }
  } catch (error: any) {
    return res.status(500).json({
      success: false,
      message: "Unexpected error updating table question",
      error: error.message,
    });
  }
};

/**
 * Delete a table question
 *
 * @param req - Request object containing question ID
 * @param res - Response object
 * @returns JSON response with success message or error
 */
export const deleteTableQuestion = async (req: userRequest, res: Response) => {
  try {
    // User authentication is handled by the middleware
    const questionId = parseInt(req.params.id);

    if (isNaN(questionId)) {
      return res.status(400).json({
        success: false,
        message: "Invalid question ID",
      });
    }

    const question = await tableQuestionRepository.getTableQuestionById(
      questionId
    );

    if (!question) {
      return res.status(404).json({
        success: false,
        message: "Table question not found",
      });
    }

    // Check if user has access to the project
    // This would typically use your existing project access check logic

    await tableQuestionRepository.deleteTableQuestion(questionId);

    return res.status(200).json({
      success: true,
      message: "Table question deleted successfully",
    });
  } catch (error: any) {
    console.error("Error deleting table question:", error);
    return res.status(500).json({
      success: false,
      message: "Error deleting table question",
      error: error.message,
    });
  }
};

/**
 * Get all table questions for a project
 *
 * @param req - Request object containing project ID
 * @param res - Response object
 * @returns JSON response with table questions or error
 */
export const getTableQuestionsByProject = async (
  req: Request,
  res: Response
) => {
  try {
    const { projectId } = req.params;

    if (!projectId || isNaN(Number(projectId))) {
      return res.status(400).json({
        success: false,
        message: "Invalid project ID",
      });
    }

    const tables = await tableQuestionRepository.getTableQuestionsByProjectId(
      Number(projectId)
    );

    return res.status(200).json({
      success: true,
      data: tables,
    });
  } catch (error: any) {
    console.error("Error getting table questions:", error);
    return res.status(500).json({
      success: false,
      message: "Failed to get table questions",
      error: error.message,
    });
  }
};

/**
 * Aliases for backward compatibility with the old /tables endpoints
 *
 * These aliases allow existing frontend code to continue working without changes.
 * They simply reference the corresponding tableQuestion methods.
 */
export const getTable = getTableQuestion;
export const createTable = createTableQuestion;
export const deleteTable = deleteTableQuestion;
export const getTablesByProject = getTableQuestionsByProject;

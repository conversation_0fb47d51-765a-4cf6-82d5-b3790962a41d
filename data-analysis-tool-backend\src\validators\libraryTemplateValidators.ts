import { z } from "zod";
import { Sector } from "@prisma/client";

export const LibraryTemplateSchema = z.object({
  name: z.string().min(1, "Name is required"),
  description: z.string().min(1, "Description is required"),
  sector: z.nativeEnum(Sector),
  country: z.string().optional(),
});


export const deleteMultipleLibraryTemplateSchema = z.object({
  templateIds: z.array(z.number().int().nonnegative()).nonempty({
    message: "At least one template ID is required.",
  }),
});
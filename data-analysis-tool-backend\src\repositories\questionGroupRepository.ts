import { promises } from "dns";
import { prisma } from "../utils/prisma";
import { Prisma, QuestionGroup } from "@prisma/client";
import { promise } from "zod";
import { group } from "console";

class QuestionGroupRepository {
  async create(data: {
    title: string;
    order: number;
    projectId: number;
    parentGroupId?: number;
    selectedQuestionIds?: number[];
  }): Promise<QuestionGroup> {

    const newGroup = await prisma.questionGroup.create({
      data: {
        title: data.title,
        order: data.order,
        projectId: data.projectId,
        parentGroupId: data.parentGroupId,
      },
    });

    // Step 2: If there are selected question IDs, update those questions to associate them with the new group
    if (data.selectedQuestionIds && data.selectedQuestionIds.length > 0) {
      const question = await prisma.question.updateMany({
        where: {
          id: {
            in: data.selectedQuestionIds, // Array of selected question IDs
          },
        },
        data: {
          questionGroupId: newGroup.id, // Set the new questionGroupId to associate with the new group
        },
      });

      console.log("updated questions", question);
    }

    return newGroup;
  }

  async delete(id: number): Promise<QuestionGroup> {
    return await prisma.questionGroup.delete({
      where: { id },
    });
  }

  async deleteManyQuestionByGroup(id: number): Promise<Prisma.BatchPayload> {
    return await prisma.question.deleteMany({
      where: {
        questionGroupId: id,
      },
    });
  }

  async findById(id: number): Promise<QuestionGroup | null> {
    return await prisma.questionGroup.findUnique({
      where: { id },
      include: {
        question: {
          orderBy: { position: "asc" }
        }
      },
    });
  }

  async findAllByProject(projectId: number): Promise<QuestionGroup[]> {
    return await prisma.questionGroup.findMany({
      where: { projectId },
      select: {
        id: true,
        title: true,
        order: true,
        parentGroupId: true,
        projectId: true,
        createdAt: true,
        updatedAt: true,
        question: true,
        subGroups: true,
      },
      orderBy: {
        order: "asc",
      },
    });
  }

  async update(id: number, updates: any): Promise<QuestionGroup> {
    return await prisma.questionGroup.update({
      where: { id },
      data: updates,
    });
  }

  async updateGroupInsideParentGroup(
    childGroupId: number,
    ParentGroupId: number
  ): Promise<QuestionGroup | null> {
    return await prisma.questionGroup.update({
      where: {
        id: childGroupId,
      },
      data: {
        parentGroupId: ParentGroupId,
      },
    });
  }
  async RemoveGroupFromParentGroup(
    groupId: number
  ): Promise<QuestionGroup | null> {
    return prisma.questionGroup.update({
      where: {
        id: groupId,
      },
      data: {
        parentGroupId: null,
      },
    });
  }
}

export default new QuestionGroupRepository();

"use client";

import React, { useEffect, useState } from "react";
import Modal from "./Modal";
import { FieldValues, useForm } from "react-hook-form";
import { Select } from "../general/Select";
import countries from "@/constants/countryNames.json";
import { SectorLabelMap } from "@/constants/sectors";
import { Briefcase, FileText, Globe } from "lucide-react";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/redux/store";
import { hideCreateProjectModal } from "@/redux/slices/createProjectSlice";
import axios from "@/lib/axios";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { showNotification } from "@/redux/slices/notificationSlice";
import { useRouter } from "next/navigation";
import { useAuth } from "@/hooks/useAuth";
import { labelToKey } from "@/lib/labelToKey";

const createProject = async ({
  name,
  description,
  sector,
  country,
}: {
  name: string;
  description: string;
  sector: string;
  country: string;
}) => {
  const { data } = await axios.post(`/projects`, {
    name,
    description,
    sector,
    country,
  });
  return data;
};

interface CreateProjectModalProps {
  onBack?: () => void; // Optional back handler
  isOpen?: boolean;
  onClose?: () => void;
}

const CreateProjectModal = ({
  isOpen,
  onClose,
  onBack,
}: CreateProjectModalProps = {}) => {
  const showModal = useSelector(
    (state: RootState) => state.createProject.visible
  );

  const dispatch = useDispatch();

  const {
    register,
    formState: { isSubmitting, errors, isSubmitted },
    handleSubmit,
    setValue,
  } = useForm();

  const [selectedCountry, setSelectedCountry] = useState<string | null>(null);
  const [selectedSector, setSelectedSector] = useState<string | null>(null);

  useEffect(() => {
    register("country", { required: "Please select a country" });
    register("sector", { required: "Please select a sector" });
  }, [register]);

  useEffect(() => {
    setValue("country", selectedCountry, { shouldValidate: isSubmitted });
    setValue("sector", selectedSector, { shouldValidate: isSubmitted });
  }, [setValue, selectedCountry, selectedSector]);

  // without this closing animation won't work
  const [isClosing, setIsClosing] = useState(false);
  const handleClose = () => {
    setIsClosing(true);
    setTimeout(() => {
      dispatch(hideCreateProjectModal());
    }, 300);
    // match the time with animation duration
  };

  const handleBack = () => {
    if (onBack) {
      // Just call onBack without closing the modal
      onBack();
    }
  };

  const router = useRouter();
  // for posting data
  const queryClient = useQueryClient();

  const projectMutation = useMutation({
    mutationFn: createProject,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["projects"], exact: false });
      handleClose();
      router.push("/dashboard");
      dispatch(
        showNotification({
          message: "Project has been created successfully.",
          type: "success",
        })
      );
    },
    onError: () => {
      dispatch(
        showNotification({
          message: "Failed to create project",
          type: "error",
        })
      );
    },
  });

  const onSubmit = async (data: FieldValues) => {
    projectMutation.mutate({
      name: data.projectName,
      description: data.description,
      sector: data.sector,
      country: data.country,
    });
  };

  return (
    <Modal
      isOpen={showModal && !isClosing}
      onClose={handleClose}
      className="w-4/5 laptop:w-3/5 "
    >
      <h1 className="heading-text">Create a new project</h1>

      <form
        className="flex flex-col gap-8 max-h-[600px] overflow-y-auto p-4"
        onSubmit={handleSubmit(onSubmit)}
      >
        <div className="flex flex-col gap-4">
          {/* Project Name */}
          <div className="label-input-group group">
            <label htmlFor="project-name" className="label-text">
              <FileText size={16} /> Project Name
            </label>
            <input
              {...register("projectName", {
                required: "Project name is required.",
              })}
              id="project-name"
              type="text"
              className="input-field"
              placeholder="Enter a project name"
            />
            {errors.projectName && (
              <p className="text-red-500 text-sm">{`${errors.projectName.message}`}</p>
            )}
          </div>
          {/* Project Description */}
          <div className="label-input-group group">
            <label htmlFor="description" className="label-text">
              Description
            </label>
            <textarea
              id="description"
              {...register("description", {
                required: "Please enter the project description",
              })}
              className="input-field resize-none"
              cols={4}
              placeholder="Enter the project description"
            />
            {errors.description && (
              <p className="text-red-500 text-sm">{`${errors.description.message}`}</p>
            )}
          </div>
          {/* Country and Sector */}
          <div className="grid grid-cols-2 gap-4">
            <div className="label-input-group group">
              <label htmlFor="country" className="label-text">
                <Globe size={16} />
                Country
              </label>
              <Select
                id={`country`}
                options={countries}
                value={selectedCountry}
                onChange={setSelectedCountry}
              />
              {errors.country && (
                <p className="text-red-500 text-sm">{`${errors.country.message}`}</p>
              )}
            </div>
            <div className="label-input-group group">
              <label htmlFor="sector" className="label-text">
                <Briefcase size={16} /> Sector
              </label>
              <Select
                id={`sector`}
                options={Object.values(SectorLabelMap)} // Display labels
                value={
                  selectedSector && SectorLabelMap[selectedSector]
                    ? SectorLabelMap[selectedSector]
                    : "Select an option"
                }
                onChange={(label) => {
                  const selectedKey = labelToKey(label, SectorLabelMap);
                  setSelectedSector(selectedKey); // Set the enum key for storage
                }}
              />
              {errors.sector && (
                <p className="text-red-500 text-sm">{`${errors.sector.message}`}</p>
              )}
            </div>
          </div>
          <div className="flex justify-end gap-3 mt-4">
            {onBack && (
              <button
                type="button"
                onClick={handleBack}
                className="btn-outline"
              >
                Back
              </button>
            )}
            <button type="submit" className="btn-primary">
              {isSubmitting ? (
                <span className="flex items-center gap-2">
                  Creating{" "}
                  <div className="size-4 animate-spin border-x border-neutral-100 rounded-full"></div>
                </span>
              ) : (
                "Create Project"
              )}
            </button>
          </div>
        </div>
      </form>
    </Modal>
  );
};

export { CreateProjectModal };

import reportRepository from "../../repositories/reportRepository";
import { prisma } from "../../utils/prisma";

// Mock the prisma client
jest.mock("../../utils/prisma", () => ({
  prisma: {
    project: {
      findUnique: jest.fn(),
    },
    formSubmission: {
      findMany: jest.fn(),
    },
  },
}));

describe("Report Repository", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("generateReport", () => {
    it("should include all question types in the report", async () => {
      // Mock project with different question types
      const mockProject = {
        id: 1,
        name: "Test Project",
        questions: [
          {
            id: 1,
            label: "Select One Question",
            inputType: "selectone",
            questionOptions: [
              { id: 1, label: "Option 1" },
              { id: 2, label: "Option 2" },
            ],
          },
          {
            id: 2,
            label: "Select Many Question",
            inputType: "selectmany",
            questionOptions: [
              { id: 3, label: "Option A" },
              { id: 4, label: "Option B" },
            ],
          },
          {
            id: 3,
            label: "Text Question",
            inputType: "text",
            questionOptions: [],
          },
          {
            id: 4,
            label: "Number Question",
            inputType: "number",
            questionOptions: [],
          },
        ],
      };

      // Mock form submissions
      const mockSubmissions = [
        {
          id: 1,
          createdAt: new Date(),
          answers: [
            {
              questionId: 1,
              questionOptionId: 1,
              value: "",
            },
            {
              questionId: 2,
              questionOptionId: null,
              value: "[3, 4]",
            },
            {
              questionId: 3,
              questionOptionId: null,
              value: "Text answer",
            },
            {
              questionId: 4,
              questionOptionId: null,
              value: "42",
            },
          ],
        },
      ];

      // Setup mocks
      (prisma.project.findUnique as jest.Mock).mockResolvedValue(mockProject);
      (prisma.formSubmission.findMany as jest.Mock).mockResolvedValue(mockSubmissions);

      // Call the method
      const report = await reportRepository.generateReport(1, {});

      // Verify all questions are included
      expect(report.data.length).toBe(4);
      
      // Verify each question type is included
      const selectOneQuestion = report.data.find(item => item.question === "Select One Question");
      const selectManyQuestion = report.data.find(item => item.question === "Select Many Question");
      const textQuestion = report.data.find(item => item.question === "Text Question");
      const numberQuestion = report.data.find(item => item.question === "Number Question");
      
      expect(selectOneQuestion).toBeDefined();
      expect(selectManyQuestion).toBeDefined();
      expect(textQuestion).toBeDefined();
      expect(numberQuestion).toBeDefined();
      
      // Verify question types are correct
      expect(selectOneQuestion?.type).toBe("selectone");
      expect(selectManyQuestion?.type).toBe("selectmany");
      expect(textQuestion?.type).toBe("text");
      expect(numberQuestion?.type).toBe("number");

      // Verify summary statistics are correct
      expect(report.summary.totalQuestions).toBe(4);
    });

    it("should handle empty questions array", async () => {
      // Mock project with no questions
      const mockProject = {
        id: 1,
        name: "Test Project",
        questions: [],
      };

      // Mock empty submissions
      const mockSubmissions: any[] = [];

      // Setup mocks
      (prisma.project.findUnique as jest.Mock).mockResolvedValue(mockProject);
      (prisma.formSubmission.findMany as jest.Mock).mockResolvedValue(mockSubmissions);

      // Call the method
      const report = await reportRepository.generateReport(1, {});

      // Verify empty data array
      expect(report.data).toEqual([]);
      
      // Verify summary statistics
      expect(report.summary.totalQuestions).toBe(0);
      expect(report.summary.totalSubmissions).toBe(0);
      expect(report.summary.averageResponseRate).toBe(0);
    });
  });
});

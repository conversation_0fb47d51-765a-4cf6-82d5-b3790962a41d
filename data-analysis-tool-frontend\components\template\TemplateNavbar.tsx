"use client";

import { PanelsTopLeft, <PERSON>tings, FilePen, Database } from "lucide-react";
import { useParams } from "next/navigation";
import { Navbar } from "@/components/general/SecondaryNavbar";

const TemplateNavbar = () => {
  const { hashedId } = useParams();
  const hashedIdString = hashedId as string;

  const projectNavbarItems = [
    {
      label: "Form Builder",
      icon: <FilePen size={16} />,
      route: `/library/template/${hashedIdString}/form-builder`,
    },
    {
      label: "Settings",
      icon: <Settings size={16} />,
      route: `/library/template/${hashedIdString}/settings`,
    },
  ];

  return <Navbar items={projectNavbarItems} />;
};

export { TemplateNavbar };

-- CreateTable
CREATE TABLE "columns" (
    "id" SERIAL NOT NULL,
    "question_id" INTEGER NOT NULL,
    "column_name" TEXT NOT NULL,
    "parent_column_id" INTEGER,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "columns_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "rows" (
    "id" SERIAL NOT NULL,
    "rows_name" TEXT NOT NULL,
    "question_id" INTEGER NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "rows_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "column_rows" (
    "id" SERIAL NOT NULL,
    "column_id" INTEGER NOT NULL,
    "rows_id" INTEGER NOT NULL,
    "value" TEXT,
    "code" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "column_rows_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "column_rows_column_id_rows_id_key" ON "column_rows"("column_id", "rows_id");

-- AddForeignKey
ALTER TABLE "columns" ADD CONSTRAINT "columns_question_id_fkey" FOREIGN KEY ("question_id") REFERENCES "Question"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "columns" ADD CONSTRAINT "columns_parent_column_id_fkey" FOREIGN KEY ("parent_column_id") REFERENCES "columns"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "rows" ADD CONSTRAINT "rows_question_id_fkey" FOREIGN KEY ("question_id") REFERENCES "Question"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "column_rows" ADD CONSTRAINT "column_rows_column_id_fkey" FOREIGN KEY ("column_id") REFERENCES "columns"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "column_rows" ADD CONSTRAINT "column_rows_rows_id_fkey" FOREIGN KEY ("rows_id") REFERENCES "rows"("id") ON DELETE CASCADE ON UPDATE CASCADE;

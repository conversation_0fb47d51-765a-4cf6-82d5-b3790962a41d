import { z } from "zod";

export const ProjectUserSchema = z.object({
  userId: z.number(),
  projectId: z.number(),
  permission: z
    .object({
      viewForm: z.boolean().optional(),
      editForm: z.boolean().optional(),
      viewSubmissions: z.boolean().optional(),
      viewSubmissionsSpecific: z.boolean().optional(),
      viewSubmissionsCondition: z.boolean().optional(),
      addSubmissions: z.boolean().optional(),
      editSubmissions: z.boolean().optional(),
      editSubmissionsSpecific: z.boolean().optional(),
      editSubmissionsCondition: z.boolean().optional(),
      validateSubmissions: z.boolean().optional(),
      validateSubmissionsSpecific: z.boolean().optional(),
      validateSubmissionsCondition: z.boolean().optional(),
      deleteSubmissions: z.boolean().optional(),
      deleteSubmissionsSpecific: z.boolean().optional(),
      deleteSubmissionsCondition: z.boolean().optional(),
      manageProject: z.boolean().optional(),
    })
    .strict(),
});

// src/controllers/AnswerController.ts
import { Request, Response } from "express";
import answerRepository from "../repositories/answerRepository";
import {
  createAnswerSchema,
  CreateAnswerInput,
  answerArraySchema,
  updateMultipleAnswerSchema,
} from "../validators/answerValidator";
import { validateInput } from "../utils/validateAnswer";
import ExcelJS from "exceljs";
import { format } from "@fast-csv/format";
import formSubmissionRepository from "../repositories/formSubmissionRepository";
import axios from "axios";
import useragent from "useragent";
import questionRepository from "../repositories/questionRepository";

interface UserRequest extends Request {
  user?: {
    id: number;
  };
}

interface GeoLocationResponse {
  city: string;
  region: string;
  country_name: string;
}

const getLocationByIP = async (ip: string): Promise<string | null> => {
  const response = await axios.get<GeoLocationResponse>(
    `https://ipapi.co/${ip}/json/`
  );
  const { city, region, country_name } = response.data;
  return `${city}, ${region}, ${country_name}`;
};

export const createAnswer = async (req: Request, res: Response) => {
  try {
    const result = createAnswerSchema.safeParse(req.body);

    if (!result.success) {
      return res.status(400).json({
        success: false,
        errors: result.error.flatten().fieldErrors,
      });
    }

    const validatedData = result.data;
    const { submissionId, questionId, answerType, value, questionOptionId } =
      validatedData;

    // Check for duplicate entry unless it's selectmany
    const existance = await answerRepository.findBySubmissionIdQuestion(
      submissionId,
      questionId
    );

    if (existance && answerType !== "selectmany") {
      return res.status(400).json({
        success: false,
        message: "Answer already created",
      });
    }

    // Validate input
    const validation = validateInput(answerType, value);
    if (!validation.valid) {
      return res.status(400).json({
        success: false,
        message: "Validation error",
        errors: validation.errors,
      });
    }

    // Handle selectmany
    if (answerType === "selectmany") {
      if (!Array.isArray(value)) {
        return res.status(400).json({
          success: false,
          message: "`value` must be an array for selectmany answers",
        });
      }

      if (!Array.isArray(questionOptionId)) {
        return res.status(400).json({
          success: false,
          message: "`questionOptionId` must be an array for selectmany answers",
        });
      }

      if (value.length !== questionOptionId.length) {
        return res.status(400).json({
          success: false,
          message:
            "`value` and `questionOptionId` arrays must be the same length",
        });
      }

      const createdAnswers = [];

      for (let i = 0; i < value.length; i++) {
        const singleAnswer = {
          ...validatedData,
          value: String(value[i]),
          questionOptionId: questionOptionId[i],
        };

        const answer = await answerRepository.createAnswer(singleAnswer);
        createdAnswers.push(answer);
      }

      return res.status(201).json({
        success: true,
        message: "Multiple answers created successfully",
        data: { answers: createdAnswers },
      });
    }

    // Handle single-value answers
    if (typeof value !== "string") {
      validatedData.value = String(value);
    }

    const answer = await answerRepository.createAnswer(validatedData);

    return res.status(201).json({
      success: true,
      message: "Answer created successfully",
      data: { answer },
    });
  } catch (error: unknown) {
    return res.status(500).json({
      success: false,
      message: "Error creating answer",
      error: error instanceof Error ? error.message : "Unexpected error",
    });
  }
};

export const getAnswersBySubmission = async (req: Request, res: Response) => {
  try {
    const { submissionId } = req.body;
    const answers = await answerRepository.getAnswersBySubmission(
      parseInt(submissionId)
    );
    return res.status(200).json({
      success: true,
      message: "success getting answer by submission id",
      data: { answers },
    });
  } catch (error: unknown) {
    return res.status(500).json({
      success: false,
      message: "error getting answer",
      error: error instanceof Error ? error.message : "unexpected error",
    });
  }
};

export const getAnswerById = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const answer = await answerRepository.getAnswerById(parseInt(id));
    if (!answer) {
      return res.status(404).json({ message: "Answer not found" });
    }
    return res.status(201).json({
      success: true,
      message: "success getting answer by id",
      data: { answer },
    });
  } catch (error: unknown) {
    return res.status(500).json({
      success: false,
      message: "error getting answer",
      error: error instanceof Error ? error.message : "unexpected error",
    });
  }
};

export const updateAnswer = async (req: Request, res: Response) => {
  try {
    const result = createAnswerSchema.safeParse(req.body);

    if (!result.success) {
      return res.status(400).json({
        success: false,
        errors: result.error.flatten().fieldErrors,
      });
    }

    const validatedData = result.data;
    const { submissionId, questionId, answerType, value, questionOptionId } =
      validatedData;

    const validation = validateInput(answerType, value);
    if (!validation.valid) {
      return res.status(400).json({
        success: false,
        message: "Validation error",
        errors: validation.errors,
      });
    }

    // Handle select_many: delete old answers and create new ones
    if (answerType === "selectmany") {
      if (!Array.isArray(value)) {
        return res.status(400).json({
          success: false,
          message: "`value` must be an array for select_many",
        });
      }

      const answerOptionIds = Array.isArray(questionOptionId)
        ? questionOptionId
        : [];

      const updatedAnswers = await answerRepository.updateSelectManyAnswers(
        submissionId,
        questionId,
        value,
        answerOptionIds
      );

      return res.status(200).json({
        success: true,
        message: "select_many answers updated",
        data: { answers: updatedAnswers },
      });
    }

    // Handle all other answer types
    const updatedAnswer = await answerRepository.updateSingleAnswer(
      validatedData
    );

    return res.status(200).json({
      success: true,
      message: "Answer updated successfully",
      data: { answer: updatedAnswer },
    });
  } catch (error: any) {
    return res.status(500).json({
      success: false,
      message: "Error updating answer",
      error: error instanceof Error ? error.message : "Unexpected error",
    });
  }
};

export const deleteAnswer = async (req: Request, res: Response) => {
  try {
    const { submissionId, questionId, answerType } = req.body;

    if (
      !submissionId ||
      !questionId ||
      !answerType ||
      typeof submissionId !== "number" ||
      typeof questionId !== "number"
    ) {
      return res.status(400).json({
        success: false,
        message: "submissionId, questionId and answerType are required",
      });
    }

    const deleted =
      await answerRepository.deleteAnswersBySubmissionIdAndQuestionId(
        submissionId,
        questionId,
        answerType
      );

    return res.status(200).json({
      success: true,
      message: "Answer(s) deleted successfully",
      data: deleted,
    });
  } catch (error: unknown) {
    return res.status(500).json({
      success: false,
      message: "Error deleting answer",
      error: error instanceof Error ? error.message : "Unexpected error",
    });
  }
};

export const submitMultipleAnswer = async (req: UserRequest, res: Response) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({
        success: false,
        message: "Unauthorized",
      });
    }

    const result = answerArraySchema.safeParse(req.body);
    if (!result.success) {
      return res.status(400).json({
        success: false,
        errors: result.error.flatten().fieldErrors,
      });
    }

    const validatedData = result.data;

    const formSubmissionData = {
      projectId: validatedData[0].projectId,
    };

    const agent = useragent.parse(req.headers["user-agent"]);
    const deviceInfo = `${agent.family} on ${agent.os}`;

    const ip = req.ip!;
    const rawLocation = await getLocationByIP(ip);
    const location = rawLocation ?? undefined;

    const formSubmission = await formSubmissionRepository.create(
      formSubmissionData,
      userId,
      deviceInfo,
      location
    );

    const transformed = validatedData.flatMap((a) => {
      if (a.answerType === "selectmany" && Array.isArray(a.questionOptionId)) {
        const values = a.value
          ? String(a.value)
              .split(", ")
              .filter((v) => v)
          : [];

        return a.questionOptionId.map((optionId, index) => ({
          formSubmissionId: formSubmission.id,
          questionId: a.questionId,
          value: values[index] || "",
          answerType: a.answerType,
          imageUrl: a.imageUrl,
          questionOptionId: optionId,
          isOtherOption: a.isOtherOption ?? false,
        }));
      } else if (a.answerType === "table" && a.value) {
        // Handle table data - parse the JSON string and create a single answer record
        try {
          // Ensure the value is a string before parsing
          const tableValue = typeof a.value === "string" ? a.value : String(a.value);
          
          // Validate that it's valid JSON before saving
          JSON.parse(tableValue);
          
          return [
            {
              formSubmissionId: formSubmission.id,
              questionId: a.questionId,
              value: tableValue,
              answerType: a.answerType,
              imageUrl: a.imageUrl,
              questionOptionId: undefined,
              isOtherOption: a.isOtherOption ?? false,
            },
          ];
        } catch (err) {
          console.error("Error processing table data:", err);
          // Return an empty answer with empty value if JSON parsing fails
          return [
            {
              formSubmissionId: formSubmission.id,
              questionId: a.questionId,
              value: "",
              answerType: a.answerType,
              imageUrl: a.imageUrl,
              questionOptionId: undefined,
              isOtherOption: a.isOtherOption ?? false,
            },
          ];
        }
      } else {
        // Handle other answer types
        return [
          {
            formSubmissionId: formSubmission.id,
            questionId: a.questionId,
            value: typeof a.value !== "undefined" ? String(a.value) : "",
            answerType: a.answerType,
            imageUrl: a.imageUrl,
            questionOptionId: Array.isArray(a.questionOptionId)
              ? undefined
              : a.questionOptionId,
            isOtherOption: a.isOtherOption ?? false,
          },
        ];
      }
    });

    const answer = await answerRepository.AddMultipleAnswer(transformed);

    return res.status(200).json({
      success: true,
      message: "answer created success",
      data: { answer },
    });
  } catch (error: unknown) {
    return res.status(500).json({
      success: false,
      message: "error creating answers",
      error: error instanceof Error ? error.message : "unexpected error",
    });
  }
};

export const updateMultipleAnswers = async (req: Request, res: Response) => {
  try {
    const validationResult = updateMultipleAnswerSchema.safeParse(req.body);

    if (!validationResult.success) {
      return res.status(400).json({
        success: false,
        message: "Validation failed",
        errors: validationResult.error.flatten(),
      });
    }

    const inputAnswer = validationResult.data;

    // Split into updates and creates
    const updates = inputAnswer.filter((ans) => ans.id !== undefined);
    const creates = inputAnswer.filter(
      (ans) => ans.id === undefined && ans.value !== undefined
    );

    // Filter out duplicates from create list
    const filteredCreates = [];
    for (const ans of creates) {

      const existance = await questionRepository.findById(ans?.questionId)
      if(!existance){
        return res.status(404).json({message:"question id not found"})
      }
      const isDuplicate = await answerRepository.findDuplicateAnswer(
        ans.formSubmissionId,
        ans.questionId!,
        ans.answerType
      );

      if (!isDuplicate) {
        filteredCreates.push({
          formSubmissionId: ans.formSubmissionId,
          questionId: ans.questionId,
          value: ans.value,
          answerType: ans.answerType,
          imageUrl: ans.imageUrl,
          questionOptionId: ans.questionOptionId,
          isOtherOption: ans.isOtherOption,
        });
      }
    }

    // Perform update
    const updateAnswers =
      updates.length > 0
        ? await answerRepository.UpdateMultipleAnswers(updates)
        : [];

    // Perform create
    const createdAnswers =
      filteredCreates.length > 0
        ? await answerRepository.AddMultipleAnswer(filteredCreates)
        : [];

    const allAnswers = [...updateAnswers, ...createdAnswers];

    return res.status(200).json({
      success: true,
      message: "Answers updated successfully",
      data: allAnswers,
    });
  } catch (error: any) {
    console.error("Error updating multiple answers:", error);
    return res.status(500).json({
      success: false,
      message: "Something went wrong while updating answers",
      error: error.message,
    });
  }
};

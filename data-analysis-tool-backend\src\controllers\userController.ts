// import jwt from 'jsonwebtoken';
import jwt from "jsonwebtoken";

import userRepository from "../repositories/userRepository";
import { Request, Response } from "express";
import { ApiResponse } from "../utils/ApiResponse";
import { userSchema, updateProfileSchema } from "../validators/UserValidators";
import CryptoJS from "crypto-js";
import SHA256 from "crypto-js/sha256";
import { sendEmail } from "../utils/sendMail";
import { User } from "@prisma/client";
import { emailTemplate } from "../utils/emailTemplate";
import { forgetPasswordEmailTemplate } from "../utils/forgotPasswordEmailTemplate";
import useragent from "useragent";

interface userRequest extends Request {
  user?: {
    id: number;
    sessionId: number;
  };
}

const baseUrl = process.env.BASE_URL || "http://localhost:4000";

const clientUrl = process.env.CLIENT_URL || "http://localhost:3000";
// User signup
export const signup = async (req: Request, res: Response) => {
  try {
    const result = userSchema.safeParse(req.body);

    if (!result.success) {
      res.status(400).json({
        success: false,
        message: result.error.flatten().fieldErrors,
      });
      return;
    }

    const { name, email, password, country, sector, organizationType } =
      result.data;

    // Check if user already exists
    const existingUser = await userRepository.findByEmail(email);
    if (existingUser) {
      res.status(400).json({
        success: false,
        errorField: "email",
        message: "email already in use",
      });
      return;
    }

    // Create new user
    const user = await userRepository.create({
      name,
      email,
      password,
      country,
      sector,
      organizationType,
    });

    const verificationToken = CryptoJS.lib.WordArray.random(32).toString(
      CryptoJS.enc.Hex
    );
    const hashedToken = CryptoJS.SHA256(verificationToken).toString(
      CryptoJS.enc.Hex
    );
    const expires = new Date(Date.now() + 15 * 60 * 1000); // 15 minutes

    // update verification token
    await userRepository.sendEmailVerificationToken(
      user.id,
      hashedToken,
      expires
    );

    const verifyUrl = `${baseUrl}/api/users/verifyemail/${verificationToken}`;

    res.status(201).json({
      success: true,
      data: user,
      message: "user register success",
    });

    setTimeout(async () => {
      try {
        await sendEmail(email, "verify your email", emailTemplate(verifyUrl));
        console.log(`Verification email sent to ${email}`);
      } catch (emailError) {
        console.error(
          `Failed to send verification email to ${email}:`,
          emailError
        );
      }
    }, 0);
  } catch (error: unknown) {
    return res.status(500).json({
      success: false,
      message: "error creating user",
      error: error instanceof Error ? error.message : "Unexpected error",
    });
  }
};

export const verifyEmail = async (
  req: userRequest,
  res: Response
): Promise<void> => {
  const { token } = req.params;

  try {
    const hashedToken = CryptoJS.SHA256(token).toString(CryptoJS.enc.Hex);

    const tokenavailable = await userRepository.findByemailVerificationToken(
      hashedToken
    );

    if (!tokenavailable) {
      res.redirect(`${clientUrl}/email-verify?status=INVALID`);
      return;
    }

    const expiarystatus = await userRepository.isEmailVerificationTokenExpired(
      hashedToken
    );

    if (!expiarystatus) {
      res.redirect(`${clientUrl}/email-verify?status=EXPIRED`);
      return;
    }

    await userRepository.verifyUser(expiarystatus.id);

    res.redirect(`${clientUrl}/email-verify?status=SUCCESS`);
    return;
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: "error verifying email",
      error: error.message,
    });
    return;
  }
};

// User login
export const login = async (req: userRequest, res: Response) => {
  try {
    const { email, password } = req.body;

    // Check if user exists
    const user = await userRepository.findByEmail(email);

    if (!user || !user.id) {
      return res.status(400).json({
        success: false,
        message: "user not found",
      });
    }
    // Verify password
    const isPasswordValid = await userRepository.verifyPassword(
      user.id,
      password
    );

    if (!isPasswordValid) {
      return res.status(400).json({
        success: false,
        message: "invalid password",
      });
    }

    if (!user.isVerified) {
      return res.status(403).json({
        success: false,
        errorType: "unverified",
        message:
          "Email is not verified. Please verify your email before logging in.",
      });
    }

    const agent = useragent.parse(req.headers["user-agent"]);
    const browserInfo = agent.family; // eg: Chrome, brave etc
    const deviceInfo = agent.os.toString(); // OS name and version (e.g., "Mac OS X 10_15_4")

    const ipAddress = req.ip ?? "unknown-ip";

    const session = await userRepository.createSession({
      userId: user.id,
      deviceInfo,
      ipAddress,
      browserInfo,
    });

    // Create JWT token
    const token = jwt.sign(
      {
        id: user.id,
        name: user.name,
        email: user.email,
        sessionId: session.id,
      },
      process.env.JWT_SECRET as string,
      {
        expiresIn: "24h",
      }
    );

    const options = {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      domain: process.env.DOMAIN_NAME,
    };
    // Set cookie and send response
    return res
      .status(200)
      .cookie("token", token, options)
      .json({ message: "Log in successful.", user });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: "error logging user",
      error: error.message,
    });
    return;
  }
};

export const logout = async (
  req: userRequest,
  res: Response
): Promise<void> => {
  try {
    // for deactivating the user session
    const userId = Number(req.user?.id);
    const sessionId = Number(req.user?.sessionId);
    await userRepository.logoutSingleDevice(userId, sessionId);

    // removing token from cookie
    res
      .status(200)
      .clearCookie("token", {
        httpOnly: true,
        secure: true,
        domain: process.env.DOMAIN_NAME,
      })
      .json(new ApiResponse(200, null, "user logout success"));
    return;
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: "error logging out user",
      error: error.message,
    });
    return;
  }
};

export const logoutFromAllDevice = async (
  req: userRequest,
  res: Response
): Promise<void> => {
  try {
    if (!req.user) {
      res.status(404).json({
        success: false,
        message: "user not found",
      });
      return;
    }
    const userId = req.user.id;

    await userRepository.logoutAllDevices(userId);

    res.status(200).json({
      success: true,
      message: "logged out from all the devices",
    });
  } catch (error: unknown) {
    res.status(500).json({
      success: false,
      message: "error creating qustion",
      error: error instanceof Error ? error.message : "unexpected error",
    });
    return;
  }
};
export const updateProfile = async (
  req: userRequest,
  res: Response
): Promise<void> => {
  try {
    const result = updateProfileSchema.safeParse(req.body);

    if (!result.success) {
      res.status(400).json({
        success: false,
        message: "Validation failed",
        error: result.error.flatten(),
      });
      return;
    }

    const { name, country, city, bio, sector, organizationType } = result.data;

    if (!req.user || !req.user.id) {
      res.status(401).json({
        success: false,
        message: "unauthorized : user not found in request",
      });
      return;
    }
    const userId = req.user.id;

    const updateUser = await userRepository.updateById(userId, {
      name,
      country,
      city,
      bio,
      sector,
      organizationType,
    });

    res
      .status(200)
      .json(new ApiResponse(200, { updateUser }, "user updated success"));
    return;
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: "error updating profile",
      error: error.message,
    });
    return;
  }
};

export const changePassword = async (
  req: userRequest,
  res: Response
): Promise<void> => {
  try {
    const { currentPassword, newPassword } = req.body;

    if (!req.user || !req.user?.id) {
      res.status(400).json({ success: false, message: "user is not found" });
      return;
    }

    const isPasswordValid = await userRepository.verifyPassword(
      req.user?.id,
      currentPassword
    );
    if (!isPasswordValid) {
      res
        .status(400)
        .json({ success: false, message: "current password doesnot match" });
      return;
    }

    await userRepository.updatePassword(req.user?.id, newPassword);
    res.status(200).json(new ApiResponse(200, "password change success"));
    return;
  } catch (error: any) {
    res.status(500).json({ success: false, error: "server error" });
    return;
  }
};

export const forgetPassword = async (
  req: userRequest,
  res: Response
): Promise<void> => {
  const { email } = req.body;
  try {
    const user = await userRepository.findByEmail(email);

    if (!user) {
      res.status(404).json({ message: "user not found" });
      return;
    }

    const resetToken = CryptoJS.lib.WordArray.random(32).toString(
      CryptoJS.enc.Hex
    );
    const hashedToken = CryptoJS.SHA256(resetToken).toString(CryptoJS.enc.Hex);
    const tokenExpiry = new Date(Date.now() + 15 * 60 * 1000); // 15 minutes

    await userRepository.saveResetToken(user.id, hashedToken, tokenExpiry);

    const resetURL = `${baseUrl}/api/users/resetpasswordtoken/${resetToken}`;
    const message = forgetPasswordEmailTemplate(resetURL);

    await sendEmail(user.email, "Reset password request", message);

    res.status(200).json({
      message: "Password reset email sent",
    });
    return;
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: "error calling api",
      error: error.message,
    });
    return;
  }
};

export const checkPasswordResetToken = async (
  req: userRequest,
  res: Response
): Promise<void> => {
  const { token } = req.params;

  try {
    const hashedToken = SHA256(token).toString(CryptoJS.enc.Hex);

    const tokenAvailable = await userRepository.checkPasswordToken(hashedToken);

    if (!tokenAvailable) {
      res.redirect(
        `${clientUrl}/reset-password/change-password?status=INVALID`
      );
    }

    const expiaryStatus = await userRepository.checkPasswordTokenExpirey(
      hashedToken
    );

    if (!expiaryStatus) {
      res.redirect(
        `${clientUrl}/reset-password/change-password?status=EXPIRED`
      );
      return;
    }

    res.redirect(
      `${clientUrl}/reset-password/change-password?status=SUCCESS&token=${hashedToken}`
    );
  } catch (error: unknown) {
    res.status(500).json({
      success: false,
      message: "error creating qustion",
      error: error instanceof Error ? error.message : "unexpected error",
    });
    return;
  }
};

export const resetPassword = async (
  req: userRequest,
  res: Response
): Promise<void> => {
  try {
    const { newPassword, token } = req.body;

    const user = await userRepository.checkPasswordToken(token);
    if (!user) {
      res.status(404).json({
        success: false,
        message: "invalid user token",
      });
      return;
    }

    await userRepository.resetUserPassword(user.id, newPassword);

    res.status(200).json({
      success: true,
      message: "password changed success",
    });

    return;
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: "error calling api",
      error: error.message,
    });
  }
};

export const findUserById = async (
  req: userRequest,
  res: Response
): Promise<void> => {
  const id = Number(req.params.id);

  try {
    const user = await userRepository.findById(id);

    if (!user) {
      res.status(404).json({
        success: false,
        message: "no user found with given id",
      });
      return;
    }

    res
      .status(200)
      .json(new ApiResponse(200, { user }, "user fetched success"));
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: "error calling api",
      error: error.message,
    });
    return;
  }
};

export const findAllUsers = async (
  req: userRequest,
  res: Response
): Promise<void> => {
  try {
    const users = await userRepository.findAll();

    if (!users) {
      res.status(404).json({
        success: false,
        message: "no user found",
      });
      return;
    }

    res
      .status(200)
      .json(new ApiResponse(200, { users }, "users fetch success"));
    return;
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: "error calling api",
      error: error.message,
    });
  }
};

export const userProfile = async (
  req: userRequest,
  res: Response
): Promise<void> => {
  try {
    const id = Number(req.user!.id);
    if (!id) {
      res.status(401).json({
        success: false,
        message: "id not found, unauthenticated",
      });
      return;
    }

    const profile = await userRepository.findById(id);

    res
      .status(200)
      .json({ message: "Successfully fetched user profile", profile });
    return;
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: "error calling api",
      error: error.message,
    });
  }
};

export const sendVerificationEmail = async (req: Request, res: Response) => {
  try {
    const { email } = await req.body;

    if (!email) {
      return res
        .status(400)
        .json({ message: "Email is not provided in the request" });
    }

    const user: User | null = await userRepository.findByEmail(email);

    if (!user) {
      return res
        .status(404)
        .json({ message: "User with the provided email doesn't exist." });
    }

    const verificationToken = CryptoJS.lib.WordArray.random(32).toString(
      CryptoJS.enc.Hex
    );
    const hashedToken = CryptoJS.SHA256(verificationToken).toString(
      CryptoJS.enc.Hex
    );
    const expires = new Date(Date.now() + 15 * 60 * 1000); // 15 minutes

    // update verification token
    await userRepository.sendEmailVerificationToken(
      user.id,
      hashedToken,
      expires
    );

    const verifyUrl = `${baseUrl}/api/users/verifyemail/${verificationToken}`;

    const message = emailTemplate(verifyUrl);

    await sendEmail(user.email, "verify your email", message);

    return res
      .status(200)
      .json({ message: "Verification email sent successfully" });
  } catch (error: unknown) {
    return res.status(500).json({
      message:
        error instanceof Error
          ? `An error occured while trying to send verification email: ${error.message}`
          : "An unexpected error occured while trying to send verification email",
    });
  }
};

// Type for JWT PAYLOAD
type JwtPayload = {
  id: number;
  email: string;
  name: string;
  sessionId: number;
  iat?: number;
  exp?: number;
};

// This function returns the current session information (not from the session table but from the token)
export const getUserSession = async (req: Request, res: Response) => {
  const token = req.cookies.token;
  if (!token) {
    return res.status(401).json({ user: null });
  }
  try {
    const jwtSecret = process.env.JWT_SECRET;
    if (!jwtSecret) {
      throw new Error("Jwt Secret is not present in environment variable");
    }
    const decoded = jwt.verify(token, jwtSecret) as JwtPayload;
    return res.status(200).json({
      id: decoded.id,
      email: decoded.email,
      name: decoded.name,
      sessionId: decoded.sessionId,
    });
  } catch (error) {
    return res.status(401).json({ user: null });
  }
};

export const changeEmail = async (req: userRequest, res: Response) => {
  try {
    const id = Number(req.user?.id);
    if (!id) {
      return res.status(401).json({ message: "Id not found, unauthenticated" });
    }

    const { email } = req.body;

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

    if (!emailRegex.test(email)) {
      return res.status(400).json({ message: "Invalid email format." });
    }

    const existingUser = await userRepository.findByEmail(email);
    if (existingUser) {
      return res.status(400).json({
        success: false,
        errorField: "email",
        message: "email already in use",
      });
    }

    const user = await userRepository.changeEmail(id, { email });
    return res
      .status(200)
      .json({ message: "Successfully updated email.", user });
  } catch (error: unknown) {
    return res.status(500).json({
      message:
        error instanceof Error
          ? `Failed to update email: ${error.message}`
          : "An unexpected error occured while tyring to update email.",
    });
  }
};

export const getUserSessions = async (req: userRequest, res: Response) => {
  try {
    const userId = Number(req.user?.id);
    if (!userId) {
      return res.status(401).json({ message: "Id not found, unauthenticated" });
    }

    const sessions = await userRepository.getUserSessions({ userId });

    return res
      .status(200)
      .json({ message: "Successfully fetched user sessions.", sessions });
  } catch (error: unknown) {
    return res.status(500).json({
      message:
        error instanceof Error
          ? `Failed to fetch user sessions ${error.message}`
          : "An unexpected error occured while trying to fetch user sessions",
    });
  }
};

export const checkUserByEmail = async (req: Request, res: Response) => {
  try {
    const { email } = req.body;

    if (!email) {
      return res.status(400).json({
        success: false,
        message: "Email is required",
      });
    }

    const user = await userRepository.findByEmail(email);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: "User with this email does not exist",
      });
    }

    return res.status(200).json({
      success: true,
      message: "User exists",
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
      },
    });
  } catch (error: unknown) {
    return res.status(500).json({
      success: false,
      message: "Error checking user existence",
      error: error instanceof Error ? error.message : "Unexpected error",
    });
  }
};

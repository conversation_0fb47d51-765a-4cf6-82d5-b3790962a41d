import React, { useEffect, useState } from "react";
import Modal from "./Modal";
import { FieldVal<PERSON>, FormProvider, useForm } from "react-hook-form";
import { Select } from "../general/Select";
import { labelToKey } from "@/lib/labelToKey";
import { InputTypeMap } from "@/constants/inputType";
import { Switch } from "../ui";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { addQuestion } from "@/lib/api/form-builder";
import { DynamicOptions } from "../form-builder/DynamicOptions";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { needsOptions } from "@/lib/needsOptions";
import { QuestionSchema } from "@/types/formBuilder";
import { ContextType } from "@/types";
import { LoadingOverlay } from "../general/LoadingOverlay";
import { TableQuestionBuilder } from "../form-builder/TableQuestionBuilder";

// Confirmation dialog component
const ConfirmationDialog = ({
  isOpen,
  onConfirm,
  onCancel,
}: {
  isOpen: boolean;
  onConfirm: () => void;
  onCancel: () => void;
}) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-md w-full">
        <h2 className="text-lg text-neutral-700 font-semibold mb-4">Unsaved Changes</h2>
        <p className="mb-6 text-neutral-700">
          You have unsaved changes. Are you sure you want to close this form?
        </p>
        <div className="flex justify-end space-x-3">
          <button
            onClick={onCancel}
            className="btn-outline"
          >
            Cancel
          </button>
          <button
            onClick={onConfirm}
            className="btn-danger"
          >
            Discard Changes
          </button>
        </div>
      </div>
    </div>
  );
};

const AddQuestionModal = ({
  showModal,
  setShowModal,
  contextType,
  contextId,
  position,
}: {
  showModal: boolean;
  setShowModal: React.Dispatch<React.SetStateAction<boolean>>;
  contextType: ContextType;
  contextId: number;
  position?: number;
}) => {
  const methods = useForm<z.infer<typeof QuestionSchema>>({
    resolver: zodResolver(QuestionSchema),
    defaultValues: {
      label: "",
      inputType: "",
      hint: "",
      placeholder: "",
      questionOptions: [],
    },
  });

  const {
    register,
    formState: { errors, isSubmitted, isDirty },
    setValue,
    handleSubmit,
    reset,
    watch,
  } = methods;

  // State for confirmation dialog
  const [showConfirmation, setShowConfirmation] = useState(false);

  const queryClient = useQueryClient();
  const queryKey =
    contextType === "project"
      ? ["questions", contextId]
      : contextType === "template"
      ? ["templateQuestions", contextId]
      : ["questionBlockQuestions", contextId];
  const questionMutation = useMutation({
    mutationFn: addQuestion,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey });
      reset();
      setSelectedInputType("");
      setShowModal(false);
    },
    onError: () => {},
  });

  const [isRequired, setIsRequired] = useState(false);
  const [selectedInputType, setSelectedInputType] = useState<string>("");

  useEffect(() => {
    register("inputType", { required: "Please select an input type" });
  }, [register]);

  useEffect(() => {
    setValue("inputType", selectedInputType, { shouldValidate: isSubmitted });
  }, [selectedInputType]);

  useEffect(() => {
    if (showModal) {
      setIsLoading(false);
    }
  }, [showModal]);

  const [isLoading, setIsLoading] = useState(false);

  // Function to check if there are unsaved changes
  const hasUnsavedChanges = () => {
    // Check if the form is dirty (has been modified)
    const questionOptions = watch("questionOptions");

    return (
      isDirty ||
      // Check if any input has been filled
      !!watch("label") ||
      !!watch("hint") ||
      !!watch("placeholder") ||
      !!selectedInputType ||
      (questionOptions &&
        Array.isArray(questionOptions) &&
        questionOptions.length > 0)
    );
  };

  // Function to handle modal close with confirmation if needed
  const handleCloseWithConfirmation = () => {
    if (hasUnsavedChanges()) {
      setShowConfirmation(true);
    } else {
      // No changes, close directly
      handleClose();
    }
  };

  // Function to handle actual closing
  const handleClose = () => {
    reset();
    setSelectedInputType("");
    setIsLoading(false);
    setShowConfirmation(false);
    setShowModal(false);
  };

  const onSubmit = async (data: FieldValues) => {
    if (isLoading) return; // Prevent double submission

    // If it's a table question, let the TableQuestionBuilder handle it
    if (selectedInputType === "table") {
      // The TableQuestionBuilder component will handle its own submission
      // through its handleSubmit function
      return;
    }

    setIsLoading(true);

    const dataToSend = {
      label: data.label,
      isRequired,
      hint: data.hint,
      placeholder: data.placeholder,
      inputType: selectedInputType,
      questionOptions: data.questionOptions,
    };

    questionMutation.mutate({ contextType, contextId, dataToSend, position });
  };

  return (
    <>
      <Modal
        isOpen={showModal}
        onClose={handleCloseWithConfirmation}
        className="w-11/12 tablet:w-4/5 desktop:w-4/5 bg-neutral-100 rounded-lg p-6"
      >
        <h1 className="heading-text capitalize mb-4">Add question</h1>

        {questionMutation.isPending && <LoadingOverlay />}
        <FormProvider {...methods}>
          <form
            className="space-y-4 max-h-[500px] w-full  overflow-y-auto p-4 "
            onSubmit={handleSubmit(onSubmit)}
          >
            <div className="label-input-group group ">
              <input
                {...register("label", {
                  required: "Question name is required",
                })}
                className="input-field"
                placeholder="Enter the question"
              />
              {errors.label && (
                <p className="text-sm text-red-500">{`${errors.label.message}`}</p>
              )}
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="label-input-group group">
                <label htmlFor="question-type" className="label-text">
                  Input Type
                </label>
                <div className="mt-1">
                  <Select
                    id="question-type"
                    options={Object.values(InputTypeMap)}
                    value={
                      selectedInputType && InputTypeMap[selectedInputType]
                        ? InputTypeMap[selectedInputType]
                        : "Select an option"
                    }
                    onChange={(label) => {
                      const selectedKey = labelToKey(label, InputTypeMap);
                      setSelectedInputType(selectedKey ?? "");
                    }}
                  />
                </div>
              </div>
              <div className="flex items-end">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="required"
                    checked={isRequired}
                    onCheckedChange={() => setIsRequired((prev) => !prev)}
                    className="data-[state=checked]:bg-primary-500 data-[state=unchecked]:bg-neutral-100 border border-primary-500"
                  />
                  <label htmlFor="required" className="label-text">
                    Required
                  </label>
                </div>
              </div>
              {errors.inputType && (
                <p className="text-sm text-red-500">{`${errors.inputType.message}`}</p>
              )}
            </div>

            <div className="label-input-group group">
              <label htmlFor="hint" className="label-text">
                Help text
              </label>
              <textarea
                {...register("hint")}
                id="hint"
                placeholder="Add a hint or instructions for this question"
                className="input-field resize-none"
              />
            </div>
            <div className="label-input-group group">
              <label htmlFor="placeholder" className="label-text">
                Placeholder text
              </label>
              <input
                {...register("placeholder")}
                id="placeholder"
                placeholder="Add a placeholder for this question"
                className="input-field"
              />
            </div>

            {needsOptions(selectedInputType) && <DynamicOptions />}

            {selectedInputType === "table" && (
              <div className="mt-4">
                <TableQuestionBuilder
                  projectId={contextId}
                  isInModal={true}
                  onTableCreated={(tableId) => {
                    // Close the modal after table creation
                    reset();
                    setSelectedInputType("");
                    setShowModal(false);

                    // Refresh the questions list if a table was created
                    if (tableId !== -1) {
                      queryClient.invalidateQueries({ queryKey });
                    }
                  }}
                />
              </div>
            )}

            <div className="flex items-center justify-end space-x-4">
              <button
                type="button"
                onClick={handleCloseWithConfirmation}
                className="btn-outline"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="btn-primary flex items-center justify-center gap-2"
                onClick={(e) => {
                  if (selectedInputType === "table") {
                    e.preventDefault();
                    // Find the TableQuestionBuilder component and call its handleSubmit function
                    const tableBuilder = document.querySelector(
                      ".table-question-builder"
                    );
                    if (tableBuilder) {
                      // Trigger a custom event that the TableQuestionBuilder will listen for
                      tableBuilder.dispatchEvent(
                        new CustomEvent("submitTable")
                      );
                    }
                  }
                }}
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <span className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full" />
                    Saving...
                  </>
                ) : (
                  "Save"
                )}
              </button>
            </div>
          </form>
        </FormProvider>
      </Modal>

      {/* Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={showConfirmation}
        onConfirm={handleClose}
        onCancel={() => {
          setShowConfirmation(false);
        }}
      />
    </>
  );
};

export { AddQuestionModal };

import { Prisma, PrismaClient, Project, Sector, Status } from "@prisma/client";
import { prisma } from "../utils/prisma";

type ProjectWithUser = {
  id: number;
  name: string;
  description: string;
  sector: string;
  user: {
    id: number;
    name: string;
    email: string;
    // Add any other fields from user that you expect to access
  };
  lastDeployedAt: Date | null;
  lastSubmissionAt: Date | null;
  createdAt: Date;
  updatedAt: Date;
};

class ProjectRepository {
  async findByName(name: string, userId: number): Promise<Project | null> {
    return await prisma.project.findUnique({
      where: {
        name_userId: {
          name,
          userId,
        },
      },
    });
  }

  async findById(id: number): Promise<ProjectWithUser | null> {
    return await prisma.project.findUnique({
      where: { id },
      select: {
        id: true,
        name: true,
        description: true,
        sector: true,
        user: true,
        lastDeployedAt: true,
        lastSubmissionAt: true,
        createdAt: true,
        updatedAt: true,
      },
    });
  }

  async findProjectByIdAndUser(
    id: number,
    userId: number
  ): Promise<Partial<Project> | null> {
    return await prisma.project.findFirst({
      where: {
        id,
        OR: [
          { userId: userId },
          {
            projectUser: {
              some: { userId },
            },
          },
        ],
      },
      include: {
        user: true,
        questions: true,
        projectUser: {
          where: { userId },
          select: {
            permission: true,
          },
        },
      },
    });
  }

  async findAll(id: number): Promise<Project[]> {
    return await prisma.project.findMany({
      where: {
        OR: [
          { userId: id },
          {
            projectUser: {
              some: { userId: id },
            },
          },
        ],
      },
    });
  }

  async create(projectData: {
    name: string;
    description: string;
    sector: Sector;
    userId: number;
    country?: string;
  }): Promise<Project> {
    const { name, description, sector, userId, country } = projectData;

    return await prisma.project.create({
      data: {
        name,
        description,
        sector,
        userId,
        country,
      },
    });
  }

  async updateById(
    id: number,
    updateData: {
      name?: string;
      description?: string;
      sector?: Sector;
      country?: string;
    }
  ): Promise<Partial<Project> | null> {
    const data: {
      name?: string;
      description?: string;
      sector?: Sector;
      country?: string;
    } = {};

    if (updateData.name !== undefined) data.name = updateData.name;
    if (updateData.description !== undefined)
      data.description = updateData.description;
    if (updateData.sector !== undefined) data.sector = updateData.sector;
    if (updateData.country !== undefined) data.country = updateData.country;

    return await prisma.project.update({
      where: { id },
      data,
    });
  }

  async deleteProject(id: number): Promise<Project> {
    return await prisma.project.delete({
      where: { id },
    });
  }

  async changeProjectStatus(id: number, status: Status): Promise<Project> {
    return await prisma.project.update({
      where: { id },
      data: {
        status: status,
      },
    });
  }

  async changeManyProjectStatus(
    ids: number[],
    status: Status
  ): Promise<Prisma.BatchPayload> {
    return await prisma.project.updateMany({
      where: { id: { in: ids } },
      data: { status },
    });
  }

  async deleteMultipleProject(ids: number[]): Promise<Prisma.BatchPayload> {
    return await prisma.project.deleteMany({
      where: { id: { in: ids } },
    });
  }

  async fetchQuestionGroupWithQuestion(id: number): Promise<Project | null> {
    return await prisma.project.findUnique({
      where: { id },
      include: {
        questionGroup: {
          include: {
            question: {
              include: {
                questionOptions: true,
                questionConditions: true,
              },
              orderBy: { position: "asc" },
            },
            subGroups: {
              include: {
                question: {
                  include: {
                    questionConditions: true,
                    questionOptions: true,
                  },
                  orderBy: { position: "asc" },
                },
              },
            },
          },
          orderBy: { order: "asc" },
        },
        questions: {
          where: { questionGroupId: null },
          include: {
            questionOptions: true,
            questionGroup: true,
          },
          orderBy: { position: "asc" },
        },
      },
    });
  }
}

export default new ProjectRepository();

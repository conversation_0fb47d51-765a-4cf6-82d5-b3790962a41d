import { useQuery } from "@tanstack/react-query";
import axios from "@/lib/axios";
import { Project } from "@/types";

const fetchProjects = async () => {
  const { data } = await axios.get(`/projects`);
  return data.projects;
};

export const useProjects = (status?: string) => {
  const {
    data: projects,
    isLoading,
    error,
  } = useQuery<Project[]>({
    queryKey: ["projects", status],
    queryFn: fetchProjects,
    select: (data) => {
      if (status) {
        return data.filter((project) => project.status === status);
      }
      return data;
    },
  });

  return {
    projects,
    isLoading,
    error,
  };
};

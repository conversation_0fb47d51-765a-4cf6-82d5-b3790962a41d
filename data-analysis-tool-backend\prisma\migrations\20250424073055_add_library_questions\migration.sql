-- CreateTable
CREATE TABLE "LibraryQuestion" (
    "id" SERIAL NOT NULL,
    "libraryTemplateId" INTEGER NOT NULL,
    "label" TEXT NOT NULL,
    "inputType" "InputType" NOT NULL,
    "hint" TEXT NOT NULL,
    "placeholder" TEXT NOT NULL,
    "isRequired" BOOLEAN NOT NULL DEFAULT false,
    "position" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "LibraryQuestion_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "LibraryQuestionOption" (
    "id" SERIAL NOT NULL,
    "label" TEXT NOT NULL,
    "libraryQuestionId" INTEGER NOT NULL,
    "nextLibraryQuestionId" INTEGER,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "LibraryQuestionOption_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "LibraryQuestion" ADD CONSTRAINT "LibraryQuestion_libraryTemplateId_fkey" FOREIGN KEY ("libraryTemplateId") REFERENCES "LibraryTemplate"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "LibraryQuestionOption" ADD CONSTRAINT "LibraryQuestionOption_libraryQuestionId_fkey" FOREIGN KEY ("libraryQuestionId") REFERENCES "LibraryQuestion"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "LibraryQuestionOption" ADD CONSTRAINT "LibraryQuestionOption_nextLibraryQuestionId_fkey" FOREIGN KEY ("nextLibraryQuestionId") REFERENCES "LibraryQuestion"("id") ON DELETE SET NULL ON UPDATE CASCADE;

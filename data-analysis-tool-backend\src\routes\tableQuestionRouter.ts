import express, { RequestHand<PERSON> } from "express";
import {
  createTableQuestion,
  getTableQuestion,
  saveCellValues,
  deleteTableQuestion,
  updateTableQuestion,
  getTableQuestionsByProject,
} from "../controllers/tableQuestionController";
import { authenticate } from "../middleware/auth";

/**
 * Router for table question endpoints
 * Handles CRUD operations for table questions and related data
 *
 * Supports:
 * - Creating table questions with parent-child column relationships
 * - Retrieving table questions with their columns and rows
 * - Updating table questions while maintaining parent-child relationships
 * - Deleting table questions
 * - Saving cell values
 * - Getting all table questions for a project
 */
const router = express.Router();

// Apply authentication middleware to all routes
router.use(authenticate);

// Create a new table question
router.post("/", createTableQuestion as unknown as RequestHand<PERSON>);

// Get a table question by ID
router.get("/:id", getTableQuestion as unknown as RequestHand<PERSON>);

// Update a table question
router.patch("/:id", updateTableQuestion as unknown as <PERSON><PERSON><PERSON><PERSON><PERSON>);

// Save cell values
router.post("/cells", saveCellValues as unknown as <PERSON><PERSON><PERSON><PERSON><PERSON>);

// Delete a table question
router.delete("/:id", deleteTableQuestion as unknown as RequestHandler);

// Get all table questions for a project
// Note: This route must be defined after the /:id route to avoid conflicts
router.get(
  "/project/:projectId",
  getTableQuestionsByProject as unknown as RequestHandler
);

export default router;

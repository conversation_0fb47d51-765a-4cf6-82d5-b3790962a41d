-- CreateTable
CREATE TABLE "LibraryQuestionBlockQuestion" (
    "id" SERIAL NOT NULL,
    "label" TEXT NOT NULL,
    "inputType" "InputType" NOT NULL,
    "hint" TEXT,
    "placeholder" TEXT,
    "isRequired" BOOLEAN NOT NULL DEFAULT false,
    "position" INTEGER NOT NULL,
    "userId" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "libraryQuestionBlockQuestionGroupId" INTEGER,

    CONSTRAINT "LibraryQuestionBlockQuestion_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "LibraryQuestionBlockQuestionOption" (
    "id" SERIAL NOT NULL,
    "label" TEXT NOT NULL,
    "code" TEXT NOT NULL,
    "nextLibraryQuestionId" INTEGER,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "libraryQuestionBlockQuestionId" INTEGER NOT NULL,

    CONSTRAINT "LibraryQuestionBlockQuestionOption_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "LibraryQuestionBlockQuestionCondition" (
    "id" SERIAL NOT NULL,
    "operator" "Operator" NOT NULL,
    "value" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "libraryQuestionBlockQuestionId" INTEGER NOT NULL,

    CONSTRAINT "LibraryQuestionBlockQuestionCondition_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "LibraryQuestionBlockQuestionGroup" (
    "id" SERIAL NOT NULL,
    "title" TEXT NOT NULL,
    "order" SERIAL NOT NULL,
    "parentGroupId" INTEGER,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "LibraryQuestionBlockQuestionGroup_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "LibraryQuestionBlockQuestion" ADD CONSTRAINT "LibraryQuestionBlockQuestion_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "LibraryQuestionBlockQuestion" ADD CONSTRAINT "LibraryQuestionBlockQuestion_libraryQuestionBlockQuestionG_fkey" FOREIGN KEY ("libraryQuestionBlockQuestionGroupId") REFERENCES "LibraryQuestionBlockQuestionGroup"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "LibraryQuestionBlockQuestionOption" ADD CONSTRAINT "LibraryQuestionBlockQuestionOption_libraryQuestionBlockQue_fkey" FOREIGN KEY ("libraryQuestionBlockQuestionId") REFERENCES "LibraryQuestionBlockQuestion"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "LibraryQuestionBlockQuestionOption" ADD CONSTRAINT "LibraryQuestionBlockQuestionOption_nextLibraryQuestionId_fkey" FOREIGN KEY ("nextLibraryQuestionId") REFERENCES "LibraryQuestionBlockQuestion"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "LibraryQuestionBlockQuestionCondition" ADD CONSTRAINT "LibraryQuestionBlockQuestionCondition_libraryQuestionBlock_fkey" FOREIGN KEY ("libraryQuestionBlockQuestionId") REFERENCES "LibraryQuestionBlockQuestion"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "LibraryQuestionBlockQuestionGroup" ADD CONSTRAINT "LibraryQuestionBlockQuestionGroup_parentGroupId_fkey" FOREIGN KEY ("parentGroupId") REFERENCES "LibraryQuestionBlockQuestionGroup"("id") ON DELETE SET NULL ON UPDATE CASCADE;

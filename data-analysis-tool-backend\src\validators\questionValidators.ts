import { InputType, Operator } from "@prisma/client";
import { z } from "zod";

export const questionConditionSchema = z.object({
  id: z.number().optional(),
  operator: z.nativeEnum(Operator, {
    errorMap: () => ({ message: "Invalid operator selected" }),
  }),
  value: z.string().min(1, "Condition value is required"),
});

export const questionSchema = z
  .object({
    label: z.string(),
    inputType: z.nativeEnum(InputType),
    isRequired: z.boolean().optional(),
    hint: z.string().optional(),
    placeholder: z.string().optional(),
    position: z.number(),
    questionOptions: z
      .array(
        z.object({
          id: z.number().optional(),
          label: z.string(),
          sublabel: z.string().optional(),
          code: z.string(),
          nextQuestionId: z.number().optional().nullable(),
        })
      )
      .optional(),
    conditions: z.array(questionConditionSchema).optional().nullable(),
  })
  .superRefine((data, ctx) => {
    if (
      (data.inputType === InputType.selectone ||
        data.inputType === InputType.selectmany) &&
      (!data.questionOptions || data.questionOptions.length === 0)
    ) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        path: ["questionOptions"],
        message: "Options are required for select input types.",
      });
    }
  });

export const questionPositionsSchema = z.object({
  questionPositions: z.array(
    z.object({
      id: z.number(),
      position: z.number(),
    })
  ),
});

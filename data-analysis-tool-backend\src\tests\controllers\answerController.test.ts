import { Request, Response } from "express";
import {
  createAnswer,
  getAnswersBySubmission,
  getAnswerById,
  updateAnswer,
  deleteAnswer,
} from "../../controllers/answerController";
import answerRepository from "../../repositories/answerRepository";

// Mock the dependencies
jest.mock("../../repositories/answerRepository");
jest.mock("../../utils/validateAnswer", () => ({
  validateInput: jest.fn().mockReturnValue({ valid: true }),
}));

describe("Answer Controller", () => {
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;
  let responseObject: any = {};

  beforeEach(() => {
    // Reset mocks before each test
    jest.clearAllMocks();
    jest.resetAllMocks();

    // Setup mock response
    mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockImplementation((result) => {
        responseObject = result;
        return mockResponse;
      }),
    };

    // Reset response object
    responseObject = {};
  });

  describe("createAnswer", () => {
    beforeEach(() => {
      // Setup request for tests
      mockRequest = {
        body: {
          submissionId: 1,
          questionId: 2,
          value: "Test answer",
          answerType: "text",
          questionOptionId: 1, // Changed from null to a number to pass validation
        },
      };
    });

    it("should create an answer successfully", async () => {
      // Mock repository response
      const mockAnswer = {
        id: 1,
        formSubmissionId: 1,
        questionId: 2,
        value: "Test answer",
        answerType: "text",
        questionOptionId: 1, // Changed from null to match the request
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      // Mock the validation
      const validateInput = require("../../utils/validateAnswer").validateInput;
      (validateInput as jest.Mock).mockReturnValue({ valid: true });

      // Mock repository methods - use direct assignment for more reliable mocking
      const findBySubmissionIdQuestionMock = jest.fn().mockResolvedValue(null);
      answerRepository.findBySubmissionIdQuestion =
        findBySubmissionIdQuestionMock;

      const createAnswerMock = jest.fn().mockResolvedValue(mockAnswer);
      answerRepository.createAnswer = createAnswerMock;

      await createAnswer(mockRequest as Request, mockResponse as Response);

      expect(answerRepository.findBySubmissionIdQuestion).toHaveBeenCalledWith(
        1,
        2
      );
      expect(answerRepository.createAnswer).toHaveBeenCalledWith(
        expect.objectContaining({
          submissionId: 1,
          questionId: 2,
          value: "Test answer",
          answerType: "text",
        })
      );
      expect(mockResponse.status).toHaveBeenCalledWith(201);
      expect(responseObject).toHaveProperty("success", true);
      expect(responseObject).toHaveProperty(
        "message",
        "Answer created successfully"
      );
      expect(responseObject.data).toHaveProperty("answer", mockAnswer);
    });

    it("should return 400 when answer already exists", async () => {
      // Mock existing answer
      const findBySubmissionIdQuestionMock = jest.fn().mockResolvedValue({
        id: 1,
        formSubmissionId: 1,
        questionId: 2,
        answerType: "text", // Add answerType to match the controller's check
      });
      answerRepository.findBySubmissionIdQuestion =
        findBySubmissionIdQuestionMock;

      // Make sure validation passes
      const validateInput = require("../../utils/validateAnswer").validateInput;
      (validateInput as jest.Mock).mockReturnValue({ valid: true });

      await createAnswer(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(responseObject).toHaveProperty("success", false);
      expect(responseObject).toHaveProperty(
        "message",
        "Answer already created"
      );
      expect(answerRepository.createAnswer).not.toHaveBeenCalled();
    });

    it("should handle selectmany answers correctly", async () => {
      // Setup request for selectmany
      mockRequest = {
        body: {
          submissionId: 1,
          questionId: 2,
          value: ["Option 1", "Option 2"],
          answerType: "selectmany",
          questionOptionId: [1, 2],
        },
      };

      // Mock repository responses
      const mockAnswers = [
        {
          id: 1,
          formSubmissionId: 1,
          questionId: 2,
          value: "Option 1",
          answerType: "selectmany",
          questionOptionId: 1,
        },
        {
          id: 2,
          formSubmissionId: 1,
          questionId: 2,
          value: "Option 2",
          answerType: "selectmany",
          questionOptionId: 2,
        },
      ];

      // Make sure validateInput returns valid for this test
      const validateInput = require("../../utils/validateAnswer").validateInput;
      (validateInput as jest.Mock).mockReturnValue({ valid: true });

      (
        answerRepository.findBySubmissionIdQuestion as jest.Mock
      ).mockResolvedValue(null);
      (answerRepository.createAnswer as jest.Mock)
        .mockResolvedValueOnce(mockAnswers[0])
        .mockResolvedValueOnce(mockAnswers[1]);

      await createAnswer(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(201);
      expect(responseObject).toHaveProperty("success", true);
      expect(responseObject).toHaveProperty(
        "message",
        "Multiple answers created successfully"
      );
      expect(responseObject.data).toHaveProperty("answers");
    });

    it("should return 400 for invalid selectmany input (mismatched arrays)", async () => {
      // Setup request with mismatched arrays
      mockRequest = {
        body: {
          submissionId: 1,
          questionId: 2,
          value: ["Option 1", "Option 2"],
          answerType: "selectmany",
          questionOptionId: [1], // Only one option ID but two values
        },
      };

      // Make sure validateInput returns valid for this test
      const validateInput = require("../../utils/validateAnswer").validateInput;
      (validateInput as jest.Mock).mockReturnValue({ valid: true });

      await createAnswer(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(responseObject).toHaveProperty("success", false);
      expect(responseObject).toHaveProperty(
        "message",
        "`value` and `questionOptionId` arrays must be the same length"
      );
    });

    it("should return 400 for invalid input", async () => {
      // Missing required fields
      mockRequest.body = { submissionId: 1 };

      await createAnswer(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(responseObject).toHaveProperty("errors");
      expect(answerRepository.createAnswer).not.toHaveBeenCalled();
    });

    it("should handle server errors", async () => {
      // Mock a server error
      const findBySubmissionIdQuestionMock = jest
        .fn()
        .mockRejectedValue(new Error("Database error"));
      answerRepository.findBySubmissionIdQuestion =
        findBySubmissionIdQuestionMock;

      // Make sure validation passes
      const validateInput = require("../../utils/validateAnswer").validateInput;
      (validateInput as jest.Mock).mockReturnValue({ valid: true });

      await createAnswer(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(responseObject).toHaveProperty("success", false);
      expect(responseObject).toHaveProperty("message", "Error creating answer");
    });
  });

  describe("getAnswersBySubmission", () => {
    beforeEach(() => {
      mockRequest = {
        body: {
          submissionId: "1", // Note: string format to test parsing
        },
      };
    });

    it("should get answers by submission ID successfully", async () => {
      const mockAnswers = [
        {
          id: 1,
          formSubmissionId: 1,
          questionId: 2,
          value: "Answer 1",
        },
        {
          id: 2,
          formSubmissionId: 1,
          questionId: 3,
          value: "Answer 2",
        },
      ];

      (answerRepository.getAnswersBySubmission as jest.Mock).mockResolvedValue(
        mockAnswers
      );

      await getAnswersBySubmission(
        mockRequest as Request,
        mockResponse as Response
      );

      expect(answerRepository.getAnswersBySubmission).toHaveBeenCalledWith(1);
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(responseObject).toHaveProperty("success", true);
      expect(responseObject.data).toHaveProperty("answers", mockAnswers);
    });

    it("should handle server errors", async () => {
      (answerRepository.getAnswersBySubmission as jest.Mock).mockRejectedValue(
        new Error("Database error")
      );

      await getAnswersBySubmission(
        mockRequest as Request,
        mockResponse as Response
      );

      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(responseObject).toHaveProperty("success", false);
      expect(responseObject).toHaveProperty("message", "error getting answer");
    });
  });

  describe("getAnswerById", () => {
    beforeEach(() => {
      mockRequest = {
        params: {
          id: "1", // Note: string format to test parsing
        },
      };
    });

    it("should get answer by ID successfully", async () => {
      const mockAnswer = {
        id: 1,
        formSubmissionId: 1,
        questionId: 2,
        value: "Test answer",
      };

      (answerRepository.getAnswerById as jest.Mock).mockResolvedValue(
        mockAnswer
      );

      await getAnswerById(mockRequest as Request, mockResponse as Response);

      expect(answerRepository.getAnswerById).toHaveBeenCalledWith(1);
      expect(mockResponse.status).toHaveBeenCalledWith(201);
      expect(responseObject).toHaveProperty("success", true);
      expect(responseObject.data).toHaveProperty("answer", mockAnswer);
    });

    it("should return 404 when answer not found", async () => {
      (answerRepository.getAnswerById as jest.Mock).mockResolvedValue(null);

      await getAnswerById(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(404);
      expect(responseObject).toHaveProperty("message", "Answer not found");
    });

    it("should handle server errors", async () => {
      (answerRepository.getAnswerById as jest.Mock).mockRejectedValue(
        new Error("Database error")
      );

      await getAnswerById(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(responseObject).toHaveProperty("success", false);
      expect(responseObject).toHaveProperty("message", "error getting answer");
    });
  });

  describe("updateAnswer", () => {
    beforeEach(() => {
      mockRequest = {
        body: {
          submissionId: 1,
          questionId: 2,
          value: "Updated answer",
          answerType: "text",
          questionOptionId: 1, // Changed from null to a number to pass validation
        },
      };
    });

    it("should update a single answer successfully", async () => {
      // Mock repository response
      const updatedAnswer = {
        id: 1,
        formSubmissionId: 1,
        questionId: 2,
        value: "Updated answer",
        answerType: "text",
        questionOptionId: 1, // Changed from null to match the request
        updatedAt: new Date(),
      };

      // Make sure validation passes
      const validateInput = require("../../utils/validateAnswer").validateInput;
      (validateInput as jest.Mock).mockReturnValue({ valid: true });

      // Use direct assignment for more reliable mocking
      const updateSingleAnswerMock = jest.fn().mockResolvedValue(updatedAnswer);
      answerRepository.updateSingleAnswer = updateSingleAnswerMock;

      await updateAnswer(mockRequest as Request, mockResponse as Response);

      expect(answerRepository.updateSingleAnswer).toHaveBeenCalledWith(
        expect.objectContaining({
          submissionId: 1,
          questionId: 2,
          value: "Updated answer",
          answerType: "text",
        })
      );

      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(responseObject).toHaveProperty("success", true);
      expect(responseObject).toHaveProperty(
        "message",
        "Answer updated successfully"
      );
      expect(responseObject.data).toHaveProperty("answer", updatedAnswer);
    });

    it("should update selectmany answers correctly", async () => {
      // Setup request for selectmany
      mockRequest = {
        body: {
          submissionId: 1,
          questionId: 2,
          value: ["Updated Option 1", "Updated Option 2"],
          answerType: "selectmany",
          questionOptionId: [1, 2],
        },
      };

      const mockUpdatedAnswers = [
        {
          id: 1,
          formSubmissionId: 1,
          questionId: 2,
          value: "Updated Option 1",
          answerType: "selectmany",
          questionOptionId: 1,
        },
        {
          id: 2,
          formSubmissionId: 1,
          questionId: 2,
          value: "Updated Option 2",
          answerType: "selectmany",
          questionOptionId: 2,
        },
      ];

      // Make sure validation passes
      const validateInput = require("../../utils/validateAnswer").validateInput;
      (validateInput as jest.Mock).mockReturnValue({ valid: true });

      (answerRepository.updateSelectManyAnswers as jest.Mock).mockResolvedValue(
        mockUpdatedAnswers
      );

      await updateAnswer(mockRequest as Request, mockResponse as Response);

      expect(answerRepository.updateSelectManyAnswers).toHaveBeenCalledWith(
        1,
        2,
        ["Updated Option 1", "Updated Option 2"],
        [1, 2]
      );
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(responseObject).toHaveProperty("success", true);
      expect(responseObject).toHaveProperty(
        "message",
        "select_many answers updated"
      );
    });

    it("should return 400 for invalid input", async () => {
      // Missing required fields
      mockRequest.body = { submissionId: 1 };

      await updateAnswer(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(responseObject).toHaveProperty("errors");
      expect(answerRepository.updateSingleAnswer).not.toHaveBeenCalled();
    });

    it("should handle server errors", async () => {
      // Mock server error
      const updateSingleAnswerMock = jest
        .fn()
        .mockRejectedValue(new Error("Database error"));
      answerRepository.updateSingleAnswer = updateSingleAnswerMock;

      // Make sure validation passes
      const validateInput = require("../../utils/validateAnswer").validateInput;
      (validateInput as jest.Mock).mockReturnValue({ valid: true });

      await updateAnswer(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(responseObject).toHaveProperty("success", false);
      expect(responseObject).toHaveProperty("message", "Error updating answer");
    });

    it("should return 400 for invalid selectmany input (not an array)", async () => {
      // Setup request with invalid value (not an array)
      mockRequest = {
        body: {
          submissionId: 1,
          questionId: 2,
          value: "Not an array",
          answerType: "selectmany",
          questionOptionId: [1, 2],
        },
      };

      // Mock validateInput to return valid for this test
      const validateInput = require("../../utils/validateAnswer").validateInput;
      (validateInput as jest.Mock).mockReturnValue({ valid: true });

      await updateAnswer(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(responseObject).toHaveProperty(
        "message",
        "`value` must be an array for select_many"
      );
    });
  });

  describe("deleteAnswer", () => {
    beforeEach(() => {
      mockRequest = {
        body: {
          submissionId: 1,
          questionId: 2,
          answerType: "text",
        },
      };
    });

    it("should delete an answer successfully", async () => {
      const mockDeletedAnswer = { id: 1 };

      (
        answerRepository.deleteAnswersBySubmissionIdAndQuestionId as jest.Mock
      ).mockResolvedValue(mockDeletedAnswer);

      await deleteAnswer(mockRequest as Request, mockResponse as Response);

      expect(
        answerRepository.deleteAnswersBySubmissionIdAndQuestionId
      ).toHaveBeenCalledWith(1, 2, "text");
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(responseObject).toHaveProperty("success", true);
      expect(responseObject).toHaveProperty(
        "message",
        "Answer(s) deleted successfully"
      );
      expect(responseObject.data).toEqual(mockDeletedAnswer);
    });

    it("should return 400 for invalid input", async () => {
      // Missing required fields
      mockRequest.body = { submissionId: "not a number" };

      await deleteAnswer(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(responseObject).toHaveProperty("success", false);
      expect(responseObject).toHaveProperty(
        "message",
        "submissionId, questionId and answerType are required"
      );
      expect(
        answerRepository.deleteAnswersBySubmissionIdAndQuestionId
      ).not.toHaveBeenCalled();
    });

    it("should handle server errors", async () => {
      (
        answerRepository.deleteAnswersBySubmissionIdAndQuestionId as jest.Mock
      ).mockRejectedValue(new Error("Database error"));

      await deleteAnswer(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(responseObject).toHaveProperty("success", false);
      expect(responseObject).toHaveProperty("message", "Error deleting answer");
    });

    it("should handle when no answer is found to delete", async () => {
      (
        answerRepository.deleteAnswersBySubmissionIdAndQuestionId as jest.Mock
      ).mockResolvedValue(null);

      await deleteAnswer(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(responseObject).toHaveProperty("success", true);
      expect(responseObject).toHaveProperty(
        "message",
        "Answer(s) deleted successfully"
      );
    });
  });
});

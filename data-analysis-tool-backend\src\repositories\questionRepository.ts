import {
  InputType,
  Operator,
  Question,
  QuestionCondition,
  QuestionOption,
} from "@prisma/client";
import { prisma } from "../utils/prisma";

class QuestionRepository {
  async findById(id: number): Promise<
    | (Question & {
        questionOptions: QuestionOption[];
        questionConditions: QuestionCondition[];
      })
    | null
  > {
    return await prisma.question.findUnique({
      where: {
        id,
      },
      include: {
        questionOptions: true,
        questionConditions: true,
      },
    });
  }

  async isPorjectOwner(userId: number, projectId: number): Promise<boolean> {
    const project = await prisma.project.findUnique({
      where: {
        id: projectId,
      },
      select: { userId: true },
    });

    return !!project && project.userId === userId;
  }

  async findAll(projectId?: number): Promise<
    (Question & {
      questionOptions: QuestionOption[];
      questionConditions: QuestionCondition[];
    })[]
  > {
    return await prisma.question.findMany({
      where: projectId ? { projectId } : undefined,
      include: {
        questionOptions: true,
        questionConditions: true,
      },
      orderBy: { position: "asc" },
    });
  }

  async create(questionData: {
    projectId: number;
    label: string;
    inputType: InputType;
    hint?: string;
    placeholder?: string;
    isRequired?: boolean;
    position: number;
    questionOptions?: {
      label: string;
      sublabel:string;
      code: string;
      nextQuestionId?: number | null;
    }[];
    conditions?: {
      operator: Operator;
      value: string;
    }[];
  }): Promise<Question> {
    const {
      projectId,
      label,
      inputType,
      hint,
      placeholder,
      isRequired,
      position,
      questionOptions,
      conditions,
    } = questionData;

    return await prisma.$transaction(async (tx) => {
      // Create the question
      const question = await tx.question.create({
        data: {
          projectId,
          label,
          inputType,
          hint: hint ?? "", // fallback to empty string
          placeholder: placeholder ?? "",
          isRequired,
          position,
        },
        include: {
          questionOptions: true,
          questionConditions: true,
        },
      });

      // Create options if provided
      if (questionOptions && questionOptions.length > 0) {
        await tx.questionOption.createMany({
          data: questionOptions.map((option) => ({
            label: option.label,
            sublabel:option.sublabel,
            code: option.code,
            questionId: question.id,
            nextQuestionId: option.nextQuestionId || null,
          })),
        });
      }

      // Create conditions if provided
      if (conditions && conditions.length > 0) {
        await tx.questionCondition.createMany({
          data: conditions.map((condition) => ({
            operator: condition.operator,
            value: condition.value,
            questionId: question.id,
          })),
        });
      }

      // Return the created question with options and conditions
      return question;
    });
  }

  async updateById(
    id: number,
    updateData: {
      label?: string;
      inputType?: InputType;
      hint?: string;
      placeholder?: string;
      isRequired?: boolean;
      position?: number;
      options?: {
        id?: number;
        label: string;
        sublabel?: string;
        code: string;
        nextQuestionId?: number | null;
      }[];
      conditions?: {
        id?: number;
        operator: Operator;
        value: string;
      }[];
    }
  ) {
    const { options, conditions, ...rest } = updateData;

    const questionData = {
      label: rest.label,
      inputType: rest.inputType,
      hint: rest.hint,
      placeholder: rest.placeholder,
      isRequired: rest.isRequired,
      position: rest.position,
    };

    return await prisma.$transaction(async (tx) => {
      // Update the question basic data
      const updatedQuestion = await tx.question.update({
        where: { id },
        data: questionData,
      });

      // Handle options if provided
      if (options) {
        console.log("handleing option ");
        const existingOptions = await tx.questionOption.findMany({
          where: { questionId: id },
        });

        // Identify options to add/update/delete
        const existingIds = existingOptions.map((o) => o.id);

        const updatedIds = options
          .filter((o) => o.id)
          .map((o) => o.id as number);
        const idsToDelete = existingIds.filter(
          (id) => !updatedIds.includes(id)
        );

        // Delete removed options
        if (idsToDelete.length > 0) {
          await tx.questionOption.deleteMany({
            where: { id: { in: idsToDelete } },
          });
        }

        // Add new options and update existing ones
        for (const option of options) {
          if (option.id) {
            // Update existing option
            await tx.questionOption.update({
              where: { id: option.id },
              data: {
                label: option.label,
                sublabel: option.sublabel || "",
                code: option.code,
                nextQuestionId: option.nextQuestionId || null,
              },
            });
          } else {
            // Create new option
            await tx.questionOption.create({
              data: {
                label: option.label,
                sublabel: option.sublabel || "",
                code: option.code,
                questionId: id,
                nextQuestionId: option.nextQuestionId || null,
              },
            });
          }
        }
      }

      // Handle conditions if provided
      if (conditions) {
        const existingConditions = await tx.questionCondition.findMany({
          where: { questionId: id },
        });

        // Identify conditions to add/update/delete
        const existingIds = existingConditions.map((c) => c.id);
        const updatedIds = conditions
          .filter((c) => c.id)
          .map((c) => c.id as number);
        const idsToDelete = existingIds.filter(
          (id) => !updatedIds.includes(id)
        );

        // Delete removed conditions
        if (idsToDelete.length > 0) {
          await tx.questionCondition.deleteMany({
            where: { id: { in: idsToDelete } },
          });
        }

        // Add new conditions and update existing ones
        for (const condition of conditions) {
          if (condition.id) {
            // Check if condition exists and belongs to this question
            const existingCondition = await tx.questionCondition.findUnique({
              where: {
                id: condition.id,
              },
            });

            // Only update if condition exists and belongs to this question
            if (existingCondition && existingCondition.questionId === id) {
              // Update existing condition
              await tx.questionCondition.update({
                where: { id: condition.id },
                data: {
                  operator: condition.operator,
                  value: condition.value,
                },
              });
            } else {
              // Create new condition if ID doesn't exist
              await tx.questionCondition.create({
                data: {
                  operator: condition.operator,
                  value: condition.value,
                  questionId: id,
                },
              });
            }
          } else {
            // Create new condition
            await tx.questionCondition.create({
              data: {
                operator: condition.operator,
                value: condition.value,
                questionId: id,
              },
            });
          }
        }
      }

      // Return the updated question with options and conditions
      return (await tx.question.findUnique({
        where: { id },
        include: {
          questionOptions: true,
          questionConditions: true,
        },
      })) as Question & {
        questionOptions: QuestionOption[];
        questionConditions: QuestionCondition[];
      };
    });
  }

  async deleteQuestion(id: number): Promise<Question> {
    return await prisma.question.delete({
      where: { id },
    });
  }

  async duplicateQuestion(
    id: number,
    projectId: number
  ): Promise<Question | null> {
    // Find the question to duplicate
    const question = await prisma.question.findUnique({
      where: { id },
    });

    if (!question) {
      return null;
    }

    const duplicatedQuestion = await prisma.question.create({
      data: {
        ...question,
        id: undefined, // Reset the ID to create a new question
        projectId, // Set the new project ID
      },
      include: {
        questionOptions: true,
        questionConditions: true,
      },
    });

    return duplicatedQuestion;
  }

  async updateMultiplePositions(
    questionPositions: { id: number; position: number }[]
  ): Promise<Question[]> {
    return await prisma.$transaction(async (tx) => {
      const updatedQuestions: Question[] = [];

      for (const { id, position } of questionPositions) {
        const updatedQuestion = await tx.question.update({
          where: { id },
          data: { position },
          include: {
            questionOptions: true,
            questionConditions: true,
          },
        });
        updatedQuestions.push(updatedQuestion);
      }

      return updatedQuestions;
    });
  }
}

export default new QuestionRepository();

{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_9dd07395._.js", "server/edge/chunks/[root-of-the-server]__de110ef0._.js", "server/edge/chunks/edge-wrapper_24e2925a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|images|fonts).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|images|fonts).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "lUJu64f0BENfp9iP7T2/TsE5/VqtGlGCMzTGnjEa7hA=", "__NEXT_PREVIEW_MODE_ID": "94cb6fc3473aee9e06a41ebae23f3217", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "85facadbfd295c2dc5e4ffbd7cb622c55b4832bdc88b005673ea7408765a18c2", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "bbc8cd58d1bdc83619af30559a8b8e8f400cdcbc0032c0812bf3ad5ee91137c9"}}}, "sortedMiddleware": ["/"], "functions": {}}
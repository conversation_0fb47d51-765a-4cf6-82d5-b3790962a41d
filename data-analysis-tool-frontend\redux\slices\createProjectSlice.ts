import { createSlice } from "@reduxjs/toolkit";

const createProjectSlice = createSlice({
  name: "createProject",
  initialState: { visible: false },
  reducers: {
    showCreateProjectModal: (state) => {
      state.visible = true;
    },

    hideCreateProjectModal: (state) => {
      state.visible = false;
    },
  },
});

export const { showCreateProjectModal, hideCreateProjectModal } =
  createProjectSlice.actions;
export default createProjectSlice.reducer;

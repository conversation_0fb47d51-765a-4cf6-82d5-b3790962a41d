"use client";

import { RefreshCw, Search, Filter, Grid, ListFilter } from "lucide-react";
import React, { useState, useEffect } from "react";
import Image from "next/image";

// Real image data with Unsplash images
const realImages = [
  {
    id: 1,
    title: "Mountain Landscape",
    url: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4",
    date: "2023-05-15",
    author: "Unsplash",
  },
  {
    id: 2,
    title: "Ocean Sunset",
    url: "https://images.unsplash.com/photo-1503803548695-c2a7b4a5b875",
    date: "2023-05-16",
    author: "Unsplash",
  },
  {
    id: 3,
    title: "Forest Path",
    url: "https://images.unsplash.com/photo-1448375240586-882707db888b",
    date: "2023-05-17",
    author: "Unsplash",
  },
  {
    id: 4,
    title: "City Skyline",
    url: "https://images.unsplash.com/photo-1514565131-fce0801e5785",
    date: "2023-05-18",
    author: "Unsplash",
  },
  {
    id: 5,
    title: "Desert Dunes",
    url: "https://images.unsplash.com/photo-1509316785289-025f5b846b35",
    date: "2023-05-19",
    author: "Unsplash",
  },
  {
    id: 6,
    title: "Snowy Mountains",
    url: "https://images.unsplash.com/photo-1483728642387-6c3bdd6c93e5",
    date: "2023-05-20",
    author: "Unsplash",
  },
];

export default function GalleryPage() {
  const [searchTerm, setSearchTerm] = useState("");
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [images, setImages] = useState(realImages);
  const [isLoading, setIsLoading] = useState(true);

  // Simulate image loading
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  // Filter images based on search term
  useEffect(() => {
    if (searchTerm) {
      const filtered = realImages.filter((image) =>
        image.title.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setImages(filtered);
    } else {
      setImages(realImages);
    }
  }, [searchTerm]);

  return (
    <div className="flex flex-col space-y-6">
      {/* Header with title and action buttons */}
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-semibold text-neutral-800">Gallery</h1>
        <div className="flex gap-2">
          <button
            className="btn-primary"
            title="Refresh gallery"
            onClick={() => setIsLoading(true)}
          >
            <RefreshCw
              className={`w-4 h-4 ${isLoading ? "animate-spin" : ""}`}
            />
            <span>Refresh</span>
          </button>
        </div>
      </div>

      {/* Search and filter */}
      <div className="flex justify-between gap-4 flex-wrap">
        <div className="flex gap-2">
          <button
            className={`btn-primary ${
              viewMode === "grid"
                ? "bg-primary-600 text-neutral-100"
                : "bg-primary-500 hover:bg-primary-600 text-neutral-100"
            }`}
            onClick={() => setViewMode("grid")}
          >
            <Grid className="w-4 h-4" />
            <span>Grid</span>
          </button>
          <button
            className={`btn-primary ${
              viewMode === "list"
                ? "bg-primary-600 text-neutral-100"
                : "bg-primary-500 hover:bg-primary-600 text-neutral-100"
            }`}
            onClick={() => setViewMode("list")}
          >
            <ListFilter className="w-4 h-4" />
            <span>List</span>
          </button>
          <button className="btn-primary">
            <Filter className="w-4 h-4" />
            <span>Filter</span>
          </button>
        </div>
        <div className="relative">
          <input
            type="text"
            placeholder="Search images..."
            className="pl-9 pr-4 py-2 border border-neutral-300 rounded w-64 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 focus:outline-none transition-colors"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
          <Search className="absolute left-3 top-2.5 w-4 h-4 text-neutral-400" />
        </div>
      </div>

      {/* Loading state */}
      {isLoading ? (
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {[1, 2, 3, 4, 5, 6].map((i) => (
            <div
              key={i}
              className="bg-neutral-100 border border-neutral-200 rounded-md overflow-hidden shadow-sm"
            >
              <div className="aspect-video bg-neutral-200 animate-pulse"></div>
              <div className="p-3">
                <div className="h-4 bg-neutral-200 rounded animate-pulse w-3/4 mb-2"></div>
                <div className="h-3 bg-neutral-200 rounded animate-pulse w-1/2"></div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <>
          {/* Image grid */}
          {viewMode === "grid" ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
              {images.map((image) => (
                <div
                  key={image.id}
                  className="bg-neutral-100 border border-neutral-200 rounded-md overflow-hidden shadow-sm hover:shadow-md transition-shadow"
                >
                  <div className="aspect-video overflow-hidden bg-neutral-100 relative">
                    <Image
                      src={`${image.url}?w=600&h=400&auto=format&fit=crop`}
                      alt={image.title}
                      fill
                      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                      className="object-cover hover:scale-105 transition-transform duration-300"
                    />
                  </div>
                  <div className="p-3">
                    <h3 className="font-medium text-neutral-800">
                      {image.title}
                    </h3>
                    <p className="text-xs text-neutral-500 mt-1">
                      {image.date} • Photo by {image.author}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="bg-neutral-100 border border-neutral-200 rounded-md overflow-hidden shadow-sm">
              <ul className="divide-y divide-neutral-200">
                {images.map((image) => (
                  <li
                    key={image.id}
                    className="flex items-center gap-4 p-3 hover:bg-neutral-50"
                  >
                    <div className="w-20 h-16 bg-neutral-100 overflow-hidden rounded relative">
                      <Image
                        src={`${image.url}?w=160&h=120&auto=format&fit=crop`}
                        alt={image.title}
                        fill
                        sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                        className="object-cover"
                      />
                    </div>
                    <div>
                      <h3 className="font-medium text-neutral-800">
                        {image.title}
                      </h3>
                      <p className="text-xs text-neutral-500 mt-1">
                        {image.date} • Photo by {image.author}
                      </p>
                    </div>
                  </li>
                ))}
              </ul>
            </div>
          )}
        </>
      )}
    </div>
  );
}

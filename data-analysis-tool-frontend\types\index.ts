import { Question } from "./formBuilder";

export type User = {
  id: number;
  name: string;
  email: string;
};

export type Profile = {
  id: number;
  email: string;
  name: string;
  country: string;
  city?: string;
  bio?: string;
  sector: string;
  organizationType: string;
};

export type Session = {
  id: number;
  deviceInfo: string;
  ipAddress: string;
  browserInfo: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
};
export interface ProjectUser {
  userId?: number; // if present
  permission: {
    viewForm?: boolean;
    editForm?: boolean;
    viewSubmissions?: boolean;
    editSubmissions?: boolean;
    addSubmissions?: boolean;
    deleteSubmissions?: boolean;
    validateSubmissions?: boolean;
    manageProject?: boolean;
  };
}

export type ProjectPermissionFlags = {
  viewForm?: boolean;
  editForm?: boolean;
  editSubmissions?: boolean;
  addSubmissions?: boolean;
  deleteSubmissions?: boolean;
  viewSubmissions?: boolean;
  manageProject?: boolean;
};

export type Project = {
  id: number;
  name: string;
  description?: string;
  status: string;
  createdAt: string;
  updatedAt: string;
  userId: number;
  questions?: Question[];
  user: User;
  sector: string;
  lastDeployedAt?: string;
  lastSubmissionAt?: string;
  country: string;
  projectUser?: ProjectUser[];
};

export type Template = {
  id: number;
  name: string;
  userId: number;
  user: User;
  libraryQuestions: TemplateQuestion[];
  description: string;
  sector: string;
  country: string;
  createdAt: string;
  updatedAt: string;
};

export type TemplateQuestion = {
  id: number;
  label: string;
  hint: string;
  isRequired: boolean;
};

// for form builder
export type ContextType = "project" | "template" | "questionBlock";

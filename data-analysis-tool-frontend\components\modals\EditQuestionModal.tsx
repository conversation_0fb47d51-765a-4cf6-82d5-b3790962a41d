import { Question, QuestionSchema } from "@/types/formBuilder";
import React, { useEffect, useState } from "react";
import Modal from "./Modal";
import { FieldValues, FormProvider, useForm } from "react-hook-form";
import { z } from "zod";
import { InputTypeMap } from "@/constants/inputType";
import { needsOptions } from "@/lib/needsOptions";
import { DynamicOptions } from "../form-builder/DynamicOptions";
import { Switch } from "../ui";
import { ContextType, TemplateQuestion } from "@/types";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { updateQuestion } from "@/lib/api/form-builder";
import { showNotification } from "@/redux/slices/notificationSlice";
import { useDispatch } from "react-redux";
import { LoadingOverlay } from "../general/LoadingOverlay";

const EditQuestionModal = ({
  showModal,
  setShowModal,
  contextType,
  question,
  contextId,
}: {
  showModal: boolean;
  setShowModal: React.Dispatch<React.SetStateAction<boolean>>;
  contextType: ContextType;
  question: Question | TemplateQuestion;
  contextId: number;
}) => {
  const methods = useForm<z.infer<typeof QuestionSchema>>({});

  const {
    register,
    formState: { errors, isSubmitted },
    setValue,
    handleSubmit,
    reset,
  } = methods;

  useEffect(() => {
    const result = QuestionSchema.safeParse(question);
    if (result.success) {
      reset(result.data);
      setSelectedInputType(result.data.inputType || "");
    }
  }, [question]);

  const [isRequired, setIsRequired] = useState(false);
  const [selectedInputType, setSelectedInputType] = useState<string>("");

  useEffect(() => {
    register("inputType", { required: "Please select an input type" });
  }, [register]);

  useEffect(() => {
    setValue("inputType", selectedInputType, { shouldValidate: isSubmitted });
  }, [selectedInputType]);

  const queryClient = useQueryClient();
  const dispatch = useDispatch();

  const queryKey =
    contextType === "project"
      ? ["questions"]
      : contextType === "template"
      ? ["templateQuestions"]
      : ["questionBlockQuestions"];

  const updateQuestionMutation = useMutation({
    mutationFn: updateQuestion,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey,
        exact: false,
      });
      dispatch(
        showNotification({
          message: "Successfully updated question",
          type: "success",
        })
      );
      setShowModal(false);
    },
    onError: () => {
      dispatch(
        showNotification({
          message: "Failed to update question",
          type: "error",
        })
      );
    },
  });

  const onSubmit = async (data: FieldValues) => {
    const dataToSend = {
      label: data.label,
      isRequired,
      hint: data.hint,
      placeholder: data.placeholder,
      questionOptions: data.questionOptions,
      // Preserve the original position if it exists (for Question type)
      ...(('position' in question) && { position: question.position }),
    };
    updateQuestionMutation.mutate({
      id: question.id,
      contextType,
      dataToSend,
      contextId,
    });
  };

  return (
    <Modal
      isOpen={showModal}
      onClose={() => {
        setShowModal(false);
      }}
      className="w-11/12 tablet:w-4/5 desktop:w-3/5"
    >
      <h1 className="heading-text capitalize mb-4">Edit question</h1>

      {updateQuestionMutation.isPending && <LoadingOverlay />}
      <FormProvider {...methods}>
        <form
          className="space-y-4 max-h-[500px] overflow-y-auto p-4"
          onSubmit={handleSubmit(onSubmit)}
        >
          <div className="label-input-group group">
            <input
              {...register("label", { required: "Question name is required" })}
              className="input-field"
              placeholder="Enter the question"
            />
            {errors.label && (
              <p className="text-sm text-red-500">{`${errors.label.message}`}</p>
            )}
          </div>
          <div className="grid grid-cols-2 gap-4">
            <div className="label-input-group group">
              <label htmlFor="question-type" className="label-text">
                Input Type
              </label>
              <input
                id="question-type"
                className="input-field bg-gray-100 "
                value={
                  selectedInputType && InputTypeMap[selectedInputType]
                    ? InputTypeMap[selectedInputType]
                    : "N/A"
                }
                disabled
              />
            </div>
            <div className="flex items-end">
              <div className="flex items-center space-x-2">
                <Switch
                  id="required"
                  checked={isRequired}
                  onCheckedChange={() => setIsRequired((prev) => !prev)}
                  className="data-[state=checked]:bg-primary-500 data-[state=unchecked]:bg-neutral-100 border border-primary-500"
                />
                <label htmlFor="required" className="label-text">
                  Required
                </label>
              </div>
            </div>
            {errors.inputType && (
              <p className="text-sm text-red-500">{`${errors.inputType.message}`}</p>
            )}
          </div>

          <div className="label-input-group group">
            <label htmlFor="hint" className="label-text">
              Help text
            </label>
            <textarea
              {...register("hint")}
              id="hint"
              placeholder="Add a hint or instructions for this question"
              className="input-field resize-none"
            />
          </div>
          <div className="label-input-group group">
            <label htmlFor="placeholder" className="label-text">
              Placeholder text
            </label>
            <input
              {...register("placeholder")}
              id="placeholder"
              placeholder="Add a placeholder for this question"
              className="input-field"
            />
          </div>

          {needsOptions(selectedInputType) && (
            <DynamicOptions
              contextType={contextType}
              contextId={contextId}
              currentQuestionId={question.id}
              inputType={selectedInputType}
            />
          )}
          <div className="flex items-center justify-end space-x-4">
            <button
              type="button"
              onClick={() => {
                setShowModal(false);
              }}
              className="btn-outline"
            >
              Cancel
            </button>
            <button onClick={handleSubmit(onSubmit)} className="btn-primary">
              Save Edit
            </button>
          </div>
        </form>
      </FormProvider>
    </Modal>
  );
};

export { EditQuestionModal };

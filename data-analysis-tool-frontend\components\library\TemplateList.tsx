import { useAuth } from "@/hooks/useAuth";
import { deleteTemplates, fetchTemplates } from "@/lib/api/templates";
import { Template } from "@/types";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { Table, VisibilityState } from "@tanstack/react-table";
import React, { useEffect, useState } from "react";
import Spinner from "../general/Spinner";
import { ConfirmationModal } from "../modals/ConfirmationModal";
import { GeneralTable } from "../tables/GeneralTable";
import { ChevronDown, ChevronUp, Trash } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
} from "@/components/ui/dropdown-menu";
import { DropdownMenuTrigger } from "@radix-ui/react-dropdown-menu";
import { TemplateListColumns } from "../tables/columns/TemplateListColumns";

const STORAGE_KEY = "data-table-column-visibility";

const TemplateList = () => {
  const { user } = useAuth();

  const [showConfirmationModal, setShowConfirmationModal] = useState(false);

  const {
    data: templateData,
    isLoading: templateLoading,
    isError: templateError,
  } = useQuery<Template[]>({
    queryKey: ["templates", user?.id],
    queryFn: fetchTemplates,
    enabled: !!user?.id,
  });

  const [templateFilter, setTemplateFilter] = useState<string>("");
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [tableInstance, setTableInstance] = useState<Table<Template> | null>(
    null
  );
  const [isDropdownOpen, setIsDropdownOpen] = useState<boolean>(false);
  const [templateRowSelection, setTemplateRowSelection] = useState({});

  const queryClient = useQueryClient();

  // Load column visibility from localStorage
  useEffect(() => {
    const savedVisibility = localStorage.getItem(STORAGE_KEY);
    if (savedVisibility) {
      try {
        setColumnVisibility(JSON.parse(savedVisibility));
      } catch (error) {
        console.error("Failed to parse saved column visibility", error);
      }
    }
  }, []);

  // Save column visibility to localStorage
  useEffect(() => {
    if (Object.keys(columnVisibility).length > 0) {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(columnVisibility));
    }
  }, [columnVisibility]);

  const deleteTemplateMutation = useMutation({
    mutationFn: deleteTemplates,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["templates", user?.id] });
      setShowConfirmationModal(false);
    },
    onError: () => {},
  });

  const handleDeleteClick = () => {
    if (selectedTemplateIds.length === 0) {
      return;
    }
    setShowConfirmationModal(true);
  };

  const selectedTemplateIds = React.useMemo(
    () =>
      Object.keys(templateRowSelection)
        .filter(
          (id) => templateRowSelection[id as keyof typeof templateRowSelection]
        )
        .map((id) => parseInt(id, 10)), // Convert string IDs to numbers
    [templateRowSelection]
  );

  if (templateLoading || !templateData) {
    return <Spinner />;
  }

  if (templateError) {
    return <p className="text-red-500">Error loading data</p>;
  }

  return (
    <div>
      <ConfirmationModal
        showModal={showConfirmationModal}
        onClose={() => setShowConfirmationModal(false)}
        title="Delete Templates"
        description="Are you sure you want to delete these templates? This action cannot be undone."
        confirmButtonText="Delete"
        confirmButtonClass="btn-danger"
        onConfirm={() =>
          deleteTemplateMutation.mutate({ templateIds: selectedTemplateIds })
        }
      />
      <div className="flex flex-col gap-4">
        <div className="flex items-center gap-4">
          <h1 className="sub-heading-text">Templates</h1>
          <div>
            <input
              type="text"
              value={templateFilter}
              onChange={(e) => setTemplateFilter(e.target.value)}
              placeholder="Search templates..."
              className="input-field text-sm"
            />
          </div>
          {tableInstance && (
            <DropdownMenu
              open={isDropdownOpen}
              onOpenChange={(open) => setIsDropdownOpen(open)}
            >
              <DropdownMenuTrigger asChild>
                <button className="btn-outline text-sm text-neutral-700 border-neutral-400 font-normal">
                  Show/Hide Columns
                  {isDropdownOpen ? (
                    <ChevronDown size={16} />
                  ) : (
                    <ChevronUp size={16} />
                  )}
                </button>
              </DropdownMenuTrigger>

              <DropdownMenuContent
                align="start"
                className="border bg-neutral-100 border-neutral-200 shadow-md px-2"
              >
                {tableInstance
                  .getAllColumns()
                  .filter((column) => column.getCanHide())
                  .map((column) => (
                    <DropdownMenuCheckboxItem
                      key={column.id}
                      className="capitalize cursor-pointer hover:bg-neutral-200"
                      checked={columnVisibility[column.id] ?? true}
                      onCheckedChange={(value) =>
                        setColumnVisibility((prev) => ({
                          ...prev,
                          [column.id]: value,
                        }))
                      }
                    >
                      {column.id}
                    </DropdownMenuCheckboxItem>
                  ))}
              </DropdownMenuContent>
            </DropdownMenu>
          )}
          <button
            type="button"
            className={`ml-auto btn-danger text-sm`}
            onClick={handleDeleteClick}
            disabled={selectedTemplateIds.length === 0}
          >
            Delete <Trash size={16} />
          </button>
        </div>
        {templateData.length > 0 ? (
          <GeneralTable
            columns={TemplateListColumns}
            data={templateData}
            globalFilter={templateFilter}
            setGlobalFilter={setTemplateFilter}
            onTableInit={(instance) => setTableInstance(instance)}
            onRowSelectionChange={setTemplateRowSelection}
            columnVisibility={columnVisibility}
            setColumnVisibility={setColumnVisibility}
          />
        ) : (
          <div className="text-center py-16 space-y-4">
            <p className="text-lg">
              Let's get started by creating your first library question, block,
              template or collection. Click the New button to create it.
            </p>
            <p className="text-sm text-gray-500">
              Advanced users: You can also drag and drop XLSForms here and they
              will be uploaded and converted to library items.
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export { TemplateList };

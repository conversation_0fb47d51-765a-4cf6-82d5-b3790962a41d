import { Request, Response } from "express";
import {
  getAllQuestion,
  createQuestion,
  updateQuestion,
  deleteQuestion,
  duplicateQuestion,
} from "../../controllers/questionController";
import questionRepository from "../../repositories/questionRepository";
import { InputType } from "@prisma/client";

// Mock the dependencies
jest.mock("../../repositories/questionRepository");

describe("Question Controller", () => {
  let mockRequest: Partial<Request & { user?: { id: number } }>;
  let mockResponse: Partial<Response>;
  let responseObject: any = {};

  beforeEach(() => {
    // Reset mocks before each test
    jest.clearAllMocks();
    jest.resetAllMocks();

    // Setup mock response
    mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockImplementation((result) => {
        responseObject = result;
        return mockResponse;
      }),
    };

    // Reset response object
    responseObject = {};

    // Setup default authenticated user
    mockRequest = {
      user: {
        id: 1,
      },
      params: {},
      query: {},
      body: {},
    };
  });

  describe("getAllQuestion", () => {
    beforeEach(() => {
      mockRequest.params = { projectId: "1" };
    });

    it("should get all questions successfully", async () => {
      const mockQuestions = [
        {
          id: 1,
          label: "Question 1",
          inputType: "text",
          projectId: 1,
        },
        {
          id: 2,
          label: "Question 2",
          inputType: "selectone",
          projectId: 1,
        },
      ];

      (questionRepository.isPorjectOwner as jest.Mock).mockResolvedValue(true);
      (questionRepository.findAll as jest.Mock).mockResolvedValue(
        mockQuestions
      );

      await getAllQuestion(mockRequest as any, mockResponse as Response);

      expect(questionRepository.isPorjectOwner).toHaveBeenCalledWith(1, 1);
      expect(questionRepository.findAll).toHaveBeenCalledWith(1);
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(responseObject).toHaveProperty(
        "message",
        "Successfully fetched questions."
      );
      expect(responseObject).toHaveProperty("questions", mockQuestions);
    });

    it("should return 404 when user is not found", async () => {
      // No user in request
      mockRequest.user = undefined;

      await getAllQuestion(mockRequest as any, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(404);
      expect(responseObject).toHaveProperty("success", false);
      expect(responseObject).toHaveProperty("message", "user not found");
    });

    it("should return 400 when projectId is missing", async () => {
      mockRequest.params = {};
      mockRequest.query = {};

      await getAllQuestion(mockRequest as any, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(responseObject).toHaveProperty("success", false);
      expect(responseObject).toHaveProperty(
        "message",
        "Project ID is required"
      );
    });

    it("should return 400 when user is not project owner", async () => {
      (questionRepository.isPorjectOwner as jest.Mock).mockResolvedValue(false);

      await getAllQuestion(mockRequest as any, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(responseObject).toHaveProperty("success", false);
      expect(responseObject).toHaveProperty(
        "message",
        "only project owner can view the questions"
      );
    });

    it("should handle server errors", async () => {
      (questionRepository.isPorjectOwner as jest.Mock).mockRejectedValue(
        new Error("Database error")
      );

      await getAllQuestion(mockRequest as any, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(responseObject).toHaveProperty("success", false);
      expect(responseObject).toHaveProperty(
        "message",
        "error fetching questions"
      );
    });
  });

  describe("createQuestion", () => {
    beforeEach(() => {
      mockRequest.params = { projectId: "1" };
      mockRequest.body = {
        label: "New Question",
        inputType: "text",
        isRequired: true,
        position: 1,
        hint: "This is a hint",
        placeholder: "Enter text",
      };
    });

    it("should create a question successfully", async () => {
      const mockQuestion = {
        id: 1,
        label: "New Question",
        inputType: "text",
        isRequired: true,
        position: 1,
        hint: "This is a hint",
        placeholder: "Enter text",
        projectId: 1,
      };

      (questionRepository.isPorjectOwner as jest.Mock).mockResolvedValue(true);
      (questionRepository.create as jest.Mock).mockResolvedValue(mockQuestion);

      await createQuestion(mockRequest as any, mockResponse as Response);

      expect(questionRepository.isPorjectOwner).toHaveBeenCalledWith(1, 1);
      expect(questionRepository.create).toHaveBeenCalledWith(
        expect.objectContaining({
          label: "New Question",
          inputType: "text",
          isRequired: true,
          position: 1,
          projectId: 1,
        })
      );
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(responseObject).toHaveProperty("data.question", mockQuestion);
    });

    it("should create a question with options successfully", async () => {
      mockRequest.body = {
        label: "Question with Options",
        inputType: "selectone",
        isRequired: true,
        position: 2,
        questionOptions: [
          { label: "Option 1", code: "OPT1" },
          { label: "Option 2", code: "OPT2", nextQuestionId: 3 },
        ],
      };

      const mockQuestion = {
        id: 1,
        label: "Question with Options",
        inputType: "selectone",
        isRequired: true,
        position: 2,
        projectId: 1,
        questionOptions: [
          { id: 1, label: "Option 1", code: "OPT1" },
          { id: 2, label: "Option 2", code: "OPT2", nextQuestionId: 3 },
        ],
      };

      (questionRepository.isPorjectOwner as jest.Mock).mockResolvedValue(true);
      (questionRepository.create as jest.Mock).mockResolvedValue(mockQuestion);

      await createQuestion(mockRequest as any, mockResponse as Response);

      expect(questionRepository.create).toHaveBeenCalledWith(
        expect.objectContaining({
          label: "Question with Options",
          inputType: "selectone",
          projectId: 1,
          questionOptions: [
            { label: "Option 1", code: "OPT1" },
            { label: "Option 2", code: "OPT2", nextQuestionId: 3 },
          ],
        })
      );
      expect(mockResponse.status).toHaveBeenCalledWith(200);
    });

    it("should return 403 when user is not project owner", async () => {
      (questionRepository.isPorjectOwner as jest.Mock).mockResolvedValue(false);

      await createQuestion(mockRequest as any, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(403);
      expect(responseObject).toHaveProperty("success", false);
      expect(responseObject).toHaveProperty(
        "message",
        "User is not associated with the project"
      );
    });

    it("should return 403 for invalid input due to unauthorized user", async () => {
      // Missing required fields but fails due to unauthorized user
      mockRequest.body = { label: "Invalid Question" };
      (questionRepository.isPorjectOwner as jest.Mock).mockResolvedValue(false);

      await createQuestion(mockRequest as any, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(403);
      expect(responseObject).toHaveProperty("success", false);
      expect(responseObject).toHaveProperty(
        "message",
        "User is not associated with the project"
      );
      expect(questionRepository.create).not.toHaveBeenCalled();
    });

    it("should handle server errors", async () => {
      (questionRepository.isPorjectOwner as jest.Mock).mockResolvedValue(true);
      (questionRepository.create as jest.Mock).mockRejectedValue(
        new Error("Database error")
      );

      await createQuestion(mockRequest as any, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(responseObject).toHaveProperty("success", false);
      expect(responseObject).toHaveProperty(
        "message",
        "error creating qustion"
      );
    });
  });

  describe("updateQuestion", () => {
    beforeEach(() => {
      mockRequest.params = { id: "1" };
      mockRequest.body = {
        label: "Updated Question",
        hint: "Updated hint",
      };
    });

    it("should update a question successfully", async () => {
      const existingQuestion = {
        id: 1,
        label: "Original Question",
        inputType: "text",
        position: 1,
        projectId: 1,
      };

      const updatedQuestion = {
        ...existingQuestion,
        label: "Updated Question",
        hint: "Updated hint",
      };

      (questionRepository.findById as jest.Mock).mockResolvedValue(
        existingQuestion
      );
      (questionRepository.isPorjectOwner as jest.Mock).mockResolvedValue(true);
      (questionRepository.updateById as jest.Mock).mockResolvedValue(
        updatedQuestion
      );

      await updateQuestion(mockRequest as any, mockResponse as Response);

      expect(questionRepository.findById).toHaveBeenCalledWith(1);
      expect(questionRepository.isPorjectOwner).toHaveBeenCalledWith(1, 1);
      expect(questionRepository.updateById).toHaveBeenCalled();
      expect(mockResponse.status).toHaveBeenCalledWith(200);
    });

    it("should return 404 when question not found", async () => {
      (questionRepository.findById as jest.Mock).mockResolvedValue(null);

      await updateQuestion(mockRequest as any, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(404);
      expect(responseObject).toHaveProperty("success", false);
      expect(responseObject).toHaveProperty("message", "Question not found");
      expect(questionRepository.updateById).not.toHaveBeenCalled();
    });

    it("should return 403 when user is not project owner", async () => {
      const existingQuestion = {
        id: 1,
        label: "Original Question",
        inputType: "text",
        position: 1,
        projectId: 1,
      };

      (questionRepository.findById as jest.Mock).mockResolvedValue(
        existingQuestion
      );
      (questionRepository.isPorjectOwner as jest.Mock).mockResolvedValue(false);

      await updateQuestion(mockRequest as any, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(403);
      expect(responseObject).toHaveProperty("success", false);
      expect(responseObject).toHaveProperty(
        "message",
        "You are not the project owner"
      );
      expect(questionRepository.updateById).not.toHaveBeenCalled();
    });

    it("should handle invalid question ID", async () => {
      mockRequest.params = { id: "invalid" };

      await updateQuestion(mockRequest as any, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(responseObject).toHaveProperty("success", false);
      expect(responseObject).toHaveProperty("message", "Invalid request");
    });

    it("should handle server errors", async () => {
      (questionRepository.findById as jest.Mock).mockRejectedValue(
        new Error("Database error")
      );

      await updateQuestion(mockRequest as any, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(responseObject).toHaveProperty("success", false);
    });
  });

  describe("deleteQuestion", () => {
    beforeEach(() => {
      mockRequest.params = { id: "1" };
    });

    it("should delete a question successfully", async () => {
      const existingQuestion = {
        id: 1,
        label: "Question to Delete",
        projectId: 1,
      };

      (questionRepository.findById as jest.Mock).mockResolvedValue(
        existingQuestion
      );
      (questionRepository.isPorjectOwner as jest.Mock).mockResolvedValue(true);
      (questionRepository.deleteQuestion as jest.Mock).mockResolvedValue({
        id: 1,
      });

      await deleteQuestion(mockRequest as any, mockResponse as Response);

      expect(questionRepository.findById).toHaveBeenCalledWith(1);
      expect(questionRepository.isPorjectOwner).toHaveBeenCalledWith(1, 1);
      expect(questionRepository.deleteQuestion).toHaveBeenCalledWith(1);
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(responseObject).toHaveProperty(
        "message",
        "Successfully deleted question"
      );
    });

    it("should return 404 when question not found", async () => {
      (questionRepository.findById as jest.Mock).mockResolvedValue(null);

      await deleteQuestion(mockRequest as any, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(404);
      expect(responseObject).toHaveProperty("success", false);
      expect(responseObject).toHaveProperty("message", "Question not found");
      expect(questionRepository.deleteQuestion).not.toHaveBeenCalled();
    });

    it("should return 403 when user is not project owner", async () => {
      const existingQuestion = {
        id: 1,
        label: "Question to Delete",
        projectId: 1,
      };

      (questionRepository.findById as jest.Mock).mockResolvedValue(
        existingQuestion
      );
      (questionRepository.isPorjectOwner as jest.Mock).mockResolvedValue(false);

      await deleteQuestion(mockRequest as any, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(403);
      expect(responseObject).toHaveProperty("succcess", false); // Match controller typo
      expect(responseObject).toHaveProperty(
        "message",
        "Current user cannot delete question from this project"
      );
      expect(questionRepository.deleteQuestion).not.toHaveBeenCalled();
    });

    it("should handle invalid question ID", async () => {
      mockRequest.params = { id: "invalid" };

      await deleteQuestion(mockRequest as any, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(responseObject).toHaveProperty("success", false);
      expect(responseObject).toHaveProperty(
        "message",
        "Invalid request: User ID or Question ID is missing"
      );
    });

    it("should handle server errors", async () => {
      (questionRepository.findById as jest.Mock).mockRejectedValue(
        new Error("Database error")
      );

      await deleteQuestion(mockRequest as any, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(responseObject).toHaveProperty("success", false);
      expect(responseObject).toHaveProperty("message", "Database error");
    });
  });

  describe("duplicateQuestion", () => {
    beforeEach(() => {
      mockRequest.params = { id: "1" };
    });

    it("should duplicate a question successfully", async () => {
      const existingQuestion = {
        id: 1,
        label: "Original Question",
        inputType: "text",
        position: 1,
        projectId: 1,
      };

      const duplicatedQuestion = {
        id: 2,
        label: "Original Question (Copy)",
        inputType: "text",
        position: 2,
        projectId: 1,
      };

      (questionRepository.findById as jest.Mock).mockResolvedValue(
        existingQuestion
      );
      (questionRepository.isPorjectOwner as jest.Mock).mockResolvedValue(true);
      (questionRepository.duplicateQuestion as jest.Mock).mockResolvedValue(
        duplicatedQuestion
      );

      await duplicateQuestion(mockRequest as any, mockResponse as Response);

      expect(questionRepository.findById).toHaveBeenCalledWith(1);
      expect(questionRepository.isPorjectOwner).toHaveBeenCalledWith(1, 1);
      expect(questionRepository.duplicateQuestion).toHaveBeenCalledWith(1, 1);
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(responseObject).toHaveProperty(
        "message",
        "Successfully duplicated the question."
      );
      expect(responseObject).toHaveProperty(
        "duplicatedQuestion",
        duplicatedQuestion
      );
    });

    it("should return 404 when question not found", async () => {
      (questionRepository.findById as jest.Mock).mockResolvedValue(null);

      await duplicateQuestion(mockRequest as any, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(404);
      expect(responseObject).toHaveProperty("success", false);
      expect(responseObject).toHaveProperty("message", "Question not found");
      expect(questionRepository.duplicateQuestion).not.toHaveBeenCalled();
    });

    it("should return 403 when user is not project owner", async () => {
      const existingQuestion = {
        id: 1,
        label: "Original Question",
        projectId: 1,
      };

      (questionRepository.findById as jest.Mock).mockResolvedValue(
        existingQuestion
      );
      (questionRepository.isPorjectOwner as jest.Mock).mockResolvedValue(false);

      await duplicateQuestion(mockRequest as any, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(403);
      expect(responseObject).toHaveProperty("succcess", false); // Match controller typo
      expect(responseObject).toHaveProperty(
        "message",
        "Current user cannot delete question from this project"
      );
      expect(questionRepository.duplicateQuestion).not.toHaveBeenCalled();
    });

    it("should handle invalid question ID", async () => {
      mockRequest.params = { id: "invalid" };

      await duplicateQuestion(mockRequest as any, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(responseObject).toHaveProperty("success", false);
      expect(responseObject).toHaveProperty(
        "message",
        "Invalid request: User ID or Question ID is missing"
      );
    });

    it("should handle server errors", async () => {
      (questionRepository.findById as jest.Mock).mockRejectedValue(
        new Error("Database error")
      );

      await duplicateQuestion(mockRequest as any, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(responseObject).toHaveProperty("success", false);
      expect(responseObject).toHaveProperty("message", "Database error");
    });
  });
});
